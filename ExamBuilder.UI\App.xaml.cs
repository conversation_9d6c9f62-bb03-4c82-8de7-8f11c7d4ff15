using System;
using System.Globalization;
using System.Threading;
using System.Windows;
using System.Windows.Markup;
using System.Windows.Media;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Configuration;
using Serilog;
using ExamBuilder.Core.Services;
using ExamBuilder.Data.Context;
using ExamBuilder.Security.Services;
using ExamBuilder.PDF.Services;
using ExamBuilder.Telegram.Services;
using ExamBuilder.UI.ViewModels;
using ExamBuilder.UI.Views;

namespace ExamBuilder.UI
{
    /// <summary>
    /// تطبيق إنشاء الامتحانات - المهندس يوسف غنام
    /// </summary>
    public partial class App : Application
    {
        private IHost? _host;

        public static IServiceProvider ServiceProvider { get; private set; } = null!;

        protected override void OnStartup(StartupEventArgs e)
        {
            // إعداد الثقافة العربية
            SetupArabicCulture();

            // إعداد التسجيل
            SetupLogging();

            // إعداد خدمات التطبيق
            _host = CreateHostBuilder().Build();
            ServiceProvider = _host.Services;

            // بدء الخدمات
            _host.Start();

            // إعداد معالج الأخطاء العامة
            SetupGlobalExceptionHandling();

            Log.Information("تم بدء تشغيل التطبيق بنجاح");

            base.OnStartup(e);
        }

        protected override void OnExit(ExitEventArgs e)
        {
            Log.Information("إغلاق التطبيق");
            _host?.Dispose();
            Log.CloseAndFlush();
            base.OnExit(e);
        }

        private static IHostBuilder CreateHostBuilder()
        {
            return Host.CreateDefaultBuilder()
                .ConfigureAppConfiguration((context, config) =>
                {
                    config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
                })
                .ConfigureServices((context, services) =>
                {
                    // إعداد قاعدة البيانات
                    services.AddDbContext<ExamBuilderContext>();

                    // خدمات الأعمال
                    services.AddScoped<IExamService, ExamServiceSimple>();
                    services.AddScoped<IExamRepository, ExamRepository>();
                    services.AddScoped<IQuestionRepository, QuestionRepository>();

                    // خدمة التليجرام (محاكاة)
                    services.AddScoped<ExamBuilder.Telegram.Services.ITelegramAuthService, ExamBuilder.Telegram.Services.MockTelegramAuthService>();
                    services.AddScoped<IQuestionService, QuestionService>();
                    services.AddScoped<IUserService, UserService>();

                    // خدمات الأمان
                    services.AddScoped<IAuthenticationService, AuthenticationService>();
                    services.AddScoped<IDeviceAuthorizationService, DeviceAuthorizationService>();
                    services.AddScoped<IEncryptionService, EncryptionService>();

                    // خدمات PDF
                    services.AddScoped<IPdfGeneratorService, PdfGeneratorService>();
                    services.AddScoped<IPdfTemplateService, PdfTemplateService>();

                    // خدمات Telegram
                    services.AddScoped<ITelegramBotService, TelegramBotService>();
                    services.AddScoped<ITelegramCommandService, TelegramCommandService>();

                    // ViewModels
                    services.AddTransient<LoginViewModel>();
                    services.AddTransient<MainViewModel>();

                    // Views
                    services.AddTransient<LoginWindow>();
                    services.AddTransient<MainWindow>();
                })
                .UseSerilog();
        }

        private static void SetupArabicCulture()
        {
            // إعداد الثقافة العربية
            var arabicCulture = new CultureInfo("ar-SA");
            Thread.CurrentThread.CurrentCulture = arabicCulture;
            Thread.CurrentThread.CurrentUICulture = arabicCulture;

            // إعداد اتجاه النص من اليمين إلى اليسار
            FrameworkElement.LanguageProperty.OverrideMetadata(
                typeof(FrameworkElement),
                new FrameworkPropertyMetadata(XmlLanguage.GetLanguage(arabicCulture.IetfLanguageTag)));
        }

        private static void SetupLogging()
        {
            Log.Logger = new LoggerConfiguration()
                .MinimumLevel.Information()
                .WriteTo.File("logs/exam-builder-.txt",
                    rollingInterval: RollingInterval.Day,
                    retainedFileCountLimit: 30,
                    outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}")
                .CreateLogger();
        }

        private void SetupGlobalExceptionHandling()
        {
            // معالجة الأخطاء غير المعالجة في UI Thread
            DispatcherUnhandledException += (sender, e) =>
            {
                Log.Error(e.Exception, "خطأ غير معالج في واجهة المستخدم");
                ShowErrorMessage("حدث خطأ غير متوقع في التطبيق", e.Exception.Message);
                e.Handled = true;
            };

            // معالجة الأخطاء غير المعالجة في Background Threads
            AppDomain.CurrentDomain.UnhandledException += (sender, e) =>
            {
                Log.Fatal((Exception)e.ExceptionObject, "خطأ فادح غير معالج");
                if (e.IsTerminating)
                {
                    ShowErrorMessage("خطأ فادح", "سيتم إغلاق التطبيق بسبب خطأ فادح");
                }
            };
        }

        private static void ShowErrorMessage(string title, string message)
        {
            MessageBox.Show(
                message,
                title,
                MessageBoxButton.OK,
                MessageBoxImage.Error,
                MessageBoxResult.OK,
                MessageBoxOptions.RtlReading | MessageBoxOptions.RightAlign);
        }

        /// <summary>
        /// الحصول على خدمة من حاوي الحقن
        /// </summary>
        public static T GetService<T>() where T : class
        {
            return ServiceProvider.GetRequiredService<T>();
        }

        /// <summary>
        /// تطبيق ثيم جديد
        /// </summary>
        public static void ApplyTheme(string themeName)
        {
            try
            {
                // تطبيق الألوان مباشرة بدلاً من ملفات منفصلة
                var resources = Current.Resources;

                switch (themeName.ToLower())
                {
                    case "red":
                        resources["PrimaryBrush"] = new SolidColorBrush(Color.FromRgb(211, 47, 47));
                        resources["SecondaryBrush"] = new SolidColorBrush(Color.FromRgb(244, 67, 54));
                        resources["AccentBrush"] = new SolidColorBrush(Color.FromRgb(255, 235, 238));
                        resources["InfoBrush"] = new SolidColorBrush(Color.FromRgb(255, 152, 0));
                        break;

                    case "green":
                        resources["PrimaryBrush"] = new SolidColorBrush(Color.FromRgb(46, 139, 87));
                        resources["SecondaryBrush"] = new SolidColorBrush(Color.FromRgb(76, 175, 80));
                        resources["AccentBrush"] = new SolidColorBrush(Color.FromRgb(232, 245, 233));
                        resources["InfoBrush"] = new SolidColorBrush(Color.FromRgb(0, 150, 136));
                        break;

                    case "dark":
                        resources["PrimaryBrush"] = new SolidColorBrush(Color.FromRgb(44, 44, 44));
                        resources["SecondaryBrush"] = new SolidColorBrush(Color.FromRgb(66, 66, 66));
                        resources["AccentBrush"] = new SolidColorBrush(Color.FromRgb(33, 33, 33));
                        resources["InfoBrush"] = new SolidColorBrush(Color.FromRgb(100, 181, 246));
                        break;
                }

                Log.Information($"تم تطبيق الثيم: {themeName}");
            }
            catch (Exception ex)
            {
                Log.Error(ex, $"فشل في تطبيق الثيم: {themeName}");
            }
        }
    }
}
