# دليل البدء السريع - برنامج إنشاء الامتحانات

## 🚀 التشغيل السريع

### الطريقة الأولى: استخدام ملف التشغيل
```bash
# تشغيل ملف .bat
run.bat

# أو تشغيل ملف PowerShell
.\run.ps1
```

### الطريقة الثانية: الأوامر اليدوية
```bash
# استعادة الحزم
dotnet restore

# بناء المشروع
dotnet build --configuration Release

# تشغيل التطبيق
dotnet run --project ExamBuilder.UI --configuration Release
```

## 🔑 بيانات تسجيل الدخول

- **اسم المستخدم**: `جو`
- **كلمة المرور**: `جو`

## 📋 الخطوات الأولى

### 1. تسجيل الدخول
- افتح التطبيق
- أدخل بيانات تسجيل الدخول
- اختر الثيم المفضل (أحمر، أخضر، داكن)

### 2. إنشاء امتحان جديد
- انقر على زر "جديد" في قائمة الامتحانات
- أدخل تفاصيل الامتحان (العنوان، المادة، إلخ)
- احفظ الامتحان

### 3. إضافة الأسئلة
- اختر الامتحان من القائمة
- انقر على "إضافة سؤال"
- أدخل نص السؤال ونوعه
- أضف الخيارات (للأسئلة متعددة الخيارات)
- احفظ السؤال

### 4. تصدير الامتحان
- اختر الامتحان المطلوب
- انقر على زر "تصدير"
- اختر مكان الحفظ
- سيتم إنشاء ملف PDF

## 🎨 الثيمات المتاحة

### الثيم الأحمر (افتراضي)
- لون أساسي: أحمر (#E94560)
- مناسب للاستخدام العام

### الثيم الأخضر
- لون أساسي: أخضر (#2E8B57)
- مريح للعين

### الثيم الداكن
- لون أساسي: بنفسجي (#6366F1)
- مناسب للاستخدام الليلي

## ⚙️ الإعدادات المهمة

### إعدادات Telegram (اختيارية)
```json
{
  "Telegram": {
    "BotToken": "YOUR_BOT_TOKEN",
    "ChatId": "YOUR_CHAT_ID",
    "EnableRemoteControl": true
  }
}
```

### إعدادات PDF
```json
{
  "PDF": {
    "DefaultTemplate": "Standard",
    "OutputDirectory": "Generated_PDFs",
    "CompressionLevel": "Medium"
  }
}
```

## 🔧 استكشاف الأخطاء

### مشكلة: فشل في بناء المشروع
**الحل**: تأكد من تثبيت .NET 8.0 SDK
```bash
dotnet --version
```

### مشكلة: خطأ في تسجيل الدخول
**الحل**: 
- تأكد من صحة بيانات تسجيل الدخول
- تحقق من تصريح الجهاز

### مشكلة: فشل في إنشاء PDF
**الحل**:
- تأكد من وجود مساحة كافية على القرص
- تحقق من صلاحيات الكتابة

## 📁 هيكل الملفات

```
ExamBuilder/
├── ExamBuilder.UI/           # واجهة المستخدم
├── ExamBuilder.Core/         # منطق العمل
├── ExamBuilder.Data/         # قاعدة البيانات
├── ExamBuilder.Security/     # الأمان
├── ExamBuilder.PDF/          # إنشاء PDF
├── ExamBuilder.Telegram/     # تكامل Telegram
├── Generated_PDFs/           # ملفات PDF المُنشأة
├── Logs/                     # ملفات السجلات
└── Backups/                  # النسخ الاحتياطية
```

## 🔐 الأمان

### تصريح الأجهزة
- الأجهزة الجديدة تحتاج موافقة
- يمكن إدارة الأجهزة عبر Telegram
- الأجهزة المحظورة لا يمكنها الوصول

### تشفير البيانات
- تشفير AES-256 للبيانات الحساسة
- تشفير BCrypt لكلمات المرور
- تشفير SHA-256 للتحقق من التكامل

## 📞 الدعم والمساعدة

### معلومات المطور
- **الاسم**: المهندس يوسف غنام
- **البريد الإلكتروني**: [البريد الإلكتروني]
- **WhatsApp**: [رقم WhatsApp]

### الحصول على المساعدة
1. راجع ملف README.md للتفاصيل الكاملة
2. تحقق من ملفات السجلات في مجلد Logs/
3. تواصل مع المطور للدعم التقني

## 🆕 الميزات الجديدة في الإصدار 2.0

- ✅ واجهة مستخدم محسنة بـ WPF
- ✅ أداء أفضل وسرعة استجابة عالية
- ✅ دعم محسن للغة العربية
- ✅ ثيمات متعددة قابلة للتخصيص
- ✅ نظام أمان متقدم
- ✅ تكامل محسن مع Telegram
- ✅ إنشاء PDF محسن
- ✅ نظام إدارة الأجهزة

## 📈 التحديثات المستقبلية

- 🔄 نظام النسخ الاحتياطي التلقائي
- 🔄 إحصائيات متقدمة
- 🔄 قوالب امتحانات جاهزة
- 🔄 تصدير بصيغ متعددة
- 🔄 نظام المستخدمين المتعددين

---

**ملاحظة**: هذا دليل مبسط للبدء السريع. للحصول على التفاصيل الكاملة، يرجى مراجعة ملف README.md
