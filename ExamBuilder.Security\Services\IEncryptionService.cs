using System.Security.Cryptography;
using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace ExamBuilder.Security.Services
{
    /// <summary>
    /// واجهة خدمة التشفير
    /// </summary>
    public interface IEncryptionService
    {
        string Encrypt(string plainText);
        string Decrypt(string cipherText);
        string HashData(string data);
        bool VerifyHash(string data, string hash);
        string GenerateSecureToken(int length = 32);
        byte[] GenerateSecureBytes(int length);
    }

    /// <summary>
    /// تنفيذ خدمة التشفير باستخدام AES
    /// </summary>
    public class EncryptionService : IEncryptionService
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<EncryptionService> _logger;
        private readonly byte[] _key;
        private readonly byte[] _iv;

        public EncryptionService(IConfiguration configuration, ILogger<EncryptionService> logger)
        {
            _configuration = configuration;
            _logger = logger;

            // الحصول على المفتاح من الإعدادات أو إنشاء مفتاح افتراضي
            var keyString = _configuration["Security:EncryptionKey"] ?? "my_super_secret_key_for_exam_builder_2024";
            var ivString = _configuration["Security:EncryptionIV"] ?? "custom_salt_for_password_hashing_iv";

            _key = DeriveKeyFromString(keyString, 32); // AES-256
            _iv = DeriveKeyFromString(ivString, 16);   // AES block size
        }

        public string Encrypt(string plainText)
        {
            try
            {
                if (string.IsNullOrEmpty(plainText))
                {
                    throw new ArgumentException("النص المراد تشفيره لا يمكن أن يكون فارغاً");
                }

                using var aes = Aes.Create();
                aes.Key = _key;
                aes.IV = _iv;
                aes.Mode = CipherMode.CBC;
                aes.Padding = PaddingMode.PKCS7;

                using var encryptor = aes.CreateEncryptor();
                using var msEncrypt = new MemoryStream();
                using var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write);
                using var swEncrypt = new StreamWriter(csEncrypt);

                swEncrypt.Write(plainText);
                swEncrypt.Close();

                var encrypted = msEncrypt.ToArray();
                return Convert.ToBase64String(encrypted);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تشفير البيانات");
                throw new CryptographicException("فشل في تشفير البيانات", ex);
            }
        }

        public string Decrypt(string cipherText)
        {
            try
            {
                if (string.IsNullOrEmpty(cipherText))
                {
                    throw new ArgumentException("النص المراد فك تشفيره لا يمكن أن يكون فارغاً");
                }

                var cipherBytes = Convert.FromBase64String(cipherText);

                using var aes = Aes.Create();
                aes.Key = _key;
                aes.IV = _iv;
                aes.Mode = CipherMode.CBC;
                aes.Padding = PaddingMode.PKCS7;

                using var decryptor = aes.CreateDecryptor();
                using var msDecrypt = new MemoryStream(cipherBytes);
                using var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read);
                using var srDecrypt = new StreamReader(csDecrypt);

                return srDecrypt.ReadToEnd();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فك تشفير البيانات");
                throw new CryptographicException("فشل في فك تشفير البيانات", ex);
            }
        }

        public string HashData(string data)
        {
            try
            {
                if (string.IsNullOrEmpty(data))
                {
                    throw new ArgumentException("البيانات المراد تشفيرها لا يمكن أن تكون فارغة");
                }

                using var sha256 = SHA256.Create();
                var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(data));
                return Convert.ToBase64String(hashedBytes);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تشفير البيانات بـ Hash");
                throw;
            }
        }

        public bool VerifyHash(string data, string hash)
        {
            try
            {
                var computedHash = HashData(data);
                return computedHash.Equals(hash, StringComparison.Ordinal);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحقق من Hash");
                return false;
            }
        }

        public string GenerateSecureToken(int length = 32)
        {
            try
            {
                var bytes = GenerateSecureBytes(length);
                return Convert.ToBase64String(bytes);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء رمز آمن");
                throw;
            }
        }

        public byte[] GenerateSecureBytes(int length)
        {
            try
            {
                if (length <= 0)
                {
                    throw new ArgumentException("طول البايتات يجب أن يكون أكبر من صفر");
                }

                return RandomNumberGenerator.GetBytes(length);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء بايتات آمنة");
                throw;
            }
        }

        private static byte[] DeriveKeyFromString(string input, int length)
        {
            using var sha256 = SHA256.Create();
            var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(input));
            
            if (hash.Length >= length)
            {
                var result = new byte[length];
                Array.Copy(hash, result, length);
                return result;
            }
            else
            {
                // إذا كان الهاش أقصر من المطلوب، نكرر العملية
                var result = new byte[length];
                var currentInput = input;
                var offset = 0;
                
                while (offset < length)
                {
                    var currentHash = sha256.ComputeHash(Encoding.UTF8.GetBytes(currentInput));
                    var bytesToCopy = Math.Min(currentHash.Length, length - offset);
                    Array.Copy(currentHash, 0, result, offset, bytesToCopy);
                    offset += bytesToCopy;
                    currentInput += "_iteration";
                }
                
                return result;
            }
        }
    }

    /// <summary>
    /// خدمة تشفير الملفات
    /// </summary>
    public interface IFileEncryptionService
    {
        Task EncryptFileAsync(string inputFilePath, string outputFilePath);
        Task DecryptFileAsync(string inputFilePath, string outputFilePath);
        Task<bool> IsFileEncryptedAsync(string filePath);
    }

    public class FileEncryptionService : IFileEncryptionService
    {
        private readonly IEncryptionService _encryptionService;
        private readonly ILogger<FileEncryptionService> _logger;

        public FileEncryptionService(IEncryptionService encryptionService, ILogger<FileEncryptionService> logger)
        {
            _encryptionService = encryptionService;
            _logger = logger;
        }

        public async Task EncryptFileAsync(string inputFilePath, string outputFilePath)
        {
            try
            {
                if (!File.Exists(inputFilePath))
                {
                    throw new FileNotFoundException($"الملف غير موجود: {inputFilePath}");
                }

                var fileContent = await File.ReadAllTextAsync(inputFilePath);
                var encryptedContent = _encryptionService.Encrypt(fileContent);
                
                await File.WriteAllTextAsync(outputFilePath, encryptedContent);
                
                _logger.LogInformation($"تم تشفير الملف: {inputFilePath} -> {outputFilePath}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"خطأ في تشفير الملف: {inputFilePath}");
                throw;
            }
        }

        public async Task DecryptFileAsync(string inputFilePath, string outputFilePath)
        {
            try
            {
                if (!File.Exists(inputFilePath))
                {
                    throw new FileNotFoundException($"الملف غير موجود: {inputFilePath}");
                }

                var encryptedContent = await File.ReadAllTextAsync(inputFilePath);
                var decryptedContent = _encryptionService.Decrypt(encryptedContent);
                
                await File.WriteAllTextAsync(outputFilePath, decryptedContent);
                
                _logger.LogInformation($"تم فك تشفير الملف: {inputFilePath} -> {outputFilePath}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"خطأ في فك تشفير الملف: {inputFilePath}");
                throw;
            }
        }

        public async Task<bool> IsFileEncryptedAsync(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    return false;
                }

                var content = await File.ReadAllTextAsync(filePath);
                
                // محاولة فك التشفير للتحقق من كون الملف مشفراً
                try
                {
                    _encryptionService.Decrypt(content);
                    return true;
                }
                catch
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"خطأ في التحقق من تشفير الملف: {filePath}");
                return false;
            }
        }
    }
}
