2025-05-25 12:52:42.709 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-05-25 12:52:42.745 +03:00 [INF] Hosting environment: Production
2025-05-25 12:52:42.746 +03:00 [INF] Content root path: D:\mr\qqqq\ExamBuilder.UI\bin\Release\net8.0-windows
2025-05-25 12:52:42.747 +03:00 [INF] تم بدء تشغيل التطبيق بنجاح
2025-05-25 12:52:43.290 +03:00 [INF] تم تحميل نافذة تسجيل الدخول
2025-05-25 12:52:47.696 +03:00 [ERR] خطأ في عملية تسجيل الدخول
System.Windows.Markup.XamlParseException: Provide value on 'System.Windows.Baml2006.TypeConverterMarkupExtension' threw an exception.
 ---> System.FormatException: Settings is not a valid value for PackIconMaterialKind.
 ---> System.ArgumentException: Requested value 'Settings' was not found.
   at System.Enum.TryParseByName[TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TStorage& result)
   at System.Enum.TryParseByValueOrName[TUnderlying,TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TUnderlying& result)
   at System.Enum.TryParse(Type enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, Object& result)
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   --- End of inner exception stack trace ---
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   at MS.Internal.Xaml.Runtime.ClrObjectRuntime.CallProvideValue(MarkupExtension me, IServiceProvider serviceProvider)
   --- End of inner exception stack trace ---
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.LoadBaml(XamlReader xamlReader, Boolean skipJournaledProperties, Object rootObject, XamlAccessLevel accessLevel, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at ExamBuilder.UI.Views.MainWindow.InitializeComponent() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml:line 1
   at ExamBuilder.UI.Views.MainWindow..ctor() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml.cs:line 18
   at System.RuntimeMethodHandle.InvokeMethod(Object target, Void** arguments, Signature sig, Boolean isConstructor)
   at System.Reflection.MethodBaseInvoker.InvokeWithNoArgs(Object obj, BindingFlags invokeAttr)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitDisposeCache(ServiceCallSite transientCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at ExamBuilder.UI.App.GetService[T]() in D:\mr\qqqq\ExamBuilder.UI\App.xaml.cs:line 165
   at ExamBuilder.UI.Views.LoginWindow.PerformLogin() in D:\mr\qqqq\ExamBuilder.UI\Views\LoginWindow.xaml.cs:line 86
2025-05-25 12:52:50.532 +03:00 [ERR] خطأ في عملية تسجيل الدخول
System.Windows.Markup.XamlParseException: Provide value on 'System.Windows.Baml2006.TypeConverterMarkupExtension' threw an exception.
 ---> System.FormatException: Settings is not a valid value for PackIconMaterialKind.
 ---> System.ArgumentException: Requested value 'Settings' was not found.
   at System.Enum.TryParseByName[TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TStorage& result)
   at System.Enum.TryParseByValueOrName[TUnderlying,TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TUnderlying& result)
   at System.Enum.TryParse(Type enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, Object& result)
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   --- End of inner exception stack trace ---
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   at MS.Internal.Xaml.Runtime.ClrObjectRuntime.CallProvideValue(MarkupExtension me, IServiceProvider serviceProvider)
   --- End of inner exception stack trace ---
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.LoadBaml(XamlReader xamlReader, Boolean skipJournaledProperties, Object rootObject, XamlAccessLevel accessLevel, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at ExamBuilder.UI.Views.MainWindow.InitializeComponent() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml:line 1
   at ExamBuilder.UI.Views.MainWindow..ctor() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml.cs:line 18
   at InvokeStub_MainWindow..ctor(Object, Object, IntPtr*)
   at System.Reflection.MethodBaseInvoker.InvokeWithNoArgs(Object obj, BindingFlags invokeAttr)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitDisposeCache(ServiceCallSite transientCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at ExamBuilder.UI.App.GetService[T]() in D:\mr\qqqq\ExamBuilder.UI\App.xaml.cs:line 165
   at ExamBuilder.UI.Views.LoginWindow.PerformLogin() in D:\mr\qqqq\ExamBuilder.UI\Views\LoginWindow.xaml.cs:line 86
2025-05-25 12:52:57.762 +03:00 [INF] تم تطبيق الثيم: Dark
2025-05-25 12:52:57.762 +03:00 [INF] تم تطبيق الثيم الداكن
2025-05-25 12:53:00.288 +03:00 [INF] تم تطبيق الثيم: Dark
2025-05-25 12:53:00.288 +03:00 [INF] تم تطبيق الثيم الداكن
2025-05-25 12:53:00.504 +03:00 [INF] تم تطبيق الثيم: Dark
2025-05-25 12:53:00.505 +03:00 [INF] تم تطبيق الثيم الداكن
2025-05-25 12:53:01.518 +03:00 [INF] تم تطبيق الثيم: Green
2025-05-25 12:53:01.518 +03:00 [INF] تم تطبيق الثيم الأخضر
2025-05-25 12:53:26.504 +03:00 [ERR] خطأ في عملية تسجيل الدخول
System.Windows.Markup.XamlParseException: Provide value on 'System.Windows.Baml2006.TypeConverterMarkupExtension' threw an exception.
 ---> System.FormatException: Settings is not a valid value for PackIconMaterialKind.
 ---> System.ArgumentException: Requested value 'Settings' was not found.
   at System.Enum.TryParseByName[TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TStorage& result)
   at System.Enum.TryParseByValueOrName[TUnderlying,TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TUnderlying& result)
   at System.Enum.TryParse(Type enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, Object& result)
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   --- End of inner exception stack trace ---
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   at MS.Internal.Xaml.Runtime.ClrObjectRuntime.CallProvideValue(MarkupExtension me, IServiceProvider serviceProvider)
   --- End of inner exception stack trace ---
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.LoadBaml(XamlReader xamlReader, Boolean skipJournaledProperties, Object rootObject, XamlAccessLevel accessLevel, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at ExamBuilder.UI.Views.MainWindow.InitializeComponent() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml:line 1
   at ExamBuilder.UI.Views.MainWindow..ctor() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml.cs:line 18
   at InvokeStub_MainWindow..ctor(Object, Object, IntPtr*)
   at System.Reflection.MethodBaseInvoker.InvokeWithNoArgs(Object obj, BindingFlags invokeAttr)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitDisposeCache(ServiceCallSite transientCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at ExamBuilder.UI.App.GetService[T]() in D:\mr\qqqq\ExamBuilder.UI\App.xaml.cs:line 165
   at ExamBuilder.UI.Views.LoginWindow.PerformLogin() in D:\mr\qqqq\ExamBuilder.UI\Views\LoginWindow.xaml.cs:line 86
2025-05-25 12:57:28.661 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-05-25 12:57:28.691 +03:00 [INF] Hosting environment: Production
2025-05-25 12:57:28.692 +03:00 [INF] Content root path: D:\mr\qqqq\ExamBuilder.UI\bin\Release\net8.0-windows
2025-05-25 12:57:28.693 +03:00 [INF] تم بدء تشغيل التطبيق بنجاح
2025-05-25 12:57:28.961 +03:00 [INF] تم تحميل نافذة تسجيل الدخول
2025-05-25 12:57:32.953 +03:00 [INF] محاولة تسجيل دخول - المستخدم: 'جو', كلمة المرور: 'جو'
2025-05-25 12:57:32.953 +03:00 [INF] تم التحقق من البيانات بنجاح
2025-05-25 12:57:33.953 +03:00 [INF] محاولة فتح النافذة الرئيسية
2025-05-25 12:57:33.961 +03:00 [ERR] خطأ في فتح النافذة الرئيسية
System.Windows.Markup.XamlParseException: Provide value on 'System.Windows.Baml2006.TypeConverterMarkupExtension' threw an exception.
 ---> System.FormatException: Settings is not a valid value for PackIconMaterialKind.
 ---> System.ArgumentException: Requested value 'Settings' was not found.
   at System.Enum.TryParseByName[TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TStorage& result)
   at System.Enum.TryParseByValueOrName[TUnderlying,TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TUnderlying& result)
   at System.Enum.TryParse(Type enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, Object& result)
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   --- End of inner exception stack trace ---
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   at MS.Internal.Xaml.Runtime.ClrObjectRuntime.CallProvideValue(MarkupExtension me, IServiceProvider serviceProvider)
   --- End of inner exception stack trace ---
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.LoadBaml(XamlReader xamlReader, Boolean skipJournaledProperties, Object rootObject, XamlAccessLevel accessLevel, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at ExamBuilder.UI.Views.MainWindow.InitializeComponent() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml:line 1
   at ExamBuilder.UI.Views.MainWindow..ctor() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml.cs:line 18
   at ExamBuilder.UI.Views.LoginWindow.PerformLogin() in D:\mr\qqqq\ExamBuilder.UI\Views\LoginWindow.xaml.cs:line 81
2025-05-25 12:57:33.972 +03:00 [INF] تم تسجيل دخول المستخدم: جو
2025-05-25 12:57:36.315 +03:00 [INF] محاولة تسجيل دخول - المستخدم: 'جو', كلمة المرور: 'جو'
2025-05-25 12:57:36.315 +03:00 [INF] تم التحقق من البيانات بنجاح
2025-05-25 12:57:37.316 +03:00 [INF] محاولة فتح النافذة الرئيسية
2025-05-25 12:57:37.322 +03:00 [ERR] خطأ في فتح النافذة الرئيسية
System.Windows.Markup.XamlParseException: Provide value on 'System.Windows.Baml2006.TypeConverterMarkupExtension' threw an exception.
 ---> System.FormatException: Settings is not a valid value for PackIconMaterialKind.
 ---> System.ArgumentException: Requested value 'Settings' was not found.
   at System.Enum.TryParseByName[TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TStorage& result)
   at System.Enum.TryParseByValueOrName[TUnderlying,TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TUnderlying& result)
   at System.Enum.TryParse(Type enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, Object& result)
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   --- End of inner exception stack trace ---
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   at MS.Internal.Xaml.Runtime.ClrObjectRuntime.CallProvideValue(MarkupExtension me, IServiceProvider serviceProvider)
   --- End of inner exception stack trace ---
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.LoadBaml(XamlReader xamlReader, Boolean skipJournaledProperties, Object rootObject, XamlAccessLevel accessLevel, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at ExamBuilder.UI.Views.MainWindow.InitializeComponent() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml:line 1
   at ExamBuilder.UI.Views.MainWindow..ctor() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml.cs:line 18
   at ExamBuilder.UI.Views.LoginWindow.PerformLogin() in D:\mr\qqqq\ExamBuilder.UI\Views\LoginWindow.xaml.cs:line 81
2025-05-25 12:57:37.323 +03:00 [INF] تم تسجيل دخول المستخدم: جو
2025-05-25 12:57:57.367 +03:00 [INF] محاولة تسجيل دخول - المستخدم: 'جو', كلمة المرور: 'جو'
2025-05-25 12:57:57.367 +03:00 [INF] تم التحقق من البيانات بنجاح
2025-05-25 12:57:58.367 +03:00 [INF] محاولة فتح النافذة الرئيسية
2025-05-25 12:57:58.373 +03:00 [ERR] خطأ في فتح النافذة الرئيسية
System.Windows.Markup.XamlParseException: Provide value on 'System.Windows.Baml2006.TypeConverterMarkupExtension' threw an exception.
 ---> System.FormatException: Settings is not a valid value for PackIconMaterialKind.
 ---> System.ArgumentException: Requested value 'Settings' was not found.
   at System.Enum.TryParseByName[TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TStorage& result)
   at System.Enum.TryParseByValueOrName[TUnderlying,TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TUnderlying& result)
   at System.Enum.TryParse(Type enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, Object& result)
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   --- End of inner exception stack trace ---
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   at MS.Internal.Xaml.Runtime.ClrObjectRuntime.CallProvideValue(MarkupExtension me, IServiceProvider serviceProvider)
   --- End of inner exception stack trace ---
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.LoadBaml(XamlReader xamlReader, Boolean skipJournaledProperties, Object rootObject, XamlAccessLevel accessLevel, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at ExamBuilder.UI.Views.MainWindow.InitializeComponent() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml:line 1
   at ExamBuilder.UI.Views.MainWindow..ctor() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml.cs:line 18
   at ExamBuilder.UI.Views.LoginWindow.PerformLogin() in D:\mr\qqqq\ExamBuilder.UI\Views\LoginWindow.xaml.cs:line 81
2025-05-25 12:57:58.373 +03:00 [INF] تم تسجيل دخول المستخدم: جو
2025-05-25 12:58:10.608 +03:00 [INF] تم تطبيق الثيم: Red
2025-05-25 12:58:10.609 +03:00 [INF] تم تطبيق الثيم الأحمر
2025-05-25 12:58:11.647 +03:00 [INF] تم تطبيق الثيم: Green
2025-05-25 12:58:11.647 +03:00 [INF] تم تطبيق الثيم الأخضر
2025-05-25 12:58:13.316 +03:00 [INF] تم تطبيق الثيم: Dark
2025-05-25 12:58:13.316 +03:00 [INF] تم تطبيق الثيم الداكن
2025-05-25 12:58:15.502 +03:00 [INF] محاولة تسجيل دخول - المستخدم: 'جو', كلمة المرور: 'جو'
2025-05-25 12:58:15.502 +03:00 [INF] تم التحقق من البيانات بنجاح
2025-05-25 12:58:16.502 +03:00 [INF] محاولة فتح النافذة الرئيسية
2025-05-25 12:58:16.507 +03:00 [ERR] خطأ في فتح النافذة الرئيسية
System.Windows.Markup.XamlParseException: Provide value on 'System.Windows.Baml2006.TypeConverterMarkupExtension' threw an exception.
 ---> System.FormatException: Settings is not a valid value for PackIconMaterialKind.
 ---> System.ArgumentException: Requested value 'Settings' was not found.
   at System.Enum.TryParseByName[TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TStorage& result)
   at System.Enum.TryParseByValueOrName[TUnderlying,TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TUnderlying& result)
   at System.Enum.TryParse(Type enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, Object& result)
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   --- End of inner exception stack trace ---
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   at MS.Internal.Xaml.Runtime.ClrObjectRuntime.CallProvideValue(MarkupExtension me, IServiceProvider serviceProvider)
   --- End of inner exception stack trace ---
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.LoadBaml(XamlReader xamlReader, Boolean skipJournaledProperties, Object rootObject, XamlAccessLevel accessLevel, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at ExamBuilder.UI.Views.MainWindow.InitializeComponent() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml:line 1
   at ExamBuilder.UI.Views.MainWindow..ctor() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml.cs:line 18
   at ExamBuilder.UI.Views.LoginWindow.PerformLogin() in D:\mr\qqqq\ExamBuilder.UI\Views\LoginWindow.xaml.cs:line 81
2025-05-25 12:58:16.507 +03:00 [INF] تم تسجيل دخول المستخدم: جو
2025-05-25 12:58:27.452 +03:00 [INF] محاولة تسجيل دخول - المستخدم: 'جو', كلمة المرور: 'جو'
2025-05-25 12:58:27.452 +03:00 [INF] تم التحقق من البيانات بنجاح
2025-05-25 12:58:28.452 +03:00 [INF] محاولة فتح النافذة الرئيسية
2025-05-25 12:58:28.456 +03:00 [ERR] خطأ في فتح النافذة الرئيسية
System.Windows.Markup.XamlParseException: Provide value on 'System.Windows.Baml2006.TypeConverterMarkupExtension' threw an exception.
 ---> System.FormatException: Settings is not a valid value for PackIconMaterialKind.
 ---> System.ArgumentException: Requested value 'Settings' was not found.
   at System.Enum.TryParseByName[TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TStorage& result)
   at System.Enum.TryParseByValueOrName[TUnderlying,TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TUnderlying& result)
   at System.Enum.TryParse(Type enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, Object& result)
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   --- End of inner exception stack trace ---
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   at MS.Internal.Xaml.Runtime.ClrObjectRuntime.CallProvideValue(MarkupExtension me, IServiceProvider serviceProvider)
   --- End of inner exception stack trace ---
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.LoadBaml(XamlReader xamlReader, Boolean skipJournaledProperties, Object rootObject, XamlAccessLevel accessLevel, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at ExamBuilder.UI.Views.MainWindow.InitializeComponent() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml:line 1
   at ExamBuilder.UI.Views.MainWindow..ctor() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml.cs:line 18
   at ExamBuilder.UI.Views.LoginWindow.PerformLogin() in D:\mr\qqqq\ExamBuilder.UI\Views\LoginWindow.xaml.cs:line 81
2025-05-25 12:58:28.457 +03:00 [INF] تم تسجيل دخول المستخدم: جو
2025-05-25 12:58:34.780 +03:00 [INF] تم تطبيق الثيم: Green
2025-05-25 12:58:34.780 +03:00 [INF] تم تطبيق الثيم الأخضر
2025-05-25 12:58:35.745 +03:00 [INF] تم تطبيق الثيم: Dark
2025-05-25 12:58:35.745 +03:00 [INF] تم تطبيق الثيم الداكن
2025-05-25 12:58:37.005 +03:00 [INF] تم تطبيق الثيم: Red
2025-05-25 12:58:37.005 +03:00 [INF] تم تطبيق الثيم الأحمر
2025-05-25 12:58:42.464 +03:00 [INF] محاولة تسجيل دخول - المستخدم: 'جو', كلمة المرور: 'جو'
2025-05-25 12:58:42.464 +03:00 [INF] تم التحقق من البيانات بنجاح
2025-05-25 12:58:43.465 +03:00 [INF] محاولة فتح النافذة الرئيسية
2025-05-25 12:58:43.469 +03:00 [ERR] خطأ في فتح النافذة الرئيسية
System.Windows.Markup.XamlParseException: Provide value on 'System.Windows.Baml2006.TypeConverterMarkupExtension' threw an exception.
 ---> System.FormatException: Settings is not a valid value for PackIconMaterialKind.
 ---> System.ArgumentException: Requested value 'Settings' was not found.
   at System.Enum.TryParseByName[TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TStorage& result)
   at System.Enum.TryParseByValueOrName[TUnderlying,TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TUnderlying& result)
   at System.Enum.TryParse(Type enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, Object& result)
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   --- End of inner exception stack trace ---
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   at MS.Internal.Xaml.Runtime.ClrObjectRuntime.CallProvideValue(MarkupExtension me, IServiceProvider serviceProvider)
   --- End of inner exception stack trace ---
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.LoadBaml(XamlReader xamlReader, Boolean skipJournaledProperties, Object rootObject, XamlAccessLevel accessLevel, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at ExamBuilder.UI.Views.MainWindow.InitializeComponent() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml:line 1
   at ExamBuilder.UI.Views.MainWindow..ctor() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml.cs:line 18
   at ExamBuilder.UI.Views.LoginWindow.PerformLogin() in D:\mr\qqqq\ExamBuilder.UI\Views\LoginWindow.xaml.cs:line 81
2025-05-25 12:58:43.470 +03:00 [INF] تم تسجيل دخول المستخدم: جو
2025-05-25 12:58:47.652 +03:00 [INF] محاولة تسجيل دخول - المستخدم: 'جو', كلمة المرور: 'جو'
2025-05-25 12:58:47.652 +03:00 [INF] تم التحقق من البيانات بنجاح
2025-05-25 12:58:48.652 +03:00 [INF] محاولة فتح النافذة الرئيسية
2025-05-25 12:58:48.657 +03:00 [ERR] خطأ في فتح النافذة الرئيسية
System.Windows.Markup.XamlParseException: Provide value on 'System.Windows.Baml2006.TypeConverterMarkupExtension' threw an exception.
 ---> System.FormatException: Settings is not a valid value for PackIconMaterialKind.
 ---> System.ArgumentException: Requested value 'Settings' was not found.
   at System.Enum.TryParseByName[TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TStorage& result)
   at System.Enum.TryParseByValueOrName[TUnderlying,TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TUnderlying& result)
   at System.Enum.TryParse(Type enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, Object& result)
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   --- End of inner exception stack trace ---
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   at MS.Internal.Xaml.Runtime.ClrObjectRuntime.CallProvideValue(MarkupExtension me, IServiceProvider serviceProvider)
   --- End of inner exception stack trace ---
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.LoadBaml(XamlReader xamlReader, Boolean skipJournaledProperties, Object rootObject, XamlAccessLevel accessLevel, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at ExamBuilder.UI.Views.MainWindow.InitializeComponent() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml:line 1
   at ExamBuilder.UI.Views.MainWindow..ctor() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml.cs:line 18
   at ExamBuilder.UI.Views.LoginWindow.PerformLogin() in D:\mr\qqqq\ExamBuilder.UI\Views\LoginWindow.xaml.cs:line 81
2025-05-25 12:58:48.657 +03:00 [INF] تم تسجيل دخول المستخدم: جو
2025-05-25 12:58:55.169 +03:00 [INF] محاولة تسجيل دخول - المستخدم: 'جو', كلمة المرور: 'جو'
2025-05-25 12:58:55.169 +03:00 [INF] تم التحقق من البيانات بنجاح
2025-05-25 12:58:56.169 +03:00 [INF] محاولة فتح النافذة الرئيسية
2025-05-25 12:58:56.173 +03:00 [ERR] خطأ في فتح النافذة الرئيسية
System.Windows.Markup.XamlParseException: Provide value on 'System.Windows.Baml2006.TypeConverterMarkupExtension' threw an exception.
 ---> System.FormatException: Settings is not a valid value for PackIconMaterialKind.
 ---> System.ArgumentException: Requested value 'Settings' was not found.
   at System.Enum.TryParseByName[TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TStorage& result)
   at System.Enum.TryParseByValueOrName[TUnderlying,TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TUnderlying& result)
   at System.Enum.TryParse(Type enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, Object& result)
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   --- End of inner exception stack trace ---
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   at MS.Internal.Xaml.Runtime.ClrObjectRuntime.CallProvideValue(MarkupExtension me, IServiceProvider serviceProvider)
   --- End of inner exception stack trace ---
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.LoadBaml(XamlReader xamlReader, Boolean skipJournaledProperties, Object rootObject, XamlAccessLevel accessLevel, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at ExamBuilder.UI.Views.MainWindow.InitializeComponent() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml:line 1
   at ExamBuilder.UI.Views.MainWindow..ctor() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml.cs:line 18
   at ExamBuilder.UI.Views.LoginWindow.PerformLogin() in D:\mr\qqqq\ExamBuilder.UI\Views\LoginWindow.xaml.cs:line 81
2025-05-25 12:58:56.173 +03:00 [INF] تم تسجيل دخول المستخدم: جو
2025-05-25 12:59:57.409 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-05-25 12:59:57.439 +03:00 [INF] Hosting environment: Production
2025-05-25 12:59:57.440 +03:00 [INF] Content root path: D:\mr\qqqq\ExamBuilder.UI\bin\Release\net8.0-windows
2025-05-25 12:59:57.440 +03:00 [INF] تم بدء تشغيل التطبيق بنجاح
2025-05-25 12:59:57.704 +03:00 [INF] تم تحميل نافذة تسجيل الدخول
2025-05-25 13:00:02.433 +03:00 [INF] محاولة تسجيل دخول - المستخدم: 'جو', كلمة المرور: 'جو'
2025-05-25 13:00:02.434 +03:00 [INF] تحقق المستخدم: True, تحقق كلمة المرور: True
2025-05-25 13:00:02.434 +03:00 [INF] تم التحقق من البيانات بنجاح
2025-05-25 13:00:03.434 +03:00 [INF] محاولة فتح النافذة الرئيسية
2025-05-25 13:00:03.444 +03:00 [ERR] خطأ في فتح النافذة الرئيسية
System.Windows.Markup.XamlParseException: Provide value on 'System.Windows.Baml2006.TypeConverterMarkupExtension' threw an exception.
 ---> System.FormatException: Settings is not a valid value for PackIconMaterialKind.
 ---> System.ArgumentException: Requested value 'Settings' was not found.
   at System.Enum.TryParseByName[TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TStorage& result)
   at System.Enum.TryParseByValueOrName[TUnderlying,TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TUnderlying& result)
   at System.Enum.TryParse(Type enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, Object& result)
   at System.Enum.Parse(Type enumType, String value, Boolean ignoreCase)
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   --- End of inner exception stack trace ---
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   at MS.Internal.Xaml.Runtime.ClrObjectRuntime.CallProvideValue(MarkupExtension me, IServiceProvider serviceProvider)
   --- End of inner exception stack trace ---
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.LoadBaml(XamlReader xamlReader, Boolean skipJournaledProperties, Object rootObject, XamlAccessLevel accessLevel, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at ExamBuilder.UI.Views.MainWindow.InitializeComponent() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml:line 1
   at ExamBuilder.UI.Views.MainWindow..ctor() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml.cs:line 18
   at ExamBuilder.UI.Views.LoginWindow.PerformLogin() in D:\mr\qqqq\ExamBuilder.UI\Views\LoginWindow.xaml.cs:line 87
2025-05-25 13:00:03.454 +03:00 [INF] تم تسجيل دخول المستخدم: جو
2025-05-25 13:00:06.536 +03:00 [INF] محاولة تسجيل دخول - المستخدم: 'جو', كلمة المرور: 'جو'
2025-05-25 13:00:06.536 +03:00 [INF] تحقق المستخدم: True, تحقق كلمة المرور: True
2025-05-25 13:00:06.536 +03:00 [INF] تم التحقق من البيانات بنجاح
2025-05-25 13:00:07.536 +03:00 [INF] محاولة فتح النافذة الرئيسية
2025-05-25 13:00:07.542 +03:00 [ERR] خطأ في فتح النافذة الرئيسية
System.Windows.Markup.XamlParseException: Provide value on 'System.Windows.Baml2006.TypeConverterMarkupExtension' threw an exception.
 ---> System.FormatException: Settings is not a valid value for PackIconMaterialKind.
 ---> System.ArgumentException: Requested value 'Settings' was not found.
   at System.Enum.TryParseByName[TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TStorage& result)
   at System.Enum.TryParseByValueOrName[TUnderlying,TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TUnderlying& result)
   at System.Enum.TryParse(Type enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, Object& result)
   at System.Enum.Parse(Type enumType, String value, Boolean ignoreCase)
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   --- End of inner exception stack trace ---
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   at MS.Internal.Xaml.Runtime.ClrObjectRuntime.CallProvideValue(MarkupExtension me, IServiceProvider serviceProvider)
   --- End of inner exception stack trace ---
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.LoadBaml(XamlReader xamlReader, Boolean skipJournaledProperties, Object rootObject, XamlAccessLevel accessLevel, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at ExamBuilder.UI.Views.MainWindow.InitializeComponent() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml:line 1
   at ExamBuilder.UI.Views.MainWindow..ctor() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml.cs:line 18
   at ExamBuilder.UI.Views.LoginWindow.PerformLogin() in D:\mr\qqqq\ExamBuilder.UI\Views\LoginWindow.xaml.cs:line 87
2025-05-25 13:00:07.542 +03:00 [INF] تم تسجيل دخول المستخدم: جو
2025-05-25 13:00:10.130 +03:00 [INF] تم تطبيق الثيم: Green
2025-05-25 13:00:10.130 +03:00 [INF] تم تطبيق الثيم الأخضر
2025-05-25 13:06:14.995 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-05-25 13:06:15.025 +03:00 [INF] Hosting environment: Production
2025-05-25 13:06:15.026 +03:00 [INF] Content root path: D:\mr\qqqq\ExamBuilder.UI\bin\Release\net8.0-windows
2025-05-25 13:06:15.027 +03:00 [INF] تم بدء تشغيل التطبيق بنجاح
2025-05-25 13:06:15.294 +03:00 [INF] تم تحميل نافذة تسجيل الدخول
2025-05-25 13:08:14.621 +03:00 [INF] محاولة تسجيل دخول - المستخدم: 'جو', كلمة المرور: 'جو'
2025-05-25 13:08:14.621 +03:00 [INF] تحقق المستخدم: True, تحقق كلمة المرور: True
2025-05-25 13:08:14.621 +03:00 [INF] تم التحقق من البيانات بنجاح
2025-05-25 13:08:19.627 +03:00 [INF] محاولة فتح النافذة الرئيسية
2025-05-25 13:08:19.636 +03:00 [ERR] خطأ في فتح النافذة الرئيسية
System.Windows.Markup.XamlParseException: Provide value on 'System.Windows.Baml2006.TypeConverterMarkupExtension' threw an exception.
 ---> System.FormatException: Settings is not a valid value for PackIconMaterialKind.
 ---> System.ArgumentException: Requested value 'Settings' was not found.
   at System.Enum.TryParseByName[TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TStorage& result)
   at System.Enum.TryParseByValueOrName[TUnderlying,TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TUnderlying& result)
   at System.Enum.TryParse(Type enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, Object& result)
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   --- End of inner exception stack trace ---
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   at MS.Internal.Xaml.Runtime.ClrObjectRuntime.CallProvideValue(MarkupExtension me, IServiceProvider serviceProvider)
   --- End of inner exception stack trace ---
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.LoadBaml(XamlReader xamlReader, Boolean skipJournaledProperties, Object rootObject, XamlAccessLevel accessLevel, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at ExamBuilder.UI.Views.MainWindow.InitializeComponent() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml:line 1
   at ExamBuilder.UI.Views.MainWindow..ctor() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml.cs:line 18
   at ExamBuilder.UI.Views.LoginWindow.PerformLogin() in D:\mr\qqqq\ExamBuilder.UI\Views\LoginWindow.xaml.cs:line 102
2025-05-25 13:08:19.647 +03:00 [INF] تم تسجيل دخول المستخدم: جو
2025-05-25 13:08:24.499 +03:00 [INF] محاولة تسجيل دخول - المستخدم: 'جو', كلمة المرور: 'جو'
2025-05-25 13:08:24.499 +03:00 [INF] تحقق المستخدم: True, تحقق كلمة المرور: True
2025-05-25 13:08:24.499 +03:00 [INF] تم التحقق من البيانات بنجاح
2025-05-25 13:08:30.485 +03:00 [INF] محاولة فتح النافذة الرئيسية
2025-05-25 13:08:30.491 +03:00 [ERR] خطأ في فتح النافذة الرئيسية
System.Windows.Markup.XamlParseException: Provide value on 'System.Windows.Baml2006.TypeConverterMarkupExtension' threw an exception.
 ---> System.FormatException: Settings is not a valid value for PackIconMaterialKind.
 ---> System.ArgumentException: Requested value 'Settings' was not found.
   at System.Enum.TryParseByName[TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TStorage& result)
   at System.Enum.TryParseByValueOrName[TUnderlying,TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TUnderlying& result)
   at System.Enum.TryParse(Type enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, Object& result)
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   --- End of inner exception stack trace ---
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   at MS.Internal.Xaml.Runtime.ClrObjectRuntime.CallProvideValue(MarkupExtension me, IServiceProvider serviceProvider)
   --- End of inner exception stack trace ---
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.LoadBaml(XamlReader xamlReader, Boolean skipJournaledProperties, Object rootObject, XamlAccessLevel accessLevel, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at ExamBuilder.UI.Views.MainWindow.InitializeComponent() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml:line 1
   at ExamBuilder.UI.Views.MainWindow..ctor() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml.cs:line 18
   at ExamBuilder.UI.Views.LoginWindow.PerformLogin() in D:\mr\qqqq\ExamBuilder.UI\Views\LoginWindow.xaml.cs:line 102
2025-05-25 13:08:30.491 +03:00 [INF] تم تسجيل دخول المستخدم: جو
2025-05-25 13:08:37.185 +03:00 [INF] محاولة تسجيل دخول - المستخدم: 'جو', كلمة المرور: 'جو'
2025-05-25 13:08:37.186 +03:00 [INF] تحقق المستخدم: True, تحقق كلمة المرور: True
2025-05-25 13:08:37.186 +03:00 [INF] تم التحقق من البيانات بنجاح
2025-05-25 13:21:49.262 +03:00 [INF] تم رفض طلب الدخول من قبل المستخدم
2025-05-25 13:21:51.977 +03:00 [INF] محاولة تسجيل دخول - المستخدم: 'جو', كلمة المرور: 'جو'
2025-05-25 13:21:51.977 +03:00 [INF] تحقق المستخدم: True, تحقق كلمة المرور: True
2025-05-25 13:21:51.977 +03:00 [INF] تم التحقق من البيانات بنجاح
2025-05-25 13:21:55.319 +03:00 [INF] محاولة فتح النافذة الرئيسية
2025-05-25 13:21:55.323 +03:00 [ERR] خطأ في فتح النافذة الرئيسية
System.Windows.Markup.XamlParseException: Provide value on 'System.Windows.Baml2006.TypeConverterMarkupExtension' threw an exception.
 ---> System.FormatException: Settings is not a valid value for PackIconMaterialKind.
 ---> System.ArgumentException: Requested value 'Settings' was not found.
   at System.Enum.TryParseByName[TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TStorage& result)
   at System.Enum.TryParseByValueOrName[TUnderlying,TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TUnderlying& result)
   at System.Enum.TryParse(Type enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, Object& result)
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   --- End of inner exception stack trace ---
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   at MS.Internal.Xaml.Runtime.ClrObjectRuntime.CallProvideValue(MarkupExtension me, IServiceProvider serviceProvider)
   --- End of inner exception stack trace ---
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.LoadBaml(XamlReader xamlReader, Boolean skipJournaledProperties, Object rootObject, XamlAccessLevel accessLevel, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at ExamBuilder.UI.Views.MainWindow.InitializeComponent() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml:line 1
   at ExamBuilder.UI.Views.MainWindow..ctor() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml.cs:line 18
   at ExamBuilder.UI.Views.LoginWindow.PerformLogin() in D:\mr\qqqq\ExamBuilder.UI\Views\LoginWindow.xaml.cs:line 102
2025-05-25 13:21:55.324 +03:00 [INF] تم تسجيل دخول المستخدم: جو
2025-05-25 13:21:56.422 +03:00 [INF] تم تطبيق الثيم الأخضر
2025-05-25 13:21:57.781 +03:00 [INF] تم تطبيق الثيم الأحمر
2025-05-25 13:21:59.013 +03:00 [INF] تم تطبيق الثيم الداكن
2025-05-25 13:21:59.573 +03:00 [INF] تم تطبيق الثيم الداكن
2025-05-25 13:21:59.754 +03:00 [INF] تم تطبيق الثيم الداكن
2025-05-25 13:21:59.976 +03:00 [INF] تم تطبيق الثيم الداكن
2025-05-25 13:22:00.387 +03:00 [INF] تم تطبيق الثيم الداكن
2025-05-25 13:22:00.728 +03:00 [INF] تم تطبيق الثيم الداكن
2025-05-25 13:22:01.099 +03:00 [INF] تم تطبيق الثيم الداكن
