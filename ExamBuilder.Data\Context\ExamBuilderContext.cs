using Microsoft.EntityFrameworkCore;
using ExamBuilder.Core.Models;

namespace ExamBuilder.Data.Context
{
    /// <summary>
    /// سياق قاعدة البيانات الرئيسي
    /// </summary>
    public class ExamBuilderContext : DbContext
    {
        public ExamBuilderContext()
        {
        }

        public ExamBuilderContext(DbContextOptions<ExamBuilderContext> options) : base(options)
        {
        }

        // DbSets
        public DbSet<Exam> Exams { get; set; }
        public DbSet<Question> Questions { get; set; }
        public DbSet<QuestionOption> QuestionOptions { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                optionsBuilder.UseSqlite("Data Source=ExamBuilder.db");
            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // تكوين نموذج الامتحان
            modelBuilder.Entity<Exam>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Title).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Subject).IsRequired().HasMaxLength(100);
                entity.Property(e => e.CreatedBy).IsRequired();
                
                // تحويل Settings إلى JSON
                entity.OwnsOne(e => e.Settings, settings =>
                {
                    settings.Property(s => s.FontFamily).HasDefaultValue("Traditional Arabic");
                    settings.Property(s => s.FontSize).HasDefaultValue(14);
                    settings.Property(s => s.QuestionsPerPage).HasDefaultValue(5);
                });
            });

            // تكوين نموذج السؤال
            modelBuilder.Entity<Question>(entity =>
            {
                entity.HasKey(q => q.Id);
                entity.Property(q => q.Text).IsRequired();
                entity.Property(q => q.Points).HasColumnType("decimal(5,2)");
                
                // العلاقة مع الامتحان
                entity.HasOne(q => q.Exam)
                      .WithMany(e => e.Questions)
                      .HasForeignKey(q => q.ExamId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            // تكوين نموذج خيارات السؤال
            modelBuilder.Entity<QuestionOption>(entity =>
            {
                entity.HasKey(o => o.Id);
                entity.Property(o => o.Text).IsRequired();
                
                // العلاقة مع السؤال
                entity.HasOne(o => o.Question)
                      .WithMany(q => q.Options)
                      .HasForeignKey(o => o.QuestionId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            // بيانات أولية
            SeedData(modelBuilder);
        }

        private static void SeedData(ModelBuilder modelBuilder)
        {
            // إضافة امتحان تجريبي
            modelBuilder.Entity<Exam>().HasData(
                new Exam
                {
                    Id = 1,
                    Title = "امتحان تجريبي",
                    Subject = "الرياضيات",
                    Description = "امتحان تجريبي للاختبار",
                    TeacherName = "المهندس يوسف غنام",
                    Grade = "الصف الثالث الثانوي",
                    DurationMinutes = 120,
                    TotalMarks = 100,
                    CreatedBy = "جو",
                    CreatedAt = DateTime.Now
                }
            );

            // إضافة أسئلة تجريبية
            modelBuilder.Entity<Question>().HasData(
                new Question
                {
                    Id = 1,
                    ExamId = 1,
                    Text = "ما هو ناتج 2 + 2؟",
                    Type = QuestionType.MultipleChoice,
                    Points = 5,
                    Order = 1,
                    Difficulty = DifficultyLevel.Easy,
                    CreatedAt = DateTime.Now
                },
                new Question
                {
                    Id = 2,
                    ExamId = 1,
                    Text = "حل المعادلة: x² - 4 = 0",
                    Type = QuestionType.ShortAnswer,
                    Points = 10,
                    Order = 2,
                    Difficulty = DifficultyLevel.Medium,
                    CreatedAt = DateTime.Now
                }
            );

            // إضافة خيارات للسؤال الأول
            modelBuilder.Entity<QuestionOption>().HasData(
                new QuestionOption { Id = 1, QuestionId = 1, Text = "3", IsCorrect = false, Order = 1 },
                new QuestionOption { Id = 2, QuestionId = 1, Text = "4", IsCorrect = true, Order = 2 },
                new QuestionOption { Id = 3, QuestionId = 1, Text = "5", IsCorrect = false, Order = 3 },
                new QuestionOption { Id = 4, QuestionId = 1, Text = "6", IsCorrect = false, Order = 4 }
            );
        }
    }
}
