# برنامج إنشاء الامتحانات - المهندس يوسف غنام
# سكريبت تشغيل PowerShell

# تعيين ترميز UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    برنامج إنشاء الامتحانات" -ForegroundColor Yellow
Write-Host "    المهندس يوسف غنام" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "جارٍ التحقق من متطلبات النظام..." -ForegroundColor Blue

# التحقق من وجود .NET 8.0
try {
    $dotnetVersion = dotnet --version
    Write-Host "تم العثور على .NET: $dotnetVersion" -ForegroundColor Green
}
catch {
    Write-Host "خطأ: .NET 8.0 غير مثبت على النظام" -ForegroundColor Red
    Write-Host "يرجى تثبيت .NET 8.0 SDK من:" -ForegroundColor Yellow
    Write-Host "https://dotnet.microsoft.com/download/dotnet/8.0" -ForegroundColor Blue
    Read-Host "اضغط Enter للخروج"
    exit 1
}

Write-Host ""
Write-Host "جارٍ استعادة الحزم..." -ForegroundColor Blue

try {
    dotnet restore
    if ($LASTEXITCODE -ne 0) {
        throw "فشل في استعادة الحزم"
    }
    Write-Host "تم استعادة الحزم بنجاح" -ForegroundColor Green
}
catch {
    Write-Host "خطأ في استعادة الحزم: $_" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
    exit 1
}

Write-Host ""
Write-Host "جارٍ بناء المشروع..." -ForegroundColor Blue

try {
    dotnet build --configuration Release
    if ($LASTEXITCODE -ne 0) {
        throw "فشل في بناء المشروع"
    }
    Write-Host "تم بناء المشروع بنجاح" -ForegroundColor Green
}
catch {
    Write-Host "خطأ في بناء المشروع: $_" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
    exit 1
}

Write-Host ""
Write-Host "جارٍ تشغيل التطبيق..." -ForegroundColor Blue
Write-Host ""
Write-Host "بيانات تسجيل الدخول:" -ForegroundColor Yellow
Write-Host "اسم المستخدم: جو" -ForegroundColor Cyan
Write-Host "كلمة المرور: جو" -ForegroundColor Cyan
Write-Host ""

try {
    dotnet run --project ExamBuilder.UI --configuration Release
    if ($LASTEXITCODE -ne 0) {
        throw "فشل في تشغيل التطبيق"
    }
}
catch {
    Write-Host "خطأ في تشغيل التطبيق: $_" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
    exit 1
}

Write-Host ""
Write-Host "تم إغلاق التطبيق بنجاح" -ForegroundColor Green
Read-Host "اضغط Enter للخروج"
