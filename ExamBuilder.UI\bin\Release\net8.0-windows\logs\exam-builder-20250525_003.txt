2025-05-25 13:31:42.966 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-05-25 13:31:43.000 +03:00 [INF] Hosting environment: Production
2025-05-25 13:31:43.001 +03:00 [INF] Content root path: D:\mr\qqqq\ExamBuilder.UI\bin\Release\net8.0-windows
2025-05-25 13:31:43.001 +03:00 [INF] تم بدء تشغيل التطبيق بنجاح
2025-05-25 13:31:43.270 +03:00 [INF] تم تحميل نافذة تسجيل الدخول
2025-05-25 13:31:52.720 +03:00 [INF] محاولة تسجيل دخول - المستخدم: 'جو', كلمة المرور: 'جو'
2025-05-25 13:31:52.720 +03:00 [INF] تحقق المستخدم: True, تحقق كلمة المرور: True
2025-05-25 13:31:52.720 +03:00 [INF] تم التحقق من البيانات بنجاح
2025-05-25 13:31:56.598 +03:00 [INF] محاولة فتح النافذة الرئيسية
2025-05-25 13:31:56.607 +03:00 [ERR] خطأ في فتح النافذة الرئيسية
System.Windows.Markup.XamlParseException: Provide value on 'System.Windows.Baml2006.TypeConverterMarkupExtension' threw an exception.
 ---> System.FormatException: Settings is not a valid value for PackIconMaterialKind.
 ---> System.ArgumentException: Requested value 'Settings' was not found.
   at System.Enum.TryParseByName[TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TStorage& result)
   at System.Enum.TryParseByValueOrName[TUnderlying,TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TUnderlying& result)
   at System.Enum.TryParse(Type enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, Object& result)
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   --- End of inner exception stack trace ---
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   at MS.Internal.Xaml.Runtime.ClrObjectRuntime.CallProvideValue(MarkupExtension me, IServiceProvider serviceProvider)
   --- End of inner exception stack trace ---
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.LoadBaml(XamlReader xamlReader, Boolean skipJournaledProperties, Object rootObject, XamlAccessLevel accessLevel, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at ExamBuilder.UI.Views.MainWindow.InitializeComponent() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml:line 1
   at ExamBuilder.UI.Views.MainWindow..ctor() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml.cs:line 18
   at ExamBuilder.UI.Views.LoginWindow.PerformLogin() in D:\mr\qqqq\ExamBuilder.UI\Views\LoginWindow.xaml.cs:line 102
2025-05-25 13:31:56.618 +03:00 [INF] تم تسجيل دخول المستخدم: جو
2025-05-25 13:32:00.743 +03:00 [INF] محاولة تسجيل دخول - المستخدم: 'جو', كلمة المرور: 'جو'
2025-05-25 13:32:00.743 +03:00 [INF] تحقق المستخدم: True, تحقق كلمة المرور: True
2025-05-25 13:32:00.743 +03:00 [INF] تم التحقق من البيانات بنجاح
2025-05-25 13:32:04.368 +03:00 [INF] محاولة فتح النافذة الرئيسية
2025-05-25 13:32:04.374 +03:00 [ERR] خطأ في فتح النافذة الرئيسية
System.Windows.Markup.XamlParseException: Provide value on 'System.Windows.Baml2006.TypeConverterMarkupExtension' threw an exception.
 ---> System.FormatException: Settings is not a valid value for PackIconMaterialKind.
 ---> System.ArgumentException: Requested value 'Settings' was not found.
   at System.Enum.TryParseByName[TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TStorage& result)
   at System.Enum.TryParseByValueOrName[TUnderlying,TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TUnderlying& result)
   at System.Enum.TryParse(Type enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, Object& result)
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   --- End of inner exception stack trace ---
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   at MS.Internal.Xaml.Runtime.ClrObjectRuntime.CallProvideValue(MarkupExtension me, IServiceProvider serviceProvider)
   --- End of inner exception stack trace ---
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.LoadBaml(XamlReader xamlReader, Boolean skipJournaledProperties, Object rootObject, XamlAccessLevel accessLevel, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at ExamBuilder.UI.Views.MainWindow.InitializeComponent() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml:line 1
   at ExamBuilder.UI.Views.MainWindow..ctor() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml.cs:line 18
   at ExamBuilder.UI.Views.LoginWindow.PerformLogin() in D:\mr\qqqq\ExamBuilder.UI\Views\LoginWindow.xaml.cs:line 102
2025-05-25 13:32:04.375 +03:00 [INF] تم تسجيل دخول المستخدم: جو
2025-05-25 13:32:05.574 +03:00 [INF] تم تطبيق الثيم الأحمر
2025-05-25 13:32:06.699 +03:00 [INF] تم تطبيق الثيم الأخضر
2025-05-25 13:32:08.041 +03:00 [INF] تم تطبيق الثيم الداكن
2025-05-25 13:32:10.478 +03:00 [INF] محاولة تسجيل دخول - المستخدم: 'جو', كلمة المرور: 'جو'
2025-05-25 13:32:10.478 +03:00 [INF] تحقق المستخدم: True, تحقق كلمة المرور: True
2025-05-25 13:32:10.478 +03:00 [INF] تم التحقق من البيانات بنجاح
2025-05-25 13:32:14.057 +03:00 [INF] محاولة فتح النافذة الرئيسية
2025-05-25 13:32:14.062 +03:00 [ERR] خطأ في فتح النافذة الرئيسية
System.Windows.Markup.XamlParseException: Provide value on 'System.Windows.Baml2006.TypeConverterMarkupExtension' threw an exception.
 ---> System.FormatException: Settings is not a valid value for PackIconMaterialKind.
 ---> System.ArgumentException: Requested value 'Settings' was not found.
   at System.Enum.TryParseByName[TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TStorage& result)
   at System.Enum.TryParseByValueOrName[TUnderlying,TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TUnderlying& result)
   at System.Enum.TryParse(Type enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, Object& result)
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   --- End of inner exception stack trace ---
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   at MS.Internal.Xaml.Runtime.ClrObjectRuntime.CallProvideValue(MarkupExtension me, IServiceProvider serviceProvider)
   --- End of inner exception stack trace ---
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.LoadBaml(XamlReader xamlReader, Boolean skipJournaledProperties, Object rootObject, XamlAccessLevel accessLevel, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at ExamBuilder.UI.Views.MainWindow.InitializeComponent() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml:line 1
   at ExamBuilder.UI.Views.MainWindow..ctor() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml.cs:line 18
   at ExamBuilder.UI.Views.LoginWindow.PerformLogin() in D:\mr\qqqq\ExamBuilder.UI\Views\LoginWindow.xaml.cs:line 102
2025-05-25 13:32:14.062 +03:00 [INF] تم تسجيل دخول المستخدم: جو
2025-05-25 13:32:24.973 +03:00 [INF] محاولة تسجيل دخول - المستخدم: 'جو', كلمة المرور: 'جو'
2025-05-25 13:32:24.973 +03:00 [INF] تحقق المستخدم: True, تحقق كلمة المرور: True
2025-05-25 13:32:24.973 +03:00 [INF] تم التحقق من البيانات بنجاح
2025-05-25 13:32:28.383 +03:00 [INF] محاولة فتح النافذة الرئيسية
2025-05-25 13:32:28.387 +03:00 [ERR] خطأ في فتح النافذة الرئيسية
System.Windows.Markup.XamlParseException: Provide value on 'System.Windows.Baml2006.TypeConverterMarkupExtension' threw an exception.
 ---> System.FormatException: Settings is not a valid value for PackIconMaterialKind.
 ---> System.ArgumentException: Requested value 'Settings' was not found.
   at System.Enum.TryParseByName[TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TStorage& result)
   at System.Enum.TryParseByValueOrName[TUnderlying,TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TUnderlying& result)
   at System.Enum.TryParse(Type enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, Object& result)
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   --- End of inner exception stack trace ---
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   at MS.Internal.Xaml.Runtime.ClrObjectRuntime.CallProvideValue(MarkupExtension me, IServiceProvider serviceProvider)
   --- End of inner exception stack trace ---
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.LoadBaml(XamlReader xamlReader, Boolean skipJournaledProperties, Object rootObject, XamlAccessLevel accessLevel, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at ExamBuilder.UI.Views.MainWindow.InitializeComponent() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml:line 1
   at ExamBuilder.UI.Views.MainWindow..ctor() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml.cs:line 18
   at ExamBuilder.UI.Views.LoginWindow.PerformLogin() in D:\mr\qqqq\ExamBuilder.UI\Views\LoginWindow.xaml.cs:line 102
2025-05-25 13:32:28.388 +03:00 [INF] تم تسجيل دخول المستخدم: جو
2025-05-25 13:32:40.295 +03:00 [INF] محاولة تسجيل دخول - المستخدم: 'جو', كلمة المرور: 'جو'
2025-05-25 13:32:40.295 +03:00 [INF] تحقق المستخدم: True, تحقق كلمة المرور: True
2025-05-25 13:32:40.295 +03:00 [INF] تم التحقق من البيانات بنجاح
2025-05-25 13:32:44.245 +03:00 [INF] محاولة فتح النافذة الرئيسية
2025-05-25 13:32:44.249 +03:00 [ERR] خطأ في فتح النافذة الرئيسية
System.Windows.Markup.XamlParseException: Provide value on 'System.Windows.Baml2006.TypeConverterMarkupExtension' threw an exception.
 ---> System.FormatException: Settings is not a valid value for PackIconMaterialKind.
 ---> System.ArgumentException: Requested value 'Settings' was not found.
   at System.Enum.TryParseByName[TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TStorage& result)
   at System.Enum.TryParseByValueOrName[TUnderlying,TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TUnderlying& result)
   at System.Enum.TryParse(Type enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, Object& result)
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   --- End of inner exception stack trace ---
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   at MS.Internal.Xaml.Runtime.ClrObjectRuntime.CallProvideValue(MarkupExtension me, IServiceProvider serviceProvider)
   --- End of inner exception stack trace ---
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.LoadBaml(XamlReader xamlReader, Boolean skipJournaledProperties, Object rootObject, XamlAccessLevel accessLevel, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at ExamBuilder.UI.Views.MainWindow.InitializeComponent() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml:line 1
   at ExamBuilder.UI.Views.MainWindow..ctor() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml.cs:line 18
   at ExamBuilder.UI.Views.LoginWindow.PerformLogin() in D:\mr\qqqq\ExamBuilder.UI\Views\LoginWindow.xaml.cs:line 102
2025-05-25 13:32:44.249 +03:00 [INF] تم تسجيل دخول المستخدم: جو
