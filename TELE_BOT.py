import os
import uuid
import requests
import socket
import uuid as uuid_lib
from time import sleep

# Telegram Bot Token and Chat ID
import os

BOT_TOKEN = os.getenv("TELEGRAM_BOT_TOKEN", "default_token")
CHAT_ID = os.getenv("TELEGRAM_CHAT_ID", "default_chat_id")

# Get MAC address as device ID
def get_device_id():
    mac = uuid_lib.getnode()
    mac_address = ':'.join(("%012X" % mac)[i:i+2] for i in range(0, 12, 2))
    return mac_address

# Send a message to the Telegram bot
def send_telegram_message(message):
    url = f"https://api.telegram.org/bot{BOT_TOKEN}/sendMessage"
    payload = {"chat_id": CHAT_ID, "text": message}
    response = requests.post(url, json=payload)

    if not response.ok:
        print("فشل في إرسال الرسالة.")
        print("Status Code:", response.status_code)
        print("Response Text:", response.text)
    else:
        print("تم إرسال الرسالة بنجاح.")

    return response.ok

# Check for approval from Telegram bot
def wait_for_approval():
    url = f"https://api.telegram.org/bot{BOT_TOKEN}/getUpdates"
    last_update_id = None

    timeout = 300  # Timeout in seconds
    start_time = time.time()

    while time.time() - start_time < timeout:
        try:
            response = requests.get(url, timeout=10)
        if response.ok:
            updates = response.json().get("result", [])
            for update in updates:
                update_id = update["update_id"]
                if last_update_id is None or update_id > last_update_id:
                    last_update_id = update_id
                    message = update.get("message", {}).get("text", "")
                    if message.lower() == "موافق":
                        return True
                    elif message.lower() == "مرفوض":
                        return False
        except requests.RequestException as e:
            print(f"Error fetching updates: {e}")
            sleep(5)
    print("Approval timeout reached.")
    return False

# Main logic
def main():
    device_id = get_device_id()
    approval_file = "approval.txt"

    if os.path.exists(approval_file):
        with open(approval_file, "r") as file:
            status = file.read().strip()
            if status == "approved":
                print("تمت الموافقة مسبقًا. يتم تشغيل البرنامج...")
                # منطق البرنامج هنا
                return
            elif status == "rejected":
                print("تم رفض هذا الجهاز من قبل. لا يمكن تشغيل البرنامج.")
                exit()

    # أول تشغيل
    message = f"جهاز جديد بيحاول يشتغل:\nMAC Address: {device_id}\n\nلو موافق اكتب 'موافق'، لو لأ اكتب 'مرفوض'."
    if send_telegram_message(message):
        print("تم إرسال طلب الإذن. انتظر الرد...")
        approved = wait_for_approval()
        if approved:
            print("تمت الموافقة. يتم تشغيل البرنامج...")
            with open(approval_file, "w") as file:
                file.write("approved")
            # منطق البرنامج هنا
        else:
            print("تم الرفض. لا يمكن تشغيل البرنامج.")
            with open(approval_file, "w") as file:
                file.write("rejected")
            exit()
    else:
        print("فشل في إرسال الرسالة إلى Telegram. تأكد من صحة الإعدادات.")

if __name__ == "__main__":
    main()
