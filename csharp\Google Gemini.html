<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام تأمين الفيديوهات التعليمية</title>
    
    <!-- Tailwind CSS for styling -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Google Fonts: Cairo for Arabic UI -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
        }
        .hidden-view {
            display: none;
        }
        #video-player::-webkit-media-controls-enclosure {
            overflow: hidden;
        }
        #video-player::-webkit-media-controls-panel {
            width: calc(100% + 30px);
        }
        .qr-code-item {
            page-break-inside: avoid;
        }
        @media print {
            body * {
                visibility: hidden;
            }
            #qrcodes-grid, #qrcodes-grid * {
                visibility: visible;
            }
            #qrcodes-grid {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
            }
        }
    </style>
</head>
<body class="bg-gray-100 text-gray-800">

    <div class="container mx-auto p-4 md:p-8 max-w-4xl">
        
        <!-- Global Alert Message -->
        <div id="alert-container" class="fixed top-5 right-5 z-50 w-72"></div>

        <!-- Teacher View: URL Input and QR Code Generation -->
        <div id="teacher-view">
            <div class="bg-white p-6 md:p-8 rounded-2xl shadow-lg">
                <h1 class="text-2xl md:text-3xl font-bold text-center text-indigo-600 mb-2">منصة المعلم</h1>
                <p class="text-center text-gray-500 mb-6">حوّل رابط الفيديو إلى أكواد QR متعددة، كل كود لجهاز واحد.</p>

                <div class="space-y-4">
                    <div>
                        <label for="video-name" class="block mb-2 text-sm font-medium text-gray-700">اسم الفيديو (للتنظيم)</label>
                        <input type="text" id="video-name" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-indigo-500 focus:border-indigo-500 block w-full p-2.5" placeholder="مثال: الدرس الأول في الفيزياء" required>
                    </div>
                    <div>
                        <label for="video-url" class="block mb-2 text-sm font-medium text-gray-700">رابط الفيديو (URL)</label>
                        <input type="url" id="video-url" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-indigo-500 focus:border-indigo-500 block w-full p-2.5" placeholder="https://www.youtube.com/watch?v=..." required>
                        <p class="mt-2 text-xs text-gray-500">ضع هنا رابط فيديو يوتيوب (غير مدرج) أو أي رابط مباشر آخر.</p>
                    </div>
                    <div>
                        <label for="code-quantity" class="block mb-2 text-sm font-medium text-gray-700">عدد الأكواد المطلوب إنشاؤها</label>
                        <input type="number" id="code-quantity" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-indigo-500 focus:border-indigo-500 block w-full p-2.5" value="1" min="1" max="100" required>
                    </div>
                </div>

                <button id="generate-btn" class="mt-6 w-full text-white bg-indigo-600 hover:bg-indigo-700 focus:ring-4 focus:outline-none focus:ring-indigo-300 font-medium rounded-lg text-lg px-5 py-3 text-center transition-transform transform hover:scale-105">
                    توليد الأكواد
                </button>

                <div id="loading-spinner" class="hidden-view text-center mt-4">
                    <svg class="animate-spin h-8 w-8 text-indigo-600 mx-auto" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <p id="loading-text" class="mt-2 text-gray-600">جاري الإنشاء...</p>
                </div>

                <div id="multiple-results-view" class="hidden-view mt-8">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-xl font-bold text-gray-800">تم إنشاء الأكواد بنجاح!</h2>
                        <button id="print-btn" class="text-white bg-green-600 hover:bg-green-700 font-medium rounded-lg text-sm px-5 py-2.5 flex items-center space-x-2 space-x-reverse">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M5 4v3H4a2 2 0 00-2 2v6a2 2 0 002 2h12a2 2 0 002-2V9a2 2 0 00-2-2h-1V4a2 2 0 00-2-2H7a2 2 0 00-2 2zm8 0H7v3h6V4zm0 8H7V9h6v3z" clip-rule="evenodd"></path></svg>
                            <span>طباعة الأكواد</span>
                        </button>
                    </div>
                    <p class="mb-4 text-gray-600">اطبع هذه الصفحة وقم بتوزيع الأكواد على طلابك. كل كود مخصص لطالب واحد فقط.</p>
                    <div id="qrcodes-grid" class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 p-4 border rounded-lg bg-gray-50">
                        <!-- QR Codes will be injected here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Student View: Video Player -->
        <div id="student-view" class="hidden-view">
             <div id="player-container" class="hidden-view bg-white p-4 md:p-6 rounded-2xl shadow-lg">
                <h1 id="video-title-player" class="text-2xl font-bold text-center mb-4"></h1>
                <div class="aspect-w-16 aspect-h-9 bg-black rounded-lg overflow-hidden">
                    <video id="video-player" class="w-full h-full" controls controlsList="nodownload" oncontextmenu="return false;"></video>
                </div>
                 <p class="text-xs text-center mt-2 text-gray-400">المشاهدة مقتصرة على هذا الجهاز فقط.</p>
                 <a href="/" class="mt-4 inline-block text-indigo-600 hover:underline">&larr; العودة للصفحة الرئيسية</a>
            </div>
            <div id="auth-container" class="text-center bg-white p-8 rounded-2xl shadow-lg">
                 <div id="auth-loading" class="text-center">
                    <svg class="animate-spin h-10 w-10 text-indigo-600 mx-auto" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>
                    <p class="mt-4 text-lg text-gray-700">جاري التحقق من صلاحية الجهاز...</p>
                </div>
                <div id="auth-error" class="hidden-view">
                     <div class="mx-auto w-16 h-16 flex items-center justify-center bg-red-100 rounded-full mb-4"><svg class="w-10 h-10 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636"></path></svg></div>
                     <h2 class="text-2xl font-bold text-red-600">الجهاز غير مصرح له</h2>
                     <p class="text-gray-600 mt-2">هذا الفيديو تم ربطه بجهاز آخر. لا يمكنك مشاهدته من هذا الجهاز.</p>
                     <a href="/" class="mt-6 inline-block text-indigo-600 hover:underline">&larr; العودة للصفحة الرئيسية</a>
                </div>
            </div>
        </div>

    </div>

    <!-- QR Code generation library -->
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>

    <!-- Firebase SDK -->
    <script type="module">
        /*
         * ===============================================================
         * ملاحظات هامة لتشخيص الأخطاء (حسب إرشاداتك)
         * ===============================================================
         * 1. إعدادات Firebase:
         * تأكد من استبدال القيم أدناه بالقيم الصحيحة من مشروعك على Firebase.
         * إذا بقيت "YOUR_API_KEY"، لن يعمل الاتصال.
         *
         * 2. قواعد Firestore:
         * للتجربة، يمكنك استخدام القواعد التالية في صفحة Rules في Firestore
         * للسماح بالقراءة والكتابة للجميع. تذكر تأمينها لاحقاً.
         *
         * service cloud.firestore {
         * match /databases/{database}/documents {
         * match /artifacts/{appId}/public/data/videos/{videoId} {
         * allow read, write: if true;
         * }
         * }
         * }
         * ===============================================================
        */

        const firebaseConfig = {
            apiKey: "YOUR_API_KEY",
            authDomain: "YOUR_AUTH_DOMAIN",
            projectId: "YOUR_PROJECT_ID",
            storageBucket: "YOUR_STORAGE_BUCKET",
            messagingSenderId: "YOUR_MESSAGING_SENDER_ID",
            appId: "YOUR_APP_ID"
        };
        const finalFirebaseConfig = typeof __firebase_config !== 'undefined' ? JSON.parse(__firebase_config) : firebaseConfig;

        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-app.js";
        import { getFirestore, doc, getDoc, setDoc, collection, addDoc, serverTimestamp, writeBatch } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-firestore.js";
        import { getAuth, signInAnonymously, onAuthStateChanged, signInWithCustomToken } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-auth.js";

        const app = initializeApp(finalFirebaseConfig);
        const db = getFirestore(app);
        const auth = getAuth(app);
        const appId = typeof __app_id !== 'undefined' ? __app_id : 'secure-video-default';

        // --- DOM Elements ---
        const teacherView = document.getElementById('teacher-view');
        const studentView = document.getElementById('student-view');
        const generateBtn = document.getElementById('generate-btn');
        const videoNameInput = document.getElementById('video-name');
        const videoUrlInput = document.getElementById('video-url');
        const codeQuantityInput = document.getElementById('code-quantity');
        const loadingSpinner = document.getElementById('loading-spinner');
        const loadingText = document.getElementById('loading-text');

        const multipleResultsView = document.getElementById('multiple-results-view');
        const qrcodesGrid = document.getElementById('qrcodes-grid');
        const printBtn = document.getElementById('print-btn');

        const playerContainer = document.getElementById('player-container');
        const authContainer = document.getElementById('auth-container');
        const authLoading = document.getElementById('auth-loading');
        const authError = document.getElementById('auth-error');
        const videoPlayer = document.getElementById('video-player');
        const videoTitlePlayer = document.getElementById('video-title-player');
        const alertContainer = document.getElementById('alert-container');

        function showAlert(message) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `p-4 mb-4 text-sm rounded-lg shadow bg-red-100 text-red-800`;
            alertDiv.role = 'alert';
            alertDiv.innerHTML = `<span class="font-medium">${message}</span>`;
            alertContainer.appendChild(alertDiv);
            setTimeout(() => {
                alertDiv.remove();
            }, 4000);
        }

        function getDeviceFingerprint() {
            let deviceId = localStorage.getItem('device_unique_id');
            if (!deviceId) {
                deviceId = crypto.randomUUID();
                localStorage.setItem('device_unique_id', deviceId);
            }
            return deviceId;
        }

        async function handleRouting() {
            const urlParams = new URLSearchParams(window.location.search);
            const videoId = urlParams.get('videoId');

            if (videoId) {
                teacherView.classList.add('hidden-view');
                studentView.classList.remove('hidden-view');
                authContainer.classList.remove('hidden-view');
                playerContainer.classList.add('hidden-view');
                await initializeViewer(videoId);
            } else {
                teacherView.classList.remove('hidden-view');
                studentView.classList.add('hidden-view');
            }
        }
        
        async function authenticateUser() {
             try {
                if (auth.currentUser) return;
                if (typeof __initial_auth_token !== 'undefined' && __initial_auth_token) {
                    await signInWithCustomToken(auth, __initial_auth_token);
                } else {
                    await signInAnonymously(auth);
                }
            } catch (error) {
                console.error("Authentication Error:", error);
                showAlert("فشل الاتصال بالخادم. يرجى تحديث الصفحة.");
            }
        }
        
        async function generateSecureLinks() {
            console.log('بدء توليد الأكواد'); // -- تمت الإضافة حسب طلبك للتشخيص --
            
            const videoName = videoNameInput.value.trim();
            const videoUrl = videoUrlInput.value.trim();
            const quantity = parseInt(codeQuantityInput.value, 10);

            if (!videoName || !videoUrl) {
                showAlert('الرجاء إدخال اسم ورابط الفيديو.');
                return;
            }
             if (isNaN(quantity) || quantity < 1 || quantity > 100) {
                showAlert('الرجاء إدخال عدد صحيح بين 1 و 100.');
                return;
            }
            try {
                new URL(videoUrl);
            } catch (_) {
                showAlert('الرجاء إدخال رابط URL صحيح.');
                return;
            }

            generateBtn.disabled = true;
            generateBtn.textContent = 'جاري الإنشاء...';
            loadingSpinner.classList.remove('hidden-view');
            multipleResultsView.classList.add('hidden-view');
            qrcodesGrid.innerHTML = '';

            const videoDocs = [];
            for (let i = 0; i < quantity; i++) {
                videoDocs.push({
                    videoName: videoName,
                    videoUrl: videoUrl,
                    deviceId: null,
                    createdAt: serverTimestamp()
                });
            }

            try {
                const batch = writeBatch(db);
                const generatedIds = [];
                 const videoCollectionRef = collection(db, `artifacts/${appId}/public/data/videos`);

                for(const videoData of videoDocs) {
                    const docRef = doc(videoCollectionRef); // Create a new doc with a unique ID
                    batch.set(docRef, videoData);
                    generatedIds.push(docRef.id);
                }

                await batch.commit();
                console.log('تم حفظ البيانات في Firestore بنجاح.');

                for (let i = 0; i < generatedIds.length; i++) {
                    const videoId = generatedIds[i];
                    const newUrl = `${window.location.origin}${window.location.pathname}?videoId=${videoId}`;
                    
                    const qrItem = document.createElement('div');
                    qrItem.className = 'qr-code-item flex flex-col items-center p-2 border rounded-md bg-white';
                    
                    const canvas = document.createElement('canvas');
                    qrItem.appendChild(canvas);
                    
                    const label = document.createElement('p');
                    label.className = 'text-xs mt-1';
                    label.textContent = `الكود رقم ${i + 1}`;
                    qrItem.appendChild(label);
                    
                    qrcodesGrid.appendChild(qrItem);
                    
                    QRCode.toCanvas(canvas, newUrl, { width: 128, errorCorrectionLevel: 'H' });
                }

                multipleResultsView.classList.remove('hidden-view');

            } catch (error) {
                console.error("Error creating secure links: ", error);
                showAlert('حدث خطأ أثناء إنشاء الروابط. يرجى مراجعة Console المتصفح.');
            } finally {
                generateBtn.disabled = false;
                generateBtn.textContent = 'توليد الأكواد';
                loadingSpinner.classList.add('hidden-view');
            }
        }

        async function initializeViewer(videoId) {
            const currentDeviceFingerprint = getDeviceFingerprint();
            try {
                const videoDocRef = doc(db, `artifacts/${appId}/public/data/videos`, videoId);
                const docSnap = await getDoc(videoDocRef);

                if (!docSnap.exists()) {
                    return showViewerError('رابط الفيديو غير صالح أو تم حذفه.');
                }
                const videoData = docSnap.data();
                const registeredDeviceId = videoData.deviceId;
                if (!registeredDeviceId) {
                    await setDoc(videoDocRef, { deviceId: currentDeviceFingerprint }, { merge: true });
                    playVideo(videoData);
                } else if (registeredDeviceId === currentDeviceFingerprint) {
                    playVideo(videoData);
                } else {
                    showViewerError('هذا الفيديو تم ربطه بجهاز آخر. لا يمكنك مشاهدته من هذا الجهاز.');
                }
            } catch (error) {
                console.error("Error verifying device:", error);
                showViewerError('حدث خطأ أثناء التحقق. الرجاء تحديث الصفحة والمحاولة مرة أخرى.');
            }
        }
        
        function showViewerError(message) {
            authLoading.classList.add('hidden-view');
            authError.classList.remove('hidden-view');
            authError.querySelector('p').textContent = message;
        }

        function playVideo(videoData) {
            authContainer.classList.add('hidden-view');
            playerContainer.classList.remove('hidden-view');
            videoTitlePlayer.textContent = videoData.videoName;
            
            try {
                const url = new URL(videoData.videoUrl);
                if (url.hostname.includes('youtube.com') || url.hostname.includes('youtu.be')) {
                    const videoId = url.searchParams.get('v') || url.pathname.split('/').pop();
                    const embedUrl = `https://www.youtube.com/embed/${videoId}?autoplay=1&rel=0`;
                    const iframe = document.createElement('iframe');
                    iframe.src = embedUrl;
                    iframe.className = "w-full h-full";
                    iframe.setAttribute('frameborder', '0');
                    iframe.setAttribute('allow', 'accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture');
                    iframe.setAttribute('allowfullscreen', true);
                    videoPlayer.replaceWith(iframe);
                } else {
                     videoPlayer.src = videoData.videoUrl;
                     videoPlayer.play().catch(() => console.log("Autoplay prevented."));
                }
            } catch (e) {
                 videoPlayer.src = videoData.videoUrl;
                 videoPlayer.play().catch(() => console.log("Autoplay prevented."));
            }
        }
        
        async function main() {
            await authenticateUser();
            handleRouting();
        }

        generateBtn.addEventListener('click', generateSecureLinks);
        printBtn.addEventListener('click', () => window.print());
        
        main();

    </script>
</body>
</html>
