# برنامج إنشاء الامتحانات - المهندس يوسف غنام

## نظرة عامة

برنامج احترافي لإنشاء وإدارة الامتحانات مع دعم كامل للغة العربية واتجاه RTL. تم تطويره باستخدام C# و WPF لتوفير أداء ممتاز وواجهة مستخدم حديثة.

## المميزات الرئيسية

### 🎯 الوظائف الأساسية
- إنشاء وتحرير الامتحانات بسهولة
- إدارة الأسئلة مع أنواع متعددة (اختيار من متعدد، صح/خطأ، إجابة قصيرة، إلخ)
- تصدير الامتحانات بصيغة PDF عالية الجودة
- دعم كامل للغة العربية واتجاه RTL
- واجهة مستخدم احترافية وجذابة

### 🔒 الأمان والحماية
- نظام تسجيل دخول آمن
- تشفير البيانات الحساسة
- نظام تصريح الأجهزة المتقدم
- التحكم عن بعد عبر Telegram Bot
- حماية من الاستخدام غير المصرح به

### 🎨 التخصيص والثيمات
- ثلاثة ثيمات مختلفة (أحمر، أخضر، داكن)
- تخصيص الخطوط والألوان
- إعدادات متقدمة للتخطيط والتصميم
- دعم الشعارات والإطارات المخصصة

### 📱 التكامل مع Telegram
- التحكم عن بعد في البرنامج
- إشعارات فورية للأحداث المهمة
- إدارة تصاريح الأجهزة عن بعد
- مراقبة حالة النظام

## متطلبات النظام

### الحد الأدنى
- Windows 10 أو أحدث
- .NET 8.0 Runtime
- 4 GB RAM
- 500 MB مساحة تخزين
- دقة شاشة 1024x768

### المستحسن
- Windows 11
- 8 GB RAM أو أكثر
- 2 GB مساحة تخزين
- دقة شاشة 1920x1080 أو أعلى

## التشغيل السريع

### الطريقة الأسهل:
1. تأكد من تثبيت .NET 8.0 SDK
2. شغل الملف `run_final.bat`
3. استخدم بيانات الدخول:
   - اسم المستخدم: `جو`
   - كلمة المرور: `جو`

### التشغيل اليدوي:
```bash
dotnet restore
dotnet build --configuration Release
copy ExamBuilder.UI\appsettings.json ExamBuilder.UI\bin\Release\net8.0-windows\
cd ExamBuilder.UI\bin\Release\net8.0-windows
dotnet ExamBuilder.UI.dll
```

## التثبيت والتشغيل التفصيلي

### 1. تحميل المتطلبات
```bash
# تثبيت .NET 8.0 SDK
winget install Microsoft.DotNet.SDK.8

# أو تحميل من الموقع الرسمي
# https://dotnet.microsoft.com/download/dotnet/8.0
```

### 2. بناء المشروع
```bash
# استنساخ المشروع
git clone [repository-url]
cd ExamBuilder

# استعادة الحزم
dotnet restore

# بناء المشروع
dotnet build --configuration Release

# تشغيل التطبيق
dotnet run --project ExamBuilder.UI
```

### 3. إعداد قاعدة البيانات
سيتم إنشاء قاعدة البيانات تلقائياً عند التشغيل الأول.

### 4. إعداد Telegram Bot (اختياري)
1. إنشاء بوت جديد عبر @BotFather في Telegram
2. الحصول على Bot Token
3. تحديث الإعدادات في `appsettings.json`

## الاستخدام

### تسجيل الدخول
- اسم المستخدم: `جو`
- كلمة المرور: `جو`

### إنشاء امتحان جديد
1. انقر على "إنشاء امتحان جديد"
2. أدخل تفاصيل الامتحان
3. أضف الأسئلة
4. اختر الإعدادات المطلوبة
5. صدّر الامتحان كـ PDF

### تغيير الثيم
- انقر على أزرار الثيمات في أسفل نافذة تسجيل الدخول
- أو من قائمة الإعدادات في التطبيق الرئيسي

## هيكل المشروع

```
ExamBuilder/
├── ExamBuilder.Core/          # منطق العمل الأساسي
├── ExamBuilder.UI/            # واجهة المستخدم WPF
├── ExamBuilder.Data/          # طبقة البيانات
├── ExamBuilder.Security/      # نظام الأمان
├── ExamBuilder.PDF/           # خدمات PDF
├── ExamBuilder.Telegram/      # تكامل Telegram
└── ExamBuilder.Tests/         # الاختبارات
```

## التقنيات المستخدمة

- **Frontend**: WPF, MahApps.Metro, Material Design
- **Backend**: .NET 8.0, Entity Framework Core
- **Database**: SQLite
- **Security**: BCrypt, AES Encryption
- **PDF Generation**: iTextSharp/ReportLab
- **Telegram**: Telegram.Bot API
- **Logging**: Serilog

## الإعدادات

### ملف appsettings.json
يحتوي على جميع إعدادات التطبيق:
- إعدادات الأمان والتشفير
- إعدادات Telegram Bot
- إعدادات قاعدة البيانات
- إعدادات PDF والتصدير
- إعدادات واجهة المستخدم

### مجلدات البيانات
- `%AppData%/ExamBuilder/` - بيانات التطبيق
- `Generated_PDFs/` - ملفات PDF المُنشأة
- `Backups/` - النسخ الاحتياطية
- `Logs/` - ملفات السجلات

## الأمان

### تشفير البيانات
- تشفير AES-256 للبيانات الحساسة
- تشفير BCrypt لكلمات المرور
- تشفير SHA-256 للتحقق من التكامل

### نظام تصريح الأجهزة
- تسجيل الأجهزة بناءً على MAC Address
- نظام موافقة يدوية للأجهزة الجديدة
- إمكانية حظر الأجهزة عن بعد

## استكشاف الأخطاء

### مشاكل شائعة

#### 1. فشل في تسجيل الدخول
- تأكد من صحة اسم المستخدم وكلمة المرور
- تحقق من تصريح الجهاز

#### 2. مشاكل في إنشاء PDF
- تأكد من وجود مساحة كافية على القرص
- تحقق من صلاحيات الكتابة في مجلد الإخراج

#### 3. مشاكل Telegram Bot
- تحقق من صحة Bot Token و Chat ID
- تأكد من الاتصال بالإنترنت

### ملفات السجلات
تحقق من ملفات السجلات في مجلد `Logs/` لمزيد من التفاصيل حول الأخطاء.

## المساهمة

نرحب بالمساهمات! يرجى:
1. إنشاء Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

## الترخيص

© 2024 المهندس يوسف غنام - جميع الحقوق محفوظة

## التواصل

- **المطور**: المهندس يوسف غنام
- **WhatsApp**: [رابط WhatsApp]
- **Facebook**: [رابط Facebook]
- **Instagram**: [رابط Instagram]

## الإصدارات

### الإصدار 2.0.0 (الحالي)
- إعادة كتابة كاملة بـ C# WPF
- واجهة مستخدم محسنة
- أداء أفضل
- ميزات أمان متقدمة
- دعم محسن للغة العربية

### الإصدار 1.x (Python)
- الإصدار الأصلي بـ Python و CustomTkinter
- الوظائف الأساسية لإنشاء الامتحانات

---

**ملاحظة**: هذا البرنامج مصمم خصيصاً للمعلمين والمؤسسات التعليمية لتسهيل عملية إنشاء وإدارة الامتحانات بطريقة احترافية وآمنة.
