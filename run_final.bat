@echo off
chcp 65001 > nul
echo ========================================
echo    برنامج إنشاء الامتحانات
echo    المهندس يوسف غنام
echo ========================================
echo.

echo جارٍ التحقق من متطلبات النظام...

:: التحقق من وجود .NET 8.0
dotnet --version > nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: .NET 8.0 غير مثبت على النظام
    echo يرجى تثبيت .NET 8.0 SDK من:
    echo https://dotnet.microsoft.com/download/dotnet/8.0
    pause
    exit /b 1
)

echo تم العثور على .NET: 
dotnet --version

echo.
echo جارٍ استعادة الحزم...
dotnet restore

if %errorlevel% neq 0 (
    echo خطأ في استعادة الحزم
    pause
    exit /b 1
)

echo.
echo جارٍ بناء المشروع...
dotnet build --configuration Release

if %errorlevel% neq 0 (
    echo خطأ في بناء المشروع
    pause
    exit /b 1
)

echo.
echo جارٍ نسخ ملفات الإعدادات...
copy ExamBuilder.UI\appsettings.json ExamBuilder.UI\bin\Release\net8.0-windows\ > nul

echo.
echo جارٍ تشغيل التطبيق...
echo.
echo بيانات تسجيل الدخول:
echo اسم المستخدم: جو
echo كلمة المرور: جو
echo.

cd ExamBuilder.UI\bin\Release\net8.0-windows
dotnet ExamBuilder.UI.dll

if %errorlevel% neq 0 (
    echo خطأ في تشغيل التطبيق
    pause
    exit /b 1
)

echo.
echo تم إغلاق التطبيق بنجاح
pause
