using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Input;
using Microsoft.Extensions.Logging;
using ExamBuilder.Security.Services;

namespace ExamBuilder.UI.ViewModels
{
    /// <summary>
    /// ViewModel لنافذة تسجيل الدخول
    /// </summary>
    public class LoginViewModel : INotifyPropertyChanged
    {
        private readonly IAuthenticationService _authenticationService;
        private readonly IDeviceAuthorizationService _deviceAuthorizationService;
        private readonly ILogger<LoginViewModel> _logger;

        private string _username = "جو";
        private string _password = string.Empty;
        private string _statusMessage = string.Empty;
        private bool _isLoading = false;
        private bool _isLoginEnabled = true;

        public LoginViewModel(
            IAuthenticationService authenticationService,
            IDeviceAuthorizationService deviceAuthorizationService,
            ILogger<LoginViewModel> logger)
        {
            _authenticationService = authenticationService;
            _deviceAuthorizationService = deviceAuthorizationService;
            _logger = logger;

            LoginCommand = new RelayCommand(async () => await LoginAsync(), () => IsLoginEnabled && !IsLoading);
            ExitCommand = new RelayCommand(() => System.Windows.Application.Current.Shutdown());
        }

        #region Properties

        public string Username
        {
            get => _username;
            set
            {
                if (SetProperty(ref _username, value))
                {
                    ((RelayCommand)LoginCommand).RaiseCanExecuteChanged();
                }
            }
        }

        public string Password
        {
            get => _password;
            set
            {
                if (SetProperty(ref _password, value))
                {
                    ((RelayCommand)LoginCommand).RaiseCanExecuteChanged();
                }
            }
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                if (SetProperty(ref _isLoading, value))
                {
                    ((RelayCommand)LoginCommand).RaiseCanExecuteChanged();
                }
            }
        }

        public bool IsLoginEnabled
        {
            get => _isLoginEnabled;
            set
            {
                if (SetProperty(ref _isLoginEnabled, value))
                {
                    ((RelayCommand)LoginCommand).RaiseCanExecuteChanged();
                }
            }
        }

        #endregion

        #region Commands

        public ICommand LoginCommand { get; }
        public ICommand ExitCommand { get; }

        #endregion

        #region Methods

        private async Task LoginAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "جارٍ التحقق من بيانات تسجيل الدخول...";

                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(Username) || string.IsNullOrWhiteSpace(Password))
                {
                    StatusMessage = "يرجى إدخال اسم المستخدم وكلمة المرور";
                    return;
                }

                // التحقق من تصريح الجهاز
                StatusMessage = "جارٍ التحقق من تصريح الجهاز...";
                var deviceAuthResult = await _deviceAuthorizationService.CheckDeviceAuthorizationAsync();

                if (!deviceAuthResult.IsAuthorized)
                {
                    if (deviceAuthResult.RequiresApproval)
                    {
                        StatusMessage = "تم إرسال طلب موافقة. يرجى الانتظار...";

                        var approvalResult = await _deviceAuthorizationService.WaitForApprovalAsync();

                        if (!approvalResult.IsApproved)
                        {
                            StatusMessage = "تم رفض الطلب أو انتهت مهلة الانتظار";
                            return;
                        }
                    }
                    else
                    {
                        StatusMessage = "هذا الجهاز محظور من استخدام التطبيق";
                        return;
                    }
                }

                // محاولة تسجيل الدخول
                StatusMessage = "جارٍ تسجيل الدخول...";
                var loginResult = await _authenticationService.AuthenticateAsync(Username, Password);

                if (loginResult.IsSuccess)
                {
                    StatusMessage = "تم تسجيل الدخول بنجاح";
                    _logger.LogInformation($"تم تسجيل دخول المستخدم: {Username}");

                    // إثارة حدث نجح تسجيل الدخول
                    OnLoginSuccessful?.Invoke(loginResult);
                }
                else
                {
                    StatusMessage = loginResult.ErrorMessage ?? "فشل في تسجيل الدخول";
                    _logger.LogWarning($"فشل في تسجيل الدخول للمستخدم: {Username}");
                }
            }
            catch (Exception ex)
            {
                StatusMessage = "حدث خطأ أثناء تسجيل الدخول";
                _logger.LogError(ex, "خطأ في عملية تسجيل الدخول");
            }
            finally
            {
                IsLoading = false;
            }
        }

        #endregion

        #region Events

        public event Action<AuthenticationResult>? OnLoginSuccessful;

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value))
                return false;

            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        #endregion
    }

    /// <summary>
    /// RelayCommand implementation
    /// </summary>
    public class RelayCommand : ICommand
    {
        private readonly Action _execute;
        private readonly Func<bool>? _canExecute;

        public RelayCommand(Action execute, Func<bool>? canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        public event EventHandler? CanExecuteChanged;

        public bool CanExecute(object? parameter)
        {
            return _canExecute?.Invoke() ?? true;
        }

        public void Execute(object? parameter)
        {
            _execute();
        }

        public void RaiseCanExecuteChanged()
        {
            CanExecuteChanged?.Invoke(this, EventArgs.Empty);
        }
    }

    /// <summary>
    /// RelayCommand with parameter
    /// </summary>
    public class RelayCommand<T> : ICommand
    {
        private readonly Action<T?> _execute;
        private readonly Func<T?, bool>? _canExecute;

        public RelayCommand(Action<T?> execute, Func<T?, bool>? canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        public event EventHandler? CanExecuteChanged;

        public bool CanExecute(object? parameter)
        {
            return _canExecute?.Invoke((T?)parameter) ?? true;
        }

        public void Execute(object? parameter)
        {
            _execute((T?)parameter);
        }

        public void RaiseCanExecuteChanged()
        {
            CanExecuteChanged?.Invoke(this, EventArgs.Empty);
        }
    }
}
