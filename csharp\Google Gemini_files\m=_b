"use strict";this.default_BardChatUi=this.default_BardChatUi||{};(function(_){var window=this;
try{
_._F_toggles_initialize=function(a){(typeof globalThis!=="undefined"?globalThis:typeof self!=="undefined"?self:this)._F_toggles_default_BardChatUi=a||[]};(0,_._F_toggles_initialize)([0x1021863f, 0x3bbb82cc, 0x3fff371d, 0x3ce4bcf, 0xa0, 0x118, ]);
/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var baa,ea,caa,gaa,ya,waa,Aaa,Ta,Caa,Ya,Gaa,Haa,Kaa,Oaa,Paa,Raa,Maa,$aa,aba,bba,cba,dba,Xaa,Yaa,mba,nba,oba,pba,tba,Yb,xba,Zb,<PERSON><PERSON>,zba,<PERSON><PERSON>,Hba,<PERSON>ba,Mba,Kba,Lba,Rba,Oba,Pba,qc,Sba,Wba,Xba,oc,Dc,fca,ica,lca,uca,rca,xca,zca,Aca,Cca,Dca,Oca,Pca,Zca,hda,jda,lda,Cd,sda,tda,wda,Pda,Qda,Rda,Sda,te,Tda,Zda,$da,bea,lea,nea,Fe,aaa,pea,qea,rea,Ie,sea,tea,uea,vea;_.aa=function(a){return function(){return aaa[a].apply(this,arguments)}};_.ba=function(a,b){return aaa[a]=b};
_.ca=function(a,b){if(Error.captureStackTrace)Error.captureStackTrace(this,_.ca);else{const c=Error().stack;c&&(this.stack=c)}a&&(this.message=String(a));b!==void 0&&(this.cause=b);this.ha=!0};baa=function(a,b){a=a.split("%s");let c="";const d=a.length-1;for(let e=0;e<d;e++)c+=a[e]+(e<b.length?b[e]:"%s");_.ca.call(this,c+a[d])};ea=function(){throw Error("r");};caa=function(a,b){b=String.fromCharCode.apply(null,b);return a==null?b:a+b};
_.faa=function(a){if(daa)a=(eaa||(eaa=new TextEncoder)).encode(a);else{let c=0;const d=new Uint8Array(3*a.length);for(let e=0;e<a.length;e++){var b=a.charCodeAt(e);if(b<128)d[c++]=b;else{if(b<2048)d[c++]=b>>6|192;else{if(b>=55296&&b<=57343){if(b<=56319&&e<a.length){const f=a.charCodeAt(++e);if(f>=56320&&f<=57343){b=(b-55296)*1024+f-56320+65536;d[c++]=b>>18|240;d[c++]=b>>12&63|128;d[c++]=b>>6&63|128;d[c++]=b&63|128;continue}else e--}b=65533}d[c++]=b>>12|224;d[c++]=b>>6&63|128}d[c++]=b&63|128}}a=c===
d.length?d:d.subarray(0,c)}return a};_.ja=function(a){_.ia.setTimeout(()=>{throw a;},0)};_.ka=function(a,b){return a.lastIndexOf(b,0)==0};_.la=function(a){return/^[\s\xa0]*$/.test(a)};_.ma=function(a,b){return a.indexOf(b)!=-1};_.oa=function(a){return _.ma(_.na().toLowerCase(),a.toLowerCase())};
_.haa=function(a,b){let c=0;a=String(a).trim().split(".");b=String(b).trim().split(".");const d=Math.max(a.length,b.length);for(let g=0;c==0&&g<d;g++){var e=a[g]||"",f=b[g]||"";do{e=/(\d*)(\D*)(.*)/.exec(e)||["","","",""];f=/(\d*)(\D*)(.*)/.exec(f)||["","","",""];if(e[0].length==0&&f[0].length==0)break;c=gaa(e[1].length==0?0:parseInt(e[1],10),f[1].length==0?0:parseInt(f[1],10))||gaa(e[2].length==0,f[2].length==0)||gaa(e[2],f[2]);e=e[3];f=f[3]}while(c==0)}return c};
gaa=function(a,b){return a<b?-1:a>b?1:0};_.na=function(){var a=_.ia.navigator;return a&&(a=a.userAgent)?a:""};_.iaa=function(a){if(!_.pa||!_.qa)return!1;for(let b=0;b<_.qa.brands.length;b++){const {brand:c}=_.qa.brands[b];if(c&&_.ma(c,a))return!0}return!1};_.ra=function(a){return _.ma(_.na(),a)};_.sa=function(){return _.pa?!!_.qa&&_.qa.brands.length>0:!1};_.jaa=function(){return _.sa()?!1:_.ra("Opera")};_.kaa=function(){return _.sa()?!1:_.ra("Trident")||_.ra("MSIE")};
_.laa=function(){return _.sa()?_.iaa("Microsoft Edge"):_.ra("Edg/")};_.ta=function(){return _.ra("Firefox")||_.ra("FxiOS")};_.xa=function(){return _.ra("Safari")&&!(_.wa()||(_.sa()?0:_.ra("Coast"))||_.jaa()||(_.sa()?0:_.ra("Edge"))||_.laa()||(_.sa()?_.iaa("Opera"):_.ra("OPR"))||_.ta()||_.ra("Silk")||_.ra("Android"))};_.wa=function(){return _.sa()?_.iaa("Chromium"):(_.ra("Chrome")||_.ra("CriOS"))&&!(_.sa()?0:_.ra("Edge"))||_.ra("Silk")};
_.maa=function(){return _.ra("Android")&&!(_.wa()||_.ta()||_.jaa()||_.ra("Silk"))};ya=function(a=!1){return a||_.pa?!!_.qa&&!!_.qa.platform:!1};_.naa=function(){return ya()?_.qa.platform==="Android":_.ra("Android")};_.oaa=function(){return _.ra("iPhone")&&!_.ra("iPod")&&!_.ra("iPad")};_.za=function(){return _.oaa()||_.ra("iPad")||_.ra("iPod")};_.Ba=function(){return ya()?_.qa.platform==="macOS":_.ra("Macintosh")};_.paa=function(){return ya()?_.qa.platform==="Linux":_.ra("Linux")};
_.qaa=function(){return ya()?_.qa.platform==="Windows":_.ra("Windows")};_.raa=function(){return ya()?_.qa.platform==="Chrome OS":_.ra("CrOS")};
_.saa=function(){var a=_.na(),b="";_.qaa()?(b=/Windows (?:NT|Phone) ([0-9.]+)/,b=(a=b.exec(a))?a[1]:"0.0"):_.za()?(b=/(?:iPhone|iPod|iPad|CPU)\s+OS\s+(\S+)/,b=(a=b.exec(a))&&a[1].replace(/_/g,".")):_.Ba()?(b=/Mac OS X ([0-9_.]+)/,b=(a=b.exec(a))?a[1].replace(/_/g,"."):"10"):_.oa("KaiOS")?(b=/(?:KaiOS)\/(\S+)/i,b=(a=b.exec(a))&&a[1]):_.naa()?(b=/Android\s+([^\);]+)(\)|;)/,b=(a=b.exec(a))&&a[1]):_.raa()&&(b=/(?:CrOS\s+(?:i686|x86_64)\s+([0-9.]+))/,b=(a=b.exec(a))&&a[1]);return b||""};
_.taa=function(a){return a[a.length-1]};_.Ca=function(a,b,c){b=_.uaa(a,b,c);return b<0?null:typeof a==="string"?a.charAt(b):a[b]};_.uaa=function(a,b,c){const d=a.length,e=typeof a==="string"?a.split(""):a;for(let f=0;f<d;f++)if(f in e&&b.call(c,e[f],f,a))return f;return-1};_.Fa=function(a,b){return _.Da(a,b)>=0};_.Ga=function(a,b){_.Fa(a,b)||a.push(b)};_.Ia=function(a,b){b=_.Da(a,b);let c;(c=b>=0)&&_.Ha(a,b);return c};_.Ha=function(a,b){Array.prototype.splice.call(a,b,1)};
_.Ja=function(a){const b=a.length;if(b>0){const c=Array(b);for(let d=0;d<b;d++)c[d]=a[d];return c}return[]};_.La=function(a,b){for(let c=1;c<arguments.length;c++){const d=arguments[c];if(_.Ka(d)){const e=a.length||0,f=d.length||0;a.length=e+f;for(let g=0;g<f;g++)a[e+g]=d[g]}else a.push(d)}};_.Ma=function(a,b,c){return arguments.length<=2?Array.prototype.slice.call(a,b):Array.prototype.slice.call(a,b,c)};
_.Na=function(a,b,c){b=b||a;var d=g=>g;c=c||d;let e=d=0;const f=new Set;for(;e<a.length;){const g=a[e++],h=c(g);f.has(h)||(f.add(h),b[d++]=g)}b.length=d};_.Oa=function(a,b,c,d){let e=0,f=a.length,g;for(;e<f;){const h=e+(f-e>>>1);let m;c?m=b.call(void 0,a[h],h,a):m=b(d,a[h]);m>0?e=h+1:(f=h,g=!m)}return g?e:-e-1};waa=function(a){return vaa[a]||""};_.yaa=function(a){a=xaa.test(a)?a.replace(xaa,waa):a;a=atob(a);const b=new Uint8Array(a.length);for(let c=0;c<a.length;c++)b[c]=a.charCodeAt(c);return b};
_.Pa=function(a){return a!=null&&a instanceof Uint8Array};_.zaa=function(a,b,c){a.__closure__error__context__984382||(a.__closure__error__context__984382={});a.__closure__error__context__984382[b]=c};Aaa=function(){const a=Error();_.zaa(a,"severity","incident");_.ja(a)};_.Qa=function(a){a=Error(a);_.zaa(a,"severity","warning");return a};_.Sa=function(a,b){if(a!=null){var c;var d=(c=Baa)!=null?c:Baa={};c=d[a]||0;c>=b||(d[a]=c+1,Aaa())}};
Ta=function(a,b=!1){return b&&Symbol.for&&a?Symbol.for(a):a!=null?Symbol(a):Symbol()};_.Va=function(a,b){a[_.Ua]|=b};Caa=function(a){if(4&a)return 512&a?512:1024&a?1024:0};_.Wa=function(a){_.Va(a,34);return a};_.Daa=function(a){_.Va(a,32);return a};Ya=function(){return typeof BigInt==="function"};_.cb=function(a){return a!=null&&a[_.Za]===_.$a};_.fb=function(a,b){return b===void 0?a.zc!==_.eb&&!!(2&(a.Be[_.Ua]|0)):!!(2&b)&&a.zc!==_.eb};_.gb=function(a,b){a.zc=b?_.eb:void 0};
_.jb=function(a,b,c){if(a==null){if(!c)throw Error();}else if(typeof a==="string")a=_.Eaa(a);else if(a.constructor!==_.ib)if(_.Pa(a))a=_.Faa(a);else{if(!b)throw Error();a=void 0}return a};_.kb=function(a,b){if(typeof b!=="number"||b<0||b>=a.length)throw Error();};Gaa=function(a,b){if(typeof b!=="number"||b<0||b>a.length)throw Error();};
Haa=function(a,b,c){const d=b&128?0:-1,e=a.length;var f;if(f=!!e)f=a[e-1],f=f!=null&&typeof f==="object"&&f.constructor===Object;const g=e+(f?-1:0);for(b=b&128?1:0;b<g;b++)c(b-d,a[b]);if(f){a=a[e-1];for(const h in a)!isNaN(h)&&c(+h,a[h])}};_.mb=function(a){return a&128?_.lb:void 0};_.nb=function(a){a.dnc=!0;return a};
_.qb=function(a){var b=a;if((0,_.ob)(b)){if(!/^\s*(?:-?[1-9]\d*|0)?\s*$/.test(b))throw Error(String(b));}else if((0,_.pb)(b)&&!Number.isSafeInteger(b))throw Error(String(b));return Iaa?BigInt(a):a=Jaa(a)?a?"1":"0":(0,_.ob)(a)?a.trim()||"0":String(a)};Kaa=function(a,b){if(a.length>b.length)return!1;if(a.length<b.length||a===b)return!0;for(let c=0;c<a.length;c++){const d=a[c],e=b[c];if(d>e)return!1;if(d<e)return!0}};_.Laa=function(a){const b=a>>>0;_.rb=b;_.sb=(a-b)/4294967296>>>0};
_.ub=function(a){if(a<0){_.Laa(-a);const [b,c]=Maa(_.rb,_.sb);_.rb=b>>>0;_.sb=c>>>0}else _.Laa(a)};_.Naa=function(a,b){const c=b*4294967296+(a>>>0);return Number.isSafeInteger(c)?c:_.vb(a,b)};Oaa=function(a,b){const c=b&**********;c&&(a=~a+1>>>0,b=~b>>>0,a==0&&(b=b+1>>>0));a=_.Naa(a,b);return typeof a==="number"?c?-a:a:c?"-"+a:a};
_.vb=function(a,b){b>>>=0;a>>>=0;if(b<=2097151)var c=""+(4294967296*b+a);else Ya()?c=""+(BigInt(b)<<BigInt(32)|BigInt(a)):(c=(a>>>24|b<<8)&16777215,b=b>>16&65535,a=(a&16777215)+c*6777216+b*6710656,c+=b*8147497,b*=2,a>=1E7&&(c+=a/1E7>>>0,a%=1E7),c>=1E7&&(b+=c/1E7>>>0,c%=1E7),c=b+Paa(c)+Paa(a));return c};Paa=function(a){a=String(a);return"0000000".slice(a.length)+a};
_.Qaa=function(a,b){if(b&**********)if(Ya())a=""+(BigInt(b|0)<<BigInt(32)|BigInt(a>>>0));else{const [c,d]=Maa(a,b);a="-"+_.vb(c,d)}else a=_.vb(a,b);return a};
Raa=function(a){if(a.length<16)_.ub(Number(a));else if(Ya())a=BigInt(a),_.rb=Number(a&BigInt(4294967295))>>>0,_.sb=Number(a>>BigInt(32)&BigInt(4294967295));else{const b=+(a[0]==="-");_.sb=_.rb=0;const c=a.length;for(let d=b,e=(c-b)%6+b;e<=c;d=e,e+=6){const f=Number(a.slice(d,e));_.sb*=1E6;_.rb=_.rb*1E6+f;_.rb>=4294967296&&(_.sb+=Math.trunc(_.rb/4294967296),_.sb>>>=0,_.rb>>>=0)}if(b){const [d,e]=Maa(_.rb,_.sb);_.rb=d;_.sb=e}}};Maa=function(a,b){b=~b;a?a=~a+1:b+=1;return[a,b]};
_.wb=function(a,b=`unexpected value ${a}!`){throw Error(b);};_.Saa=function(a){if(typeof a!=="number")throw Error("y`"+typeof a+"`"+a);return a};_.xb=function(a){if(a==null||typeof a==="number")return a;if(a==="NaN"||a==="Infinity"||a==="-Infinity")return Number(a)};_.Taa=function(a){if(typeof a!=="boolean")throw Error("z`"+_.yb(a)+"`"+a);return a};_.zb=function(a){if(a==null||typeof a==="boolean")return a;if(typeof a==="number")return!!a};
_.Bb=function(a){switch(typeof a){case "bigint":return!0;case "number":return(0,_.Ab)(a);case "string":return Uaa.test(a);default:return!1}};_.Cb=function(a){if(!(0,_.Ab)(a))throw _.Qa("enum");return a|0};_.Db=function(a){return a==null?a:(0,_.Ab)(a)?a|0:void 0};_.Eb=function(a){if(typeof a!=="number")throw _.Qa("int32");if(!(0,_.Ab)(a))throw _.Qa("int32");return a|0};_.Vaa=function(a){return a==null?a:_.Eb(a)};
_.Fb=function(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return(0,_.Ab)(a)?a|0:void 0};_.Gb=function(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return(0,_.Ab)(a)?a>>>0:void 0};
_.Zaa=function(a,b=0){if(!_.Bb(a))throw _.Qa("int64");const c=typeof a;switch(b){case 512:switch(c){case "string":return _.Hb(a);case "bigint":return String((0,_.Ib)(64,a));default:return _.Waa(a)}case 1024:switch(c){case "string":return Xaa(a);case "bigint":return _.qb((0,_.Ib)(64,a));default:return Yaa(a)}case 0:switch(c){case "string":return _.Hb(a);case "bigint":return _.qb((0,_.Ib)(64,a));default:return _.Jb(a)}default:return _.wb(b,"Unknown format requested type for int64")}};
_.Kb=function(a){return a==null?a:_.Zaa(a,0)};$aa=function(a){if(a[0]==="-")return!1;const b=a.length;return b<20?!0:b===20&&Number(a.substring(0,6))<184467};aba=function(a){const b=a.length;return a[0]==="-"?b<20?!0:b===20&&Number(a.substring(0,7))>-922337:b<19?!0:b===19&&Number(a.substring(0,6))<922337};bba=function(a){if(a<0){_.ub(a);var b=_.vb(_.rb,_.sb);a=Number(b);return Lb(a)?a:b}b=String(a);if($aa(b))return b;_.ub(a);return _.Naa(_.rb,_.sb)};
cba=function(a){if(aba(a))return a;Raa(a);return _.Qaa(_.rb,_.sb)};dba=function(a){if($aa(a))return a;Raa(a);return _.vb(_.rb,_.sb)};_.Jb=function(a){a=Mb(a);Lb(a)||(_.ub(a),a=Oaa(_.rb,_.sb));return a};_.eba=function(a){a=Mb(a);return a>=0&&Lb(a)?a:bba(a)};_.Waa=function(a){a=Mb(a);if(Lb(a))a=String(a);else{{const b=String(a);aba(b)?a=b:(_.ub(a),a=_.Qaa(_.rb,_.sb))}}return a};_.fba=function(a){a=Mb(a);if(a>=0&&Lb(a))a=String(a);else{{const b=String(a);$aa(b)?a=b:(_.ub(a),a=_.vb(_.rb,_.sb))}}return a};
_.Hb=function(a){var b=Mb(Number(a));if(Lb(b))return String(b);b=a.indexOf(".");b!==-1&&(a=a.substring(0,b));return cba(a)};Xaa=function(a){var b=Mb(Number(a));if(Lb(b))return _.qb(b);b=a.indexOf(".");b!==-1&&(a=a.substring(0,b));return Ya()?_.qb((0,_.Ib)(64,BigInt(a))):_.qb(cba(a))};Yaa=function(a){return Lb(a)?_.qb(_.Jb(a)):_.qb(_.Waa(a))};_.gba=function(a){var b=Mb(Number(a));if(Lb(b)&&b>=0)return String(b);b=a.indexOf(".");b!==-1&&(a=a.substring(0,b));return dba(a)};
_.hba=function(a){const b=typeof a;if(a==null)return a;if(b==="bigint")return _.qb((0,_.Ib)(64,a));if(_.Bb(a))return b==="string"?Xaa(a):Yaa(a)};_.iba=function(a){var b=typeof a;if(a==null)return a;if(b==="bigint")return _.qb((0,_.Nb)(64,a));if(_.Bb(a))return b==="string"?(b=Mb(Number(a)),Lb(b)&&b>=0?a=_.qb(b):(b=a.indexOf("."),b!==-1&&(a=a.substring(0,b)),a=Ya()?_.qb((0,_.Nb)(64,BigInt(a))):_.qb(dba(a)))):a=Lb(a)?_.qb(_.eba(a)):_.qb(_.fba(a)),a};
_.jba=function(a){if(a==null)return a;const b=typeof a;if(b==="bigint")return String((0,_.Ib)(64,a));if(_.Bb(a)){if(b==="string")return _.Hb(a);if(b==="number")return _.Jb(a)}};_.kba=function(a){if(a==null)return a;const b=typeof a;if(b==="bigint")return String((0,_.Nb)(64,a));if(_.Bb(a)){if(b==="string")return _.gba(a);if(b==="number")return _.eba(a)}};_.lba=function(a){if(a==null||typeof a=="string"||a instanceof _.ib)return a};_.Ob=function(a){if(typeof a!=="string")throw Error();return a};
_.Pb=function(a){if(a!=null&&typeof a!=="string")throw Error();return a};_.Qb=function(a){return a==null||typeof a==="string"?a:void 0};_.Tb=function(a,b,c,d){if(_.cb(a))return a;if(!Array.isArray(a))return c?d&2?((a=b[_.Sb])||(a=new b,_.Wa(a.Be),a=b[_.Sb]=a),b=a):b=new b:b=void 0,b;c=a[_.Ua]|0;d=c|d&32|d&2;d!==c&&(a[_.Ua]=d);return new b(a)};mba=function(a){return a};nba=function(a){return a};oba=function(a,b,c,d,e,f){a=_.Tb(a,d,c,f);e&&(a=_.Ub(a));return a};pba=function(a){return[a,this.get(a)]};
_.Xb=function(a){const b=_.Vb(_.Wb);return b?a[b]:void 0};_.rba=function(a,b,c){if(c){var d,e,f;((f=(e=(d=a[_.Wb])!=null?d:a[_.Wb]=new qba)[b])!=null?f:e[b]=[]).push(c)}};tba=function(a,b){b<100||_.Sa(sba,1)};
Yb=function(a,b,c,d){const e=d!==void 0;d=!!d;var f=_.Vb(_.Wb),g;!e&&f&&(g=a[f])&&uba(g,tba);f=[];var h=a.length;let m;g=4294967295;let u=!1;const x=!!(b&64),A=x?b&128?0:-1:void 0;if(!(b&1||(m=h&&a[h-1],m!=null&&typeof m==="object"&&m.constructor===Object?(h--,g=h):m=void 0,!x||b&128||e))){u=!0;var E;g=((E=vba)!=null?E:mba)(g-A,A,a,m)+A}b=void 0;for(E=0;E<h;E++){let N=a[E];if(N!=null&&(N=c(N,d))!=null)if(x&&E>=g){const X=E-A;var K=void 0;((K=b)!=null?K:b={})[X]=N}else f[E]=N}if(m)for(let N in m){K=
m[N];if(K==null||(K=c(K,d))==null)continue;h=+N;let X;if(x&&!Number.isNaN(h)&&(X=h+A)<g)f[X]=K;else{let fa;((fa=b)!=null?fa:b={})[N]=K}}b&&(u?f.push(b):f[g]=b);e&&_.Vb(_.Wb)&&(a=_.Xb(a))&&a instanceof qba&&(f[_.Wb]=wba(a));return f};xba=function(a){a[0]=Zb(a[0]);a[1]=Zb(a[1]);return a};
Zb=function(a){switch(typeof a){case "number":return Number.isFinite(a)?a:""+a;case "bigint":return(0,_.yba)(a)?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){const b=a[_.Ua]|0;return a.length===0&&b&1?void 0:Yb(a,b,Zb)}if(_.cb(a))return zba(a);if(a instanceof _.ib)return _.Aba(a);if(a instanceof _.ac)return a.l$b();return}return a};Dba=function(a,b){if(b){vba=b==null||b===mba||b[Bba]!==Cba?mba:b;try{return zba(a)}finally{vba=void 0}}return zba(a)};
zba=function(a){a=a.Be;return Yb(a,a[_.Ua]|0,Zb)};Gba=function(a){switch(typeof a){case "boolean":return Eba||(Eba=[0,void 0,!0]);case "number":return a>0?void 0:a===0?Fba||(Fba=[0,void 0]):[-a,void 0];case "string":return[0,a];case "object":return a}};_.bc=function(a,b,c){return a=Hba(a,b[0],b[1],c?1:2)};
Hba=function(a,b,c,d=0){if(a==null){var e=32;c?(a=[c],e|=128):a=[];b&&(e=e&-8380417|(b&1023)<<13)}else{if(!Array.isArray(a))throw Error("B");e=a[_.Ua]|0;2048&e&&!(2&e)&&Iba();if(e&256)throw Error("D");if(e&64)return d!==0||e&2048||(a[_.Ua]=e|2048),a;if(c&&(e|=128,c!==a[0]))throw Error("E");a:{c=a;e|=64;var f=c.length;if(f){var g=f-1;const m=c[g];if(m!=null&&typeof m==="object"&&m.constructor===Object){b=e&128?0:-1;g-=b;if(g>=1024)throw Error("G");for(var h in m)if(f=+h,f<g)c[f+b]=m[h],delete m[h];
else break;e=e&-8380417|(g&1023)<<13;break a}}if(b){h=Math.max(b,f-(e&128?0:-1));if(h>1024)throw Error("H");e=e&-8380417|(h&1023)<<13}}}e|=64;d===0&&(e|=2048);a[_.Ua]=e;return a};Iba=function(){_.Sa(Jba,5)};
Mba=function(a,b){if(typeof a!=="object")return a;if(Array.isArray(a)){var c=a[_.Ua]|0;return a.length===0&&c&1?void 0:Kba(a,c,b)}if(_.cb(a))return Lba(a);if(a instanceof _.ac){b=a.sN;if(b&2)return a;if(!a.size)return;c=_.Wa(a.gVa());if(a.ZR)for(a=0;a<c.length;a++){const d=c[a];let e=d[1];e==null||typeof e!=="object"?e=void 0:_.cb(e)?e=Lba(e):Array.isArray(e)?e=Kba(e,e[_.Ua]|0,!!(b&32)):e=void 0;d[1]=e}return c}if(a instanceof _.ib)return a};
Kba=function(a,b,c){if(b&2)return a;!c||4096&b||16&b?a=_.ec(a,b,!1,c&&!(b&16)):(_.Va(a,34),b&4&&Object.freeze(a));return a};_.Nba=function(a,b,c){a=new a.constructor(b);c&&_.gb(a,!0);a.Jta=_.eb;return a};Lba=function(a){const b=a.Be,c=b[_.Ua]|0;return _.fb(a,c)?a:_.hc(a,b,c)?_.Nba(a,b):_.ec(b,c)};_.ec=function(a,b,c,d){d!=null||(d=!!(34&b));a=Yb(a,b,Mba,d);d=32;c&&(d|=2);b=b&8380609|d;a[_.Ua]=b;return a};
_.Ub=function(a){const b=a.Be,c=b[_.Ua]|0;return _.fb(a,c)?_.hc(a,b,c)?_.Nba(a,b,!0):new a.constructor(_.ec(b,c,!1)):a};_.ic=function(a){if(a.zc!==_.eb)return!1;var b=a.Be;b=_.ec(b,b[_.Ua]|0);_.Va(b,2048);a.Be=b;_.gb(a,!1);a.Jta=void 0;return!0};_.jc=function(a){if(!_.ic(a)&&_.fb(a,a.Be[_.Ua]|0))throw Error();};_.lc=function(a,b){b===void 0&&(b=a[_.Ua]|0);b&32&&!(b&4096)&&(a[_.Ua]=b|4096)};_.hc=function(a,b,c){return c&2?!0:c&32&&!(c&4096)?(b[_.Ua]=c|2,_.gb(a,!0),!0):!1};
_.mc=function(a,b,c,d,e){const f=c+(e?0:-1);var g=a.length-1;if(g>=1+(e?0:-1)&&f>=g){const h=a[g];if(h!=null&&typeof h==="object"&&h.constructor===Object)return h[c]=d,b}if(f<=g)return a[f]=d,b;if(d!==void 0){let h;g=((h=b)!=null?h:b=a[_.Ua]|0)>>13&1023||536870912;c>=g?d!=null&&(a[g+(e?0:-1)]={[c]:d}):a[f]=d}return b};
_.pc=function(a,b,c,d,e,f,g){let h=a.Be,m=h[_.Ua]|0;d=_.fb(a,m)?1:d;e=!!e||d===3;d===2&&_.ic(a)&&(h=a.Be,m=h[_.Ua]|0);let u=Oba(h,b,g),x=u===nc?7:u[_.Ua]|0,A=Pba(x,m);var E=A;4&E?f==null?a=!1:(!e&&f===0&&(512&E||1024&E)&&(a.constructor[Qba]=(a.constructor[Qba]|0)+1)<5&&Aaa(),a=f===0?!1:!(f&E)):a=!0;if(a){4&A&&(u=[...u],x=0,A=oc(A,m),m=_.mc(h,m,b,u,g));let K=E=0;for(;E<u.length;E++){const N=c(u[E]);N!=null&&(u[K++]=N)}K<E&&(u.length=K);c=(A|4)&-513;A=c&=-1025;f&&(A|=f);A&=-4097}A!==x&&(u[_.Ua]=A,2&
A&&Object.freeze(u));return u=Rba(u,A,h,m,b,g,d,a,e)};Rba=function(a,b,c,d,e,f,g,h,m){let u=b;g===1||(g!==4?0:2&b||!(16&b)&&32&d)?qc(b)||(b|=!a.length||h&&!(4096&b)||32&d&&!(4096&b||16&b)?2:256,b!==u&&(a[_.Ua]=b),Object.freeze(a)):(g===2&&qc(b)&&(a=[...a],u=0,b=oc(b,d),d=_.mc(c,d,e,a,f)),qc(b)||(m||(b|=16),b!==u&&(a[_.Ua]=b)));2&b||!(4096&b||16&b)||_.lc(c,d);return a};Oba=function(a,b,c){a=_.rc(a,b,c);return Array.isArray(a)?a:nc};Pba=function(a,b){2&b&&(a|=2);return a|1};
qc=function(a){return!!(2&a)&&!!(4&a)||!!(256&a)};Sba=function(a){return _.jb(a,!0,!0)};_.Tba=function(a){a=[...a];for(let b=0;b<a.length;b++){const c=a[b]=[...a[b]];Array.isArray(c[1])&&(c[1]=_.Wa(c[1]))}return a};
_.sc=function(a,b,c,d){_.jc(a);const e=a.Be;let f=e[_.Ua]|0;if(c==null)return _.mc(e,f,b),a;let g=c===nc?7:c[_.Ua]|0,h=g;var m=qc(g);let u=m||Object.isFrozen(c);m||(g=0);u||(c=[...c],h=0,g=oc(g,f),u=!1);g|=5;var x;m=(x=Caa(g))!=null?x:0;for(x=0;x<c.length;x++){const A=c[x],E=d(A,m);Object.is(A,E)||(u&&(c=[...c],h=0,g=oc(g,f),u=!1),c[x]=E)}g!==h&&(u&&(c=[...c],g=oc(g,f)),c[_.Ua]=g);_.mc(e,f,b,c);return a};
_.tc=function(a,b,c,d,e){_.jc(a);const f=a.Be;_.mc(f,f[_.Ua]|0,b,(d==="0"?Number(c)===0:c===d)?void 0:c,e);return a};_.Uba=function(a,b,c){if(b&2)throw Error();const d=_.mb(b);let e=Oba(a,c,d),f=e===nc?7:e[_.Ua]|0,g=Pba(f,b);if(2&g||qc(g)||16&g)e=[...e],f=0,g=oc(g,b),_.mc(a,b,c,e,d);g&=-13;g!==f&&(e[_.Ua]=g);return e};Wba=function(a){let b;return(b=a[Vba])!=null?b:a[Vba]=new Map};_.Yba=function(a,b,c,d,e){const f=Wba(a),g=Xba(f,a,b,c,e);g!==d&&(g&&(b=_.mc(a,b,g,void 0,e)),f.set(c,d));return b};
Xba=function(a,b,c,d,e){let f=a.get(d);if(f!=null)return f;f=0;for(let g=0;g<d.length;g++){const h=d[g];_.rc(b,h,e)!=null&&(f!==0&&(c=_.mc(b,c,f,void 0,e)),f=h)}a.set(d,f);return f};_.wc=function(a,b,c,d,e){let f=!1;d=_.rc(a,d,e,g=>{const h=_.Tb(g,c,!1,b);f=h!==g&&h!=null;return h});if(d!=null)return f&&!_.fb(d)&&_.lc(a,b),d};
_.xc=function(a,b,c,d,e,f,g,h,m){var u=_.fb(a,c);f=u?1:f;h=!!h||f===3;u=m&&!u;(f===2||u)&&_.ic(a)&&(b=a.Be,c=b[_.Ua]|0);a=Oba(b,e,g);var x=a===nc?7:a[_.Ua]|0,A=Pba(x,c);if(m=!(4&A)){var E=a,K=c;const N=!!(2&A);N&&(K|=2);let X=!N,fa=!0,da=0,Aa=0;for(;da<E.length;da++){const va=_.Tb(E[da],d,!1,K);if(va instanceof d){if(!N){const ab=_.fb(va);X&&(X=!ab);fa&&(fa=ab)}E[Aa++]=va}}Aa<da&&(E.length=Aa);A|=4;A=fa?A&-4097:A|4096;A=X?A|8:A&-9}A!==x&&(a[_.Ua]=A,2&A&&Object.freeze(a));if(u&&!(8&A||!a.length&&(f===
1||(f!==4?0:2&A||!(16&A)&&32&c)))){qc(A)&&(a=[...a],A=oc(A,c),c=_.mc(b,c,e,a,g));d=a;u=A;for(x=0;x<d.length;x++)E=d[x],A=_.Ub(E),E!==A&&(d[x]=A);u|=8;A=u=d.length?u|4096:u&-4097;a[_.Ua]=A}return a=Rba(a,A,b,c,e,g,f,m,h)};_.Zba=function(a){a==null&&(a=void 0);return a};oc=function(a,b){return a=(2&b?a|2:a&-3)&-273};
_.yc=function(a,b,c,d,e,f,g,h,m,u){_.jc(a);b=_.pc(a,b,f,2,!0,void 0,g);let x;f=(x=Caa(b===nc?7:b[_.Ua]|0))!=null?x:0;if(m)if(Array.isArray(d))for(e=d.length,h=0;h<e;h++)b.push(c(d[h],f));else for(const A of d)b.push(c(A,f));else h&&u?(e!=null||(e=b.length-1),_.kb(b,e),b.splice(e,h)):(h&&Gaa(b,e),e!=void 0?b.splice(e,h,c(d,f)):b.push(c(d,f)));return a};
_.zc=function(a,b,c,d,e,f,g,h){_.jc(a);const m=a.Be;a=_.xc(a,m,m[_.Ua]|0,c,b,2,d,!0);if(g&&h)f!=null||(f=a.length-1),_.kb(a,f),a.splice(f,g),a.length||(a[_.Ua]&=-4097);else return g?Gaa(a,f):e=e!=null?e:new c,f!=void 0?a.splice(f,g,e):a.push(e),f=c=a===nc?7:a[_.Ua]|0,(g=_.fb(e))?(c&=-9,a.length===1&&(c&=-4097)):c|=4096,c!==f&&(a[_.Ua]=c),g||_.lc(m),e};
_.$ba=function(a,b){if(typeof a==="string")return new Ac(_.yaa(a),b);if(Array.isArray(a))return new Ac(new Uint8Array(a),b);if(a.constructor===Uint8Array)return new Ac(a,!1);if(a.constructor===ArrayBuffer)return a=new Uint8Array(a),new Ac(a,!1);if(a.constructor===_.ib)return b=_.Bc(a)||new Uint8Array(0),new Ac(b,!0,a);if(a instanceof Uint8Array)return a=a.constructor===Uint8Array?a:new Uint8Array(a.buffer,a.byteOffset,a.byteLength),new Ac(a,!1);throw Error();};_.aca=function(a){switch(typeof a){case "string":_.Cc(a)}};
Dc=function(){const a=class{constructor(){throw Error();}};Object.setPrototypeOf(a,a.prototype);return a};_.cca=function(a,b){return new Ec(a,b,bca)};fca=function(a,b,c,d,e){_.dca(a,c,_.eca(b,d),e)};
_.Fc=function(a,b,c,d){var e=d[a];if(e)return e;e={};e.J4a=d;e.n5=Gba(d[0]);var f=d[1];let g=1;f&&f.constructor===Object&&(e.extensions=f,f=d[++g],typeof f==="function"&&(e.pdb=!0,gca!=null||(gca=f),hca!=null||(hca=d[g+1]),f=d[g+=2]));const h={};for(;f&&ica(f);){for(var m=0;m<f.length;m++)h[f[m]]=f;f=d[++g]}for(m=1;f!==void 0;){typeof f==="number"&&(m+=f,f=d[++g]);let A;var u=void 0;f instanceof Ec?A=f:(A=jca,g--);let E;if((E=A)==null?0:E.ma){f=d[++g];u=d;var x=g;typeof f==="function"&&(f=f(),u[x]=
f);u=f}f=d[++g];x=m+1;typeof f==="number"&&f<0&&(x-=f,f=d[++g]);for(;m<x;m++){const K=h[m];u?c(e,m,A,u,K):b(e,m,A,K)}}return d[a]=e};ica=function(a){return Array.isArray(a)&&!!a.length&&typeof a[0]==="number"&&a[0]>0};lca=function(a){return Array.isArray(a)?a[0]instanceof Ec?a:[kca,a]:[a,void 0]};_.eca=function(a,b){if(a instanceof _.l)return a.Be;if(Array.isArray(a))return _.bc(a,b,!1)};_.mca=function(a,b,c,d){const e=c.ha;a[b]=d?(f,g,h)=>e(f,g,h,d):e};
_.nca=function(a,b,c,d,e){const f=c.ha;let g,h;a[b]=(m,u,x)=>f(m,u,x,h||(h=_.Fc(_.Gc,_.mca,_.nca,d).n5),g||(g=_.oca(d)),e)};_.oca=function(a){let b=a[pca];if(b!=null)return b;const c=_.Fc(_.Gc,_.mca,_.nca,a);b=c.pdb?(d,e)=>gca(d,e,c):(d,e)=>{for(;_.qca(e)&&e.ha!=4;){const g=e.na;let h=c[g];if(h==null){var f=c.extensions;f&&(f=f[g])&&(f=rca(f),f!=null&&(h=c[g]=f))}h!=null&&h(e,d,g)||_.rba(d,g,_.sca(e))}if(d=_.Xb(d))d.Bva=c.J4a[_.tca];return!0};a[pca]=b;a[_.tca]=uca.bind(a);return b};
uca=function(a,b,c,d){var e=this[_.Gc];const f=this[pca],g=_.bc(void 0,e.n5,!1),h=_.Xb(a);if(h){var m=!1,u=e.extensions;if(u){e=(x,A,E)=>{if(E.length!==0)if(u[A])for(const K of E){x=vca(K);try{m=!0,f(g,x)}finally{wca(x)}}else d==null||d(a,A,E)};if(b==null)uba(h,e);else if(h!=null){const x=h[b];x&&e(h,b,x)}if(m){let x=a[_.Ua]|0;if(x&2&&x&2048&&(c==null||!c.G3b))throw Error();const A=_.mb(x),E=(K,N)=>{if(_.rc(a,K,A)!=null)switch(c==null?void 0:c.lpc){case 1:return;default:throw Error();}N!=null&&(x=
_.mc(a,x,K,N,A));delete h[K]};b==null?Haa(g,g[_.Ua]|0,(K,N)=>{E(K,N)}):E(b,_.rc(g,b,A))}}}};rca=function(a){a=lca(a);const b=a[0].ha;if(a=a[1]){const c=_.oca(a),d=_.Fc(_.Gc,_.mca,_.nca,a).n5;return(e,f,g)=>b(e,f,g,d,c)}return b};xca=function(a,b,c){a[b]=c.ka};zca=function(a,b,c,d){let e,f;const g=c.ka;a[b]=(h,m,u)=>g(h,m,u,f||(f=_.Fc(yca,xca,zca,d).n5),e||(e=Aca(d)))};Aca=function(a){let b=a[Bca];if(!b){const c=_.Fc(yca,xca,zca,a);b=(d,e)=>Cca(d,e,c);a[Bca]=b}return b};
Cca=function(a,b,c){Haa(a,a[_.Ua]|0,(d,e)=>{if(e!=null){var f=Dca(c,d);f?f(b,e,d):d<500||_.Sa(Eca,3)}});(a=_.Xb(a))&&uba(a,(d,e,f)=>{Ic(b,b.ha.end());for(d=0;d<f.length;d++)Ic(b,_.Bc(f[d])||new Uint8Array(0))})};Dca=function(a,b){var c=a[b];if(c)return c;if(c=a.extensions)if(c=c[b]){c=lca(c);var d=c[0].ka;if(c=c[1]){const e=Aca(c),f=_.Fc(yca,xca,zca,c).n5;c=a.pdb?hca(f,e):(g,h,m)=>d(g,h,m,f,e)}else c=d;return a[b]=c}};
_.Jc=function(a,b,c){if(Array.isArray(b)){var d=b[_.Ua]|0;if(d&4)return b;for(var e=0,f=0;e<b.length;e++){const g=a(b[e]);g!=null&&(b[f++]=g)}f<e&&(b.length=f);c&&(b[_.Ua]=(d|5)&-1537,d&2&&Object.freeze(b));return b}};_.Kc=function(a,b,c){return new Ec(a,b,c)};_.Lc=function(a,b,c){return new Ec(a,b,c)};_.Fca=function(a,b,c=bca){return new Ec(a,b,c)};_.Nc=function(a,b,c){_.mc(a,a[_.Ua]|0,b,c,_.mb(a[_.Ua]|0))};_.Gca=function(a,b,c){b=_.bc(void 0,b,!0);_.Uba(a,a[_.Ua]|0,c).push(b);return b};
_.Ica=function(a,b,c){b=_.xb(b);b!=null&&(_.Oc(a,c,1),_.Hca(a.ha,b))};_.Pc=function(a,b,c){b=_.jba(b);if(b!=null){switch(typeof b){case "string":_.Jca(b)}_.Kca(a,c,b)}};_.Lca=function(a,b,c){b=_.Fb(b);b!=null&&b!=null&&(_.Oc(a,c,0),_.Qc(a.ha,b))};
_.Mca=function(a,b,c){b=_.kba(b);if(b!=null)switch(_.aca(b),_.Oc(a,c,1),typeof b){case "number":a=a.ha;_.Laa(b);_.Rc(a,_.rb);_.Rc(a,_.sb);break;case "bigint":c=_.Sc(b);a=a.ha;b=c.ha;_.Rc(a,c.ka);_.Rc(a,b);break;default:c=_.Cc(b),a=a.ha,b=c.ha,_.Rc(a,c.ka),_.Rc(a,b)}};_.Nca=function(a,b,c){b=_.zb(b);b!=null&&(_.Oc(a,c,0),a.ha.ha.push(b?1:0))};Oca=function(a,b,c){b=_.Qb(b);b!=null&&_.Tc(a,c,_.faa(b))};Pca=function(a,b,c,d,e){_.dca(a,c,_.eca(b,d),e)};
_.Qca=function(a,b,c){b=_.lba(b);b!=null&&_.Tc(a,c,_.$ba(b,!0).buffer)};_.Sca=function(a,b,c){_.Rca(a,c,_.Fb(b))};_.Tca=function(a,b,c){if(a.ha!==0&&a.ha!==2)return!1;b=_.Uc(b,c);a.ha==2?_.Vc(a,_.Wc,b):b.push(_.Wc(a.ka));return!0};_.Vca=function(a,b,c){if(a.ha!==2)return!1;a=_.Uca(a);_.Nc(b,c,a===Yc()?void 0:a);return!0};_.Xca=function(a,b,c){if(a.ha!==0&&a.ha!==2)return!1;b=_.Uc(b,c);a.ha==2?_.Vc(a,Wca,b):b.push(_.Wc(a.ka));return!0};_.Zc=function(a,b,c){return new Yca(a,b,c)};
_.$c=function(a,b){return(c,d)=>{{const f={zVa:!0};d&&Object.assign(f,d);c=vca(c,void 0,void 0,f);try{const g=new a,h=g.Be;_.oca(b)(h,c);var e=g}finally{wca(c)}}return e}};_.ad=function(a){return _.nb(b=>b instanceof a&&!_.fb(b))};_.cd=function(a){return b=>_.bd(a,b)};_.dd=function(a){return(0,_.yba)(a)?Number(a):String(a)};Zca=function(a=window){return a.WIZ_global_data};_.$ca=function(a,b=window){return(b=Zca(b))&&a in b?b[a]:null};_.ed=function(){ada===void 0&&(ada=new _.bda);return ada};
_.dda=function(a){if(_.fd)a(_.fd);else{let b;((b=cda)!=null?b:cda=[]).push(a)}};_.hd=function(){!_.fd&&_.gd&&_.eda(_.gd());return _.fd};_.eda=function(a){_.fd=a;let b;(b=cda)==null||b.forEach(_.dda);cda=void 0};_.id=function(a){_.fd&&_.fd.wb(a)};_.jd=function(){_.fd&&_.fd.Da()};_.kd=function(a,b,c){for(const d in a)b.call(c,a[d],d,a)};_.ld=function(a,b,c){const d={};for(const e in a)d[e]=b.call(c,a[e],e,a);return d};_.md=function(a){const b=[];let c=0;for(const d in a)b[c++]=a[d];return b};
_.nd=function(a){const b=[];let c=0;for(const d in a)b[c++]=d;return b};_.od=function(a){for(const b in a)return!1;return!0};_.pd=function(a){const b={};for(const c in a)b[c]=a[c];return b};_.qd=function(a,b){let c,d;for(let e=1;e<arguments.length;e++){d=arguments[e];for(c in d)a[c]=d[c];for(let f=0;f<fda.length;f++)c=fda[f],Object.prototype.hasOwnProperty.call(d,c)&&(a[c]=d[c])}};
_.rd=function(a){const b=arguments.length;if(b==1&&Array.isArray(arguments[0]))return _.rd.apply(null,arguments[0]);const c={};for(let d=0;d<b;d++)c[arguments[d]]=!0;return c};hda=function(){let a=null;if(!gda)return a;try{const b=c=>c;a=gda.createPolicy("BardChatUi#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(b){}return a};jda=function(){ida===void 0&&(ida=hda());return ida};_.ud=function(a){const b=jda();a=b?b.createScriptURL(a):a;return new _.sd(_.td,a)};
_.kda=function(a){return a instanceof _.sd};_.vd=function(a){if(_.kda(a))return a.ha;throw Error("ga");};lda=function(a){return a.toString().indexOf("`")===-1};_.wd=function(a){return new _.mda(_.td,a)};_.xd=function(a){return a instanceof _.mda};_.Ad=function(a){if(_.xd(a))return a.ha;throw Error("ga");};Cd=function(a){return new Bd(b=>b.substr(0,a.length+1).toLowerCase()===a+":")};_.Ed=function(a,b=nda){if(_.xd(a))return a;for(let c=0;c<b.length;++c){const d=b[c];if(d instanceof Bd&&d.eu(a))return _.wd(a)}};
_.Gd=function(a,b=nda){b=_.Ed(a,b);b===void 0&&_.oda(a.toString());return b||_.Fd};
_.Hd=function(a){var b=window;if(typeof MediaSource!=="undefined"&&a instanceof MediaSource||typeof b.ManagedMediaSource!=="undefined"&&a instanceof b.ManagedMediaSource)return _.wd(URL.createObjectURL(a));b=a.type;b.toLowerCase()==="application/octet-stream"?b=!0:(b=b.match(/^([^;]+)(?:;\w+=(?:\w+|"[\w;,= ]+"))*$/i),b=(b==null?void 0:b.length)===2&&(/^image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp|x-icon|heic|heif|avif|x-ms-bmp)$/i.test(b[1])||/^video\/(?:3gpp|avi|mpeg|mpg|mp4|ogg|webm|x-flv|x-matroska|quicktime|x-ms-wmv)$/i.test(b[1])||
/^audio\/(?:3gpp2|3gpp|aac|amr|L16|midi|mp3|mp4|mpeg|oga|ogg|opus|x-m4a|x-matroska|x-wav|wav|webm)$/i.test(b[1])||/^font\/[\w-]+$/i.test(b[1])));if(!b)throw Error("ga");return _.wd(URL.createObjectURL(a))};_.qda=function(a){if(!pda){a:{var b=document.createElement("a");try{b.href=a}catch(c){a=void 0;break a}a=b.protocol;a=a===":"||a===""?"https:":a}return a}try{b=new URL(a)}catch(c){return"https:"}return b.protocol};sda=function(a){const b=!rda.test(a);b&&_.oda(a);if(!b)return a};
_.Id=function(a){return a instanceof _.mda?_.Ad(a):sda(a)};_.Jd=function(a,b){b=_.Id(b);b!==void 0&&(a.href=b)};_.Ld=function(a){const b=jda();a=b?b.createHTML(a):a;return new _.Kd(_.td,a)};_.Md=function(a){return a instanceof _.Kd};_.Nd=function(a){if(_.Md(a))return a.ha;throw Error("ga");};_.Pd=function(a,b){a.src=_.vd(b).toString()};_.Qd=function(a,b){a.srcdoc=_.Nd(b)};_.Rd=function(a,b){a.setAttribute("sandbox","");for(let c=0;c<b.length;c++)a.sandbox.supports&&!a.sandbox.supports(b[c])||a.sandbox.add(b[c])};
_.Td=function(a,b,c){a.removeAttribute("srcdoc");switch(b){case 0:if(c instanceof _.sd)throw new _.Sd("TrustedResourceUrl",0);_.Rd(a,[]);b=_.Id(c);b!==void 0&&(a.src=b);break;case 1:if(!(c instanceof _.sd))throw new _.Sd(typeof c,1);_.Rd(a,"allow-same-origin allow-scripts allow-forms allow-popups allow-popups-to-escape-sandbox allow-storage-access-by-user-activation".split(" "));_.Pd(a,c);break;case 2:if(c instanceof _.sd)throw new _.Sd("TrustedResourceUrl",2);_.Rd(a,"allow-same-origin allow-scripts allow-forms allow-popups allow-popups-to-escape-sandbox allow-storage-access-by-user-activation".split(" "));
b=_.Id(c);b!==void 0&&(a.src=b);break;default:_.wb(b,void 0)}};_.Vd=function(a,b,c,d){b=_.Id(b);return b!==void 0?a.open(b,c,d):null};_.Xd=function(a){return tda("script",a)};_.uda=function(a){return tda("style",a)};tda=function(a,b=document){let c;const d=(c=b.querySelector)==null?void 0:c.call(b,`${a}[nonce]`);return d==null?"":d.nonce||d.getAttribute("nonce")||""};_.Zd=function(a){const b=jda();a=b?b.createScript(a):a;return new _.Yd(_.td,a)};_.vda=function(a){return a instanceof _.Yd};
_.be=function(a){if(_.vda(a))return a.ha;throw Error("ga");};wda=function(a){const b=_.Xd(a.ownerDocument);b&&a.setAttribute("nonce",b)};_.ce=function(a,b,c){a.textContent=_.be(b);(c==null?0:c.F0b)||wda(a)};_.de=function(a,b,c){a.src=_.vd(b);(c==null?0:c.F0b)||wda(a)};_.yda=function(a){if(a instanceof _.xda)return a.ha;throw Error("ga");};_.ee=function(a,b){a.nodeType===1&&_.zda(a);a.innerHTML=_.Nd(b)};
_.fe=function(a,b,c,d){if(a.length===0)throw Error("ga");a=a.map(f=>_.yda(f));const e=c.toLowerCase();if(a.every(f=>e.indexOf(f)!==0))throw Error("ha`"+c);b.setAttribute(c,d)};_.zda=function(a){if(/^(script|style)$/i.test(a.tagName))throw Error("ga");};_.Cda=function(a,b,c){if(_.kda(b))_.Ada(a,b,c);else{if(Bda.indexOf(c)===-1)throw Error("ia`"+c);b=_.Id(b);b!==void 0&&(a.href=b,a.rel=c)}};_.Ada=function(a,b,c){a.href=_.vd(b).toString();a.rel=c};
_.Dda=function(a){return"function"==typeof _.ie&&a instanceof _.ie};_.je=function(a){if(_.Dda(a))return a.ha;throw Error("ga");};_.ke=function(a,b){a.write(_.Nd(b))};_.le=function(a,b,c){return a.parseFromString(_.Nd(b),c)};_.me=function(a,b){b=_.Id(b);b!==void 0&&(a.href=b)};_.Eda=function(a,b){return a.createContextualFragment(_.Nd(b))};_.Fda=function(a){return _.Ld(a)};_.Gda=function(a){return _.ud(a)};_.ne=function(a){return new _.xda(_.td,a[0].toLowerCase())};
_.qe=function(a,b){if(_.Md(a))return a;a=_.oe(String(a));if(b==null?0:b.Ioc)a=a.replace(/(^|[\r\n\t ]) /g,"$1&#160;");if(b==null?0:b.Nua)a=a.replace(/(\r\n|\n|\r)/g,"<br>");if(b==null?0:b.Joc)a=a.replace(/(\t+)/g,'<span style="white-space:pre">$1</span>');return _.Ld(a)};_.oe=function(a){return a.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")};_.re=function(a){return _.Hda("",a)};_.Hda=function(a,b){a=_.qe(a);return _.Ld(b.map(c=>_.Nd(_.qe(c))).join(_.Nd(a).toString()))};
_.Ida=function(a,b){b.appendChild(a);a=(new XMLSerializer).serializeToString(b);a=a.slice(a.indexOf(">")+1,a.lastIndexOf("</"));return _.Ld(a)};_.Lda=function(a){if(!Jda.test(a))throw Error("ga");if(Kda.indexOf(a.toUpperCase())!==-1)throw Error("ga");};_.se=function(a,b,c){_.Lda(a);let d=`<${a}`;b&&(d+=_.Mda(b));Array.isArray(c)||(c=c===void 0?[]:[c]);Nda.indexOf(a.toUpperCase())!==-1?d+=">":(b=_.re(c.map(e=>_.Md(e)?e:_.qe(String(e)))),d+=">"+b.toString()+"</"+a+">");return _.Ld(d)};
_.Mda=function(a){var b="";const c=Object.keys(a);for(let f=0;f<c.length;f++){var d=c[f],e=a[d];if(!Jda.test(d))throw Error("ga");if(e!==void 0&&e!==null){if(/^on./i.test(d))throw Error("ga");Oda.indexOf(d.toLowerCase())!==-1&&(e=_.xd(e)?e.toString():sda(String(e))||"about:invalid#zClosurez");e=`${d}="${_.qe(String(e))}"`;b+=" "+e}}return b};Pda=function(a){try{return new URL(a,window.document.baseURI)}catch(b){return new URL("about:invalid")}};
Qda=function(a,b){const c=b.createRange();c.selectNode(b.body);a=_.Ld(a);return _.Eda(c,a)};Rda=function(a){a=a.nodeName;return typeof a==="string"?a:"FORM"};Sda=function(a){a=a.nodeType;return a===1||typeof a!=="number"};te=function(a,b,c){a.setAttribute(b,c)};Tda=function(a){return a.ym.map(b=>{const c=b.OGa;return`${b.url}${c?` ${c}`:""}`}).join(" , ")};_.ve=function(a){return _.ue.sanitize(a)};
_.Uda=function(a){const b=a.split(/[?#]/),c=/[?]/.test(a)?"?"+b[1]:"";return{xya:b[0],params:c,fragment:/[#]/.test(a)?"#"+(c?b[2]:b[1]):""}};_.we=function(a,...b){if(b.length===0)return _.ud(a[0]);let c=a[0];for(let d=0;d<b.length;d++)c+=encodeURIComponent(b[d])+a[d+1];return _.ud(c)};_.ye=function(a,b){a=_.Uda(_.vd(a).toString());return _.Vda(a.xya,a.params,a.fragment,b)};
_.Vda=function(a,b,c,d){function e(g,h){g!=null&&(Array.isArray(g)?g.forEach(m=>e(m,h)):(b+=f+encodeURIComponent(h)+"="+encodeURIComponent(g),f="&"))}let f=b.length?"&":"?";d.constructor===Object&&(d=Object.entries(d));Array.isArray(d)?d.forEach(g=>e(g[1],g[0])):d.forEach(e);return _.ud(a+b+c)};_.Wda=function(a,b){a=_.Uda(_.vd(a).toString());const c=a.xya.slice(-1)==="/"?"":"/";b=a.xya+c+encodeURIComponent(b);return _.ud(b+a.params+a.fragment)};
_.Xda=function(a,b){let c,d;return Math.random()<((d=(c=a.xpc)!=null?c:b)!=null?d:0)};_.Yda=function(a,b){const c=new XMLHttpRequest;c.open("POST",a);c.setRequestHeader("Content-Type","application/json");c.send(b)};Zda=function(a,b){var c=b||_.ze();const d=c.Qd();b=c.createElement("STYLE");const e=_.uda(d);e&&b.setAttribute("nonce",e);b.type="text/css";c=c.getElementsByTagName("HEAD")[0];b.styleSheet?b.styleSheet.cssText=a:(a=d.createTextNode(a),b.appendChild(a));c.appendChild(b);return b};
_.Ae=function(a){a&&typeof a.dispose=="function"&&a.dispose()};_.Be=function(a){for(let b=0,c=arguments.length;b<c;++b){const d=arguments[b];_.Ka(d)?_.Be.apply(null,d):_.Ae(d)}};_.De=function(a){return new _.Ce(a,_.$ca(a,window))};$da=function(a){if(typeof document!=="undefined"&&document&&document.getElementById&&(a=document.getElementById(a))){const b=a.tagName.toUpperCase();if(b=="SCRIPT"||b=="LINK")return a}return null};
bea=function(a="",b){if(a&&b)throw Error("ra");var c="";const d=_.ia._F_jsUrl;(a=b||$da(a))&&(c=a.src?a.src:a.getAttribute("href"));if(d&&c){if(d!=c)throw Error("sa`"+d+"`"+c);c=d}else c=d||c;if(!aea(c))throw Error("ta");return c};_.fea=function(){if(cea)return dea;cea=!0;let a;try{a=bea(_.ia._F_jsUrl?"":"base-js")}catch(d){return!1}const b=eea(_.Ee(a)),c=Object.keys(b);if(c.length===0)return!1;_.dda(d=>{for(const e of c){const f=b[e];for(const g of Object.keys(f))d.nma(e,g)}});return dea=!0};
_.gea=function(){};lea=function(){let a;for(;a=hea.remove();){try{a.fn.call(a.scope)}catch(b){_.ja(b)}iea(jea,a)}kea=!1};nea=function(a){a=a.buf.charCodeAt(a.ob++);return mea[a]};Fe=function(a){let b=0,c=0,d;do d=nea(a),b|=(d&31)<<c,c+=5;while(d&32);return b<0?b+4294967296:b};
_.oea=function(){var a={},b=a.cssRowKey||"",c=a.bU||"";!b&&window&&window._F_cssRowKey&&(b=window._F_cssRowKey,!c&&window._F_combinedSignature&&(c=window._F_combinedSignature));if(b&&typeof window._F_installCss!=="function")throw Error("Aa");const d=a.Gnc||_.Ge;var e=$da("base-js");b=new d(_.Gda(bea("",e),{RMa:"base-js url is generated from ModulesetsUrlBuilder"}),b,c,!0,!1);c=a.Qqc||e&&e.hasAttribute("crossorigin");e=a.qlc||e&&e.getAttribute("crossorigin");c&&(b.Lia=c);e&&(b.Haa=e);a.Iia&&(b.Iia=
a.Iia);a.fetchPriority&&(b.fetchPriority=a.fetchPriority);const f=_.hd();f.qa=b;f.Gjb(!0);_.He=g=>Promise.resolve(f.load(g))};aaa=[];pea=Object.defineProperty;qea=function(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("a");};rea=qea(this);
Ie=function(a,b){if(b)a:{var c=rea;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&b!=null&&pea(c,a,{configurable:!0,writable:!0,value:b})}};Ie("Symbol.asyncIterator",function(a){return a?a:Symbol("b")});sea=Object.create;tea=Object.setPrototypeOf;uea=function(a,b){a.prototype=sea(b.prototype);a.prototype.constructor=a;tea(a,b);a.Nb=b.prototype};
vea=function(a){function b(d){return a.next(d)}function c(d){return a.throw(d)}return new Promise(function(d,e){function f(g){g.done?d(g.value):Promise.resolve(g.value).then(b,c).then(f,e)}f(a.next())})};_.Je=function(a){return vea(a())};Ie("globalThis",function(a){return a||rea});Ie("Symbol.dispose",function(a){return a?a:Symbol("h")});Ie("Object.values",function(a){return a?a:function(b){var c=[],d;for(d in b)Object.prototype.hasOwnProperty.call(b,d)&&c.push(b[d]);return c}});
Ie("Object.entries",function(a){return a?a:function(b){var c=[],d;for(d in b)Object.prototype.hasOwnProperty.call(b,d)&&c.push([d,b[d]]);return c}});Ie("Array.prototype.values",function(a){return a?a:function(){return this[Symbol.iterator]()}});Ie("Object.fromEntries",function(a){return a?a:function(b){var c={};if(!(Symbol.iterator in b))throw new TypeError("i`"+b);b=b[Symbol.iterator].call(b);for(var d=b.next();!d.done;d=b.next()){d=d.value;if(Object(d)!==d)throw new TypeError("j");c[d[0]]=d[1]}return c}});
Ie("String.prototype.replaceAll",function(a){return a?a:function(b,c){if(b instanceof RegExp&&!b.global)throw new TypeError("k");return b instanceof RegExp?this.replace(b,c):this.replace(new RegExp(String(b).replace(/([-()\[\]{}+?*.$\^|,:#<!\\])/g,"\\$1").replace(/\x08/g,"\\x08"),"g"),c)}});Ie("String.prototype.trimLeft",function(a){function b(){return this.replace(/^[\s\xa0]+/,"")}return a||b});Ie("String.prototype.trimStart",function(a){return a||String.prototype.trimLeft});
Ie("Promise.prototype.finally",function(a){return a?a:function(b){return this.then(function(c){return Promise.resolve(b()).then(function(){return c})},function(c){return Promise.resolve(b()).then(function(){throw c;})})}});Ie("Array.prototype.flatMap",function(a){return a?a:function(b,c){var d=[];Array.prototype.forEach.call(this,function(e,f){e=b.call(c,e,f,this);Array.isArray(e)?d.push.apply(d,e):d.push(e)});return d}});
Ie("String.prototype.padStart",function(a){return a?a:function(b,c){if(this==null)throw new TypeError("l`padStart");b-=this.length;c=c!==void 0?String(c):" ";return(b>0&&c?c.repeat(Math.ceil(b/c.length)).substring(0,b):"")+this}});Ie("Set.prototype.difference",function(a){return a?a:function(b){wea(this);xea(b);var c=yea(this,b);b=new Set(this);var d=c.YTa;c=c.WMa;for(var e=d.next();!e.done;)c.has(e.value)&&b.delete(e.value),e=d.next();return b}});
Ie("Set.prototype.intersection",function(a){return a?a:function(b){wea(this);xea(b);var c=new Set,d=yea(this,b);b=d.YTa;d=d.WMa;for(var e=b.next();!e.done;)d.has(e.value)&&c.add(e.value),e=b.next();return c}});
var xea=function(a){if(typeof a!=="object"||a===null||typeof a.size!=="number"||a.size<0||typeof a.keys!=="function"||typeof a.has!=="function")throw new TypeError("n");},yea=function(a,b){if(a.size<=b.size)a={YTa:a.keys(),WMa:b};else{b=b.keys();if(typeof b!=="object"||b===null||typeof b.next!=="function")throw new TypeError("o");a={YTa:b,WMa:a}}return a},wea=function(a){if(!(a instanceof Set))throw new TypeError("p");},zea=function(a){a=Math.trunc(a)||0;a<0&&(a+=this.length);if(!(a<0||a>=this.length))return this[a]};
Ie("Array.prototype.at",function(a){return a?a:zea});var Ke=function(a){return a?a:zea};Ie("Int8Array.prototype.at",Ke);Ie("Uint8Array.prototype.at",Ke);Ie("Uint8ClampedArray.prototype.at",Ke);Ie("Int16Array.prototype.at",Ke);Ie("Uint16Array.prototype.at",Ke);Ie("Int32Array.prototype.at",Ke);Ie("Uint32Array.prototype.at",Ke);Ie("Float32Array.prototype.at",Ke);Ie("Float64Array.prototype.at",Ke);Ie("String.prototype.at",function(a){return a?a:zea});
Ie("Array.prototype.flat",function(a){return a?a:function(b){b=b===void 0?1:b;var c=[];Array.prototype.forEach.call(this,function(d){Array.isArray(d)&&b>0?(d=Array.prototype.flat.call(d,b-1),c.push.apply(c,d)):c.push(d)});return c}});Ie("String.prototype.trimRight",function(a){function b(){return this.replace(/[\s\xa0]+$/,"")}return a||b});Ie("String.prototype.trimEnd",function(a){return a||String.prototype.trimRight});
Ie("Promise.allSettled",function(a){function b(d){return{status:"fulfilled",value:d}}function c(d){return{status:"rejected",reason:d}}return a?a:function(d){var e=this;d=Array.from(d,function(f){return e.resolve(f).then(b,c)});return e.all(d)}});
Ie("String.prototype.matchAll",function(a){return a?a:function(b){if(b instanceof RegExp&&!b.global)throw new TypeError("q");var c=new RegExp(b,b instanceof RegExp?void 0:"g"),d=this,e=!1,f={next:function(){if(e)return{value:void 0,done:!0};var g=c.exec(d);if(!g)return e=!0,{value:void 0,done:!0};g[0]===""&&(c.lastIndex+=1);return{value:g,done:!1}}};f[Symbol.iterator]=function(){return f};return f}});
Ie("AggregateError",function(a){if(a)return a;a=function(b,c){c=Error(c);"stack"in c&&(this.stack=c.stack);this.errors=b;this.message=c.message};uea(a,Error);a.prototype.name="AggregateError";return a});Ie("Promise.any",function(a){return a?a:function(b){b=b instanceof Array?b:Array.from(b);return Promise.all(b.map(function(c){return Promise.resolve(c).then(function(d){throw d;},function(d){return d})})).then(function(c){throw new AggregateError(c,"All promises were rejected");},function(c){return c})}});
_._DumpException=_._DumpException||function(a){throw a;};var Bea,Oe,Cea,Dea,Eea;_.Aea=_.Aea||{};_.ia=this||self;_.Le=function(a,b,c){a=a.split(".");c=c||_.ia;for(var d;a.length&&(d=a.shift());)a.length||b===void 0?c[d]&&c[d]!==Object.prototype[d]?c=c[d]:c=c[d]={}:c[d]=b};Bea=function(a){var b=_.Me("WIZ_global_data.oxN3nb");a=b&&b[a];return a!=null?a:!1};_.Ne=_.ia._F_toggles_default_BardChatUi||[];Oe=function(){};Oe.get=function(){return null};_.He=null;
_.Me=function(a,b){a=a.split(".");b=b||_.ia;for(var c=0;c<a.length;c++)if(b=b[a[c]],b==null)return null;return b};_.yb=function(a){var b=typeof a;return b!="object"?b:a?Array.isArray(a)?"array":b:"null"};_.Ka=function(a){var b=_.yb(a);return b=="array"||b=="object"&&typeof a.length=="number"};_.Pe=function(a){var b=typeof a;return b=="object"&&a!=null||b=="function"};_.Qe=function(a){return Object.prototype.hasOwnProperty.call(a,Cea)&&a[Cea]||(a[Cea]=++Dea)};
Cea="closure_uid_"+(Math.random()*1E9>>>0);Dea=0;Eea=function(a,b,c){return a.call.apply(a.bind,arguments)};_.Re=function(a,b,c){_.Re=Eea;return _.Re.apply(null,arguments)};_.Se=function(a,b){var c=Array.prototype.slice.call(arguments,1);return function(){var d=c.slice();d.push.apply(d,arguments);return a.apply(this,d)}};_.Te=function(){return Date.now()};_.Fea=function(a,b,c){_.Le(a,b,c)};_.Vb=function(a){return a};
_.Ue=function(a,b){function c(){}c.prototype=b.prototype;a.Nb=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.base=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}};_.Ue(_.ca,Error);_.ca.prototype.name="CustomError";var Gea;_.Ue(baa,_.ca);baa.prototype.name="AssertionError";var Hea=void 0,Iea,Jea=typeof TextDecoder!=="undefined",eaa,daa=typeof TextEncoder!=="undefined";var Kea=!!(_.Ne[5]&16),Lea=!!(_.Ne[5]&32),Mea=!!(_.Ne[4]>>24&1),Nea=!!(_.Ne[5]&64);_.pa=Kea?Lea:Bea(610401301);_.Oea=Kea?Mea:Bea(1331761403);_.Pea=Kea?Nea:Bea(651175828);var Qea;Qea=_.ia.navigator;_.qa=Qea?Qea.userAgentData||null:null;var Rea=class{constructor(a){this.ha=a}};var Sea=new class{constructor(a){this.ka=a;this.ha=this.ma=void 0;this.na=!1}load(){const a=this;return _.Je(function*(){if(_.qa)return a.ha||(a.na=!0,a.ha=(()=>_.Je(function*(){try{const b=yield _.qa.getHighEntropyValues([a.ka]);a.ma=b[a.ka];return a.ma}finally{a.na=!1}}))()),yield a.ha})}}("platformVersion");var Tea;Tea=class{constructor(){this.ha=!1}load(){const a=this;return _.Je(function*(){if(ya(!0))return new Rea(yield Sea.load());a.ha=!0;return new Rea(_.saa())})}};_.Uea=new Tea;_.Da=function(a,b){return Array.prototype.indexOf.call(a,b,void 0)};_.Ve=function(a,b,c){Array.prototype.forEach.call(a,b,c)};_.Xe=function(a,b){return Array.prototype.filter.call(a,b,void 0)};_.Ye=function(a,b,c){return Array.prototype.map.call(a,b,c)};_.Ze=function(a,b){return Array.prototype.some.call(a,b,void 0)};var Vea=function(a){Vea[" "](a);return a};Vea[" "]=function(){};_.Wea=function(a,b){try{return Vea(a[b]),!0}catch(c){}return!1};_.Xea=function(a,b,c,d){d=d?d(b):b;return Object.prototype.hasOwnProperty.call(a,d)?a[d]:a[d]=c(b)};var jfa;_.Yea=_.jaa();_.$e=_.kaa();_.af=_.ra("Edge");_.Zea=_.af||_.$e;_.cf=_.ra("Gecko")&&!(_.oa("WebKit")&&!_.ra("Edge"))&&!(_.ra("Trident")||_.ra("MSIE"))&&!_.ra("Edge");_.df=_.oa("WebKit")&&!_.ra("Edge");_.$ea=_.df&&_.ra("Mobile");_.ef=_.Ba();_.gf=_.qaa();_.afa=_.paa()||_.raa();_.bfa=_.naa();_.cfa=_.oaa();_.dfa=_.ra("iPad");_.efa=_.ra("iPod");_.ffa=_.za();_.oa("KaiOS");var gfa=function(){const a=_.ia.document;return a?a.documentMode:void 0},hfa;
a:{let a="";const b=function(){const c=_.na();if(_.cf)return/rv:([^\);]+)(\)|;)/.exec(c);if(_.af)return/Edge\/([\d\.]+)/.exec(c);if(_.$e)return/\b(?:MSIE|rv)[: ]([^\);]+)(\)|;)/.exec(c);if(_.df)return/WebKit\/(\S+)/.exec(c);if(_.Yea)return/(?:Version)[ \/]?(\S+)/.exec(c)}();b&&(a=b?b[1]:"");if(_.$e){const c=gfa();if(c!=null&&c>parseFloat(a)){hfa=String(c);break a}}hfa=a}_.ifa=hfa;if(_.ia.document&&_.$e){var kfa=gfa();jfa=kfa?kfa:parseInt(_.ifa,10)||void 0}else jfa=void 0;_.lfa=jfa;_.hf={rXa:!1,tXa:!1,sXa:!1,pXa:!1,qXa:!1,uXa:!1};_.hf.T_=_.hf.rXa||_.hf.tXa||_.hf.sXa||_.hf.pXa||_.hf.qXa||_.hf.uXa;_.hf.XAa=_.Yea;_.hf.OYa=_.$e;_.hf.t_=_.af;_.hf.sM=_.hf.T_?_.hf.rXa:_.ta();_.hf.jXb=function(){return _.oaa()||_.ra("iPod")};_.hf.Qza=_.hf.T_?_.hf.tXa:_.hf.jXb();_.hf.Pza=_.hf.T_?_.hf.sXa:_.ra("iPad");_.hf.ANDROID=_.hf.T_?_.hf.pXa:_.maa();_.hf.CHROME=_.hf.T_?_.hf.qXa:_.wa();_.hf.IXb=function(){return _.xa()&&!_.za()};_.hf.XB=_.hf.T_?_.hf.uXa:_.hf.IXb();var mfa;mfa={};_.jf=null;_.kf=function(a,b){b===void 0&&(b=0);_.nfa();b=mfa[b];const c=Array(Math.floor(a.length/3)),d=b[64]||"";let e=0,f=0;for(;e<a.length-2;e+=3){var g=a[e],h=a[e+1],m=a[e+2],u=b[g>>2];g=b[(g&3)<<4|h>>4];h=b[(h&15)<<2|m>>6];m=b[m&63];c[f++]=u+g+h+m}u=0;m=d;switch(a.length-e){case 2:u=a[e+1],m=b[(u&15)<<2]||d;case 1:a=a[e],c[f]=b[a>>2]+b[(a&3)<<4|u>>4]+m+d}return c.join("")};
_.nfa=function(){if(!_.jf){_.jf={};var a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),b=["+/=","+/","-_=","-_.","-_"];for(let c=0;c<5;c++){const d=a.concat(b[c].split(""));mfa[c]=d;for(let e=0;e<d.length;e++){const f=d[e];_.jf[f]===void 0&&(_.jf[f]=e)}}}};var xaa,vaa,ofa;xaa=/[-_.]/g;vaa={"-":"+",_:"/",".":"="};_.lf={};ofa=typeof structuredClone!="undefined";var Yc,pfa;_.Eaa=function(a){return a?new _.ib(a,_.lf):Yc()};Yc=function(){return pfa||(pfa=new _.ib(null,_.lf))};_.Faa=function(a){return a.length?new _.ib(new Uint8Array(a),_.lf):Yc()};_.Aba=function(a){const b=a.ha;if(b==null)a="";else if(typeof b==="string")a=b;else{let c="",d=0;const e=b.length-10240;for(;d<e;)c+=String.fromCharCode.apply(null,b.subarray(d,d+=10240));c+=String.fromCharCode.apply(null,d?b.subarray(d):b);a=a.ha=btoa(c)}return a};
_.Bc=function(a){if(_.lf!==_.lf)throw Error("w");var b=a.ha;b==null||_.Pa(b)||(typeof b==="string"?b=_.yaa(b):(_.yb(b),b=null));return b==null?b:a.ha=b};_.ib=class{isEmpty(){return this.ha==null}constructor(a,b){if(b!==_.lf)throw Error("w");this.ha=a;if(a!=null&&a.length===0)throw Error("v");}};var Baa=void 0;var Vba,Qba,sba,Eca,Jba,Bba;_.Sb=Ta();Vba=Ta();Qba=Ta();_.Wb=Ta();_.qfa=Ta();sba=Ta();Eca=Ta();Jba=Ta();_.Za=Ta("m_m",!0);Bba=Ta();_.rfa=Ta();var nc,sfa;_.Ua=Ta("jas",!0);sfa=[];sfa[_.Ua]=7;nc=Object.freeze(sfa);var tfa;_.$a={};_.eb={};tfa=class{constructor(a,b,c){this.ha=a;this.ka=b;this.thisArg=c}next(){const a=this.ha.next();a.done||(a.value=this.ka.call(this.thisArg,a.value));return a}[Symbol.iterator](){return this}};_.mf=Object.freeze({});_.ufa=Object.freeze({});_.lb={};_.vfa=_.nb(a=>a!==null&&a!==void 0);var Jaa;_.pb=_.nb(a=>typeof a==="number");_.ob=_.nb(a=>typeof a==="string");Jaa=_.nb(a=>typeof a==="boolean");_.wfa=_.nb(a=>!!a&&(typeof a==="object"||typeof a==="function"));_.xfa=_.nb(a=>Array.isArray(a));var Iaa=typeof _.ia.BigInt==="function"&&typeof _.ia.BigInt(0)==="bigint";var Afa,yfa,Bfa,zfa;_.yba=_.nb(a=>Iaa?a>=yfa&&a<=zfa:a[0]==="-"?Kaa(a,Afa):Kaa(a,Bfa));Afa=Number.MIN_SAFE_INTEGER.toString();yfa=Iaa?BigInt(Number.MIN_SAFE_INTEGER):void 0;Bfa=Number.MAX_SAFE_INTEGER.toString();zfa=Iaa?BigInt(Number.MAX_SAFE_INTEGER):void 0;_.rb=0;_.sb=0;var Lb,Mb,Uaa;_.Ib=typeof BigInt==="function"?BigInt.asIntN:void 0;_.Nb=typeof BigInt==="function"?BigInt.asUintN:void 0;Lb=Number.isSafeInteger;_.Ab=Number.isFinite;Mb=Math.trunc;Uaa=/^-?([1-9][0-9]*|0)(\.[0-9]+)?$/;var Cba={};var Dfa,Efa;Dfa=(()=>class extends Map{constructor(){super()}})();Efa=function(a){if(a.sN&2)throw Error("A");};
_.ac=class extends Dfa{constructor(a,b,c=nba,d=nba){super();this.sN=a[_.Ua]|0;this.ZR=b;this.dea=c;this.oWa=this.ZR?oba:d;for(let e=0;e<a.length;e++){const f=a[e],g=c(f[0],!1,!0);let h=f[1];b?h===void 0&&(h=null):h=d(f[1],!1,!0,void 0,void 0,this.sN);super.set(g,h)}}l$b(){var a=xba;if(this.size!==0)return Array.from(super.entries(),a)}gVa(){return Array.from(super.entries())}clear(){Efa(this);super.clear()}delete(a){Efa(this);return super.delete(this.dea(a,!0,!1))}entries(){if(this.ZR){var a=super.keys();
a=new tfa(a,pba,this)}else a=super.entries();return a}values(){if(this.ZR){var a=super.keys();a=new tfa(a,_.ac.prototype.get,this)}else a=super.values();return a}forEach(a,b){this.ZR?super.forEach((c,d,e)=>{a.call(b,e.get(d),d,e)}):super.forEach(a,b)}set(a,b){Efa(this);a=this.dea(a,!0,!1);return a==null?this:b==null?(super.delete(a),this):super.set(a,this.oWa(b,!0,!0,this.ZR,!1,this.sN))}x4b(a){const b=this.dea(a[0],!1,!0);a=a[1];a=this.ZR?a===void 0?null:a:this.oWa(a,!1,!0,void 0,!1,this.sN);super.set(b,
a)}has(a){return super.has(this.dea(a,!1,!1))}get(a){a=this.dea(a,!1,!1);const b=super.get(a);if(b!==void 0){var c=this.ZR;return c?(c=this.oWa(b,!1,!0,c,this.fLb,this.sN),c!==b&&super.set(a,c),c):b}}[Symbol.iterator](){return this.entries()}};_.ac.prototype.toJSON=void 0;var uba=function(a,b){for(const c in a)!isNaN(c)&&b(a,+c,a[c])},wba=function(a){const b=new qba;uba(a,(c,d,e)=>{b[d]=[...e]});b.Bva=a.Bva;return b},qba=class{};var vba;_.of=ofa?structuredClone:a=>Yb(a,0,Zb);var Eba,Fba;_.Ffa=_.qb(0);_.pf={};_.qf=function(a,b,c,d,e){b=_.rc(a.Be,b,c,e);if(b!==null||d&&a.Jta!==_.eb)return b};_.rc=function(a,b,c,d){if(b===-1)return null;const e=b+(c?0:-1),f=a.length-1;let g,h;if(!(f<1+(c?0:-1))){if(e>=f)if(g=a[f],g!=null&&typeof g==="object"&&g.constructor===Object)c=g[b],h=!0;else if(e===f)c=g;else return;else c=a[e];if(d&&c!=null){d=d(c);if(d==null)return d;if(!Object.is(d,c))return h?g[b]=d:a[e]=d,d}return c}};_.rf=function(a,b,c,d){_.jc(a);const e=a.Be;_.mc(e,e[_.Ua]|0,b,c,d);return a};
_.sf=function(a,b,c,d){a=a.Be;return _.wc(a,a[_.Ua]|0,b,c,d)!==void 0};_.uf=function(a,b,c,d){const e=a.Be;return _.wc(e,e[_.Ua]|0,b,_.tf(a,d,c))!==void 0};_.Uc=function(a,b){return _.Uba(a,a[_.Ua]|0,b)};_.vf=function(a,b,c,d,e){_.zc(a,b,c,void 0,e,d,1);return a};_.Gfa=function(a,b){return _.qf(a,b,void 0,void 0,Sba)};_.wf=function(a){return a===_.mf?2:4};_.xf=function(a,b){a=_.Gfa(a,b);return a==null?Yc():a};
_.yf=function(a,b,c,d){_.jc(a);const e=a.Be;let f=e[_.Ua]|0;if(d==null){const g=Wba(e);if(Xba(g,e,f,c)===b)g.set(c,0);else return a}else f=_.Yba(e,f,c,b);_.mc(e,f,b,d);return a};_.zf=function(a,b,c,d){let e=a[_.Ua]|0;const f=_.mb(e);e=_.Yba(a,e,c,b,f);_.mc(a,e,b,d,f)};_.tf=function(a,b,c){return _.Af(a,b)===c?c:-1};_.Af=function(a,b,c){a=a.Be;return Xba(Wba(a),a,void 0,b,c)};
_.Hfa=function(a,b,c){let d=a[_.Ua]|0;const e=_.mb(d),f=_.rc(a,c,e);let g;if(_.cb(f)){if(!_.fb(f))return _.ic(f),f.Be;g=f.Be}else Array.isArray(f)&&(g=f);if(g){const h=g[_.Ua]|0;h&2&&(g=_.ec(g,h))}g=_.bc(g,b,!0);g!==f&&_.mc(a,d,c,g,e);return g};_.Bf=function(a,b,c,d){a=a.Be;(c=_.wc(a,a[_.Ua]|0,b,c,d))||(c=b[_.Sb])||(c=new b,_.Wa(c.Be),c=b[_.Sb]=c);return c};
_.n=function(a,b,c,d){let e=a.Be,f=e[_.Ua]|0;b=_.wc(e,f,b,c,d);if(b==null)return b;f=e[_.Ua]|0;if(!_.fb(a,f)){const g=_.Ub(b);g!==b&&(_.ic(a)&&(e=a.Be,f=e[_.Ua]|0),b=g,f=_.mc(e,f,c,b,d),_.lc(e,f))}return b};_.Cf=function(a,b,c,d){return _.Bf(a,b,_.tf(a,d,c))};_.Ef=function(a,b,c,d,e){const f=a.Be;return _.xc(a,f,f[_.Ua]|0,b,c,d,e,!1,!0)};_.Ff=function(a,b,c,d,e){d=_.Zba(d);_.rf(a,c,d,e);d&&!_.fb(d)&&_.lc(a.Be);return a};
_.Gf=function(a,b,c,d){_.jc(a);const e=a.Be;let f=e[_.Ua]|0;if(c==null)return _.mc(e,f,b,void 0,d),a;let g=c===nc?7:c[_.Ua]|0,h=g;const m=qc(g),u=m||Object.isFrozen(c);let x=!0,A=!0;for(let K=0;K<c.length;K++){var E=c[K];m||(E=_.fb(E),x&&(x=!E),A&&(A=E))}m||(g=x?13:5,g=A?g&-4097:g|4096);u&&g===h||(c=[...c],h=0,g=oc(g,f));g!==h&&(c[_.Ua]=g);f=_.mc(e,f,b,c,d);2&g||!(4096&g||16&g)||_.lc(e,f);return a};_.Hf=function(a,b,c,d,e){_.zc(a,b,c,void 0,d,e);return a};
_.If=function(a,b,c,d){return _.hba(_.qf(a,b,c,d))};_.Jf=function(a,b,c,d){return _.Fb(_.qf(a,b,c,d))};_.Kf=function(a,b,c,d){return _.Qb(_.qf(a,b,c,d))};_.Nf=function(a,b,c=!1){let d;return(d=_.zb(_.qf(a,b)))!=null?d:c};_.Of=function(a,b,c=0,d){let e;return(e=_.Jf(a,b,d))!=null?e:c};_.Pf=function(a,b,c=_.Ffa,d){let e;return(e=_.If(a,b,d))!=null?e:c};_.Qf=function(a,b,c=0){let d;return(d=_.qf(a,b,void 0,void 0,_.xb))!=null?d:c};_.p=function(a,b,c="",d){let e;return(e=_.Kf(a,b,d))!=null?e:c};
_.Rf=function(a,b,c=0){let d;return(d=_.Db(_.qf(a,b)))!=null?d:c};_.Sf=function(a,b,c,d,e){return _.pc(a,b,_.Qb,c,e,void 0,d)};_.Tf=function(a,b,c){a=_.Sf(a,b,3,void 0,!0);_.kb(a,c);return a[c]};_.Uf=function(a,b,c,d,e){return _.pc(a,b,_.Db,c,e,void 0,d)};_.Vf=function(a,b,c){return _.p(a,_.tf(a,c,b))};_.Wf=function(a,b,c,d){return _.n(a,b,_.tf(a,d,c),void 0)};_.Xf=function(a,b,c){return _.zb(_.qf(a,b,c,_.pf))};_.Yf=function(a,b,c){return _.Kf(a,b,c,_.pf)};
_.Zf=function(a,b,c,d){return _.rf(a,b,c==null?c:_.Taa(c),d)};_.Ifa=function(a,b,c,d){return _.yf(a,b,c,d==null?d:_.Taa(d))};_.$f=function(a,b,c){return _.rf(a,b,_.Vaa(c))};_.ag=function(a,b,c){return _.tc(a,b,_.Vaa(c),0)};_.bg=function(a,b,c){return _.rf(a,b,_.Kb(c))};_.cg=function(a,b,c){return _.tc(a,b,_.Kb(c),"0")};_.dg=function(a,b,c,d){return _.rf(a,b,_.Pb(c),d)};_.eg=function(a,b,c){return _.tc(a,b,_.jb(c,!1,!0),Yc())};_.fg=function(a,b,c){return _.rf(a,b,c==null?c:_.Cb(c))};
_.gg=function(a,b,c){return _.Kf(a,b,c)!=null};var Ac=class{constructor(a,b,c){this.buffer=a;if(c&&!b)throw Error();this.xda=b}};var Wca,Jfa,Kfa,Lfa,Mfa;_.ig=function(a,b){let c,d=0,e=0,f=0;const g=a.ma;let h=a.ha;do c=g[h++],d|=(c&127)<<f,f+=7;while(f<32&&c&128);f>32&&(e|=(c&127)>>4);for(f=3;f<32&&c&128;f+=7)c=g[h++],e|=(c&127)<<f;_.hg(a,h);if(c<128)return b(d>>>0,e>>>0);throw Error("R");};_.jg=function(a){let b=0,c=a.ha;const d=c+10,e=a.ma;for(;c<d;){const f=e[c++];b|=f;if((f&128)===0)return _.hg(a,c),!!(b&127)}throw Error("R");};
_.Wc=function(a){const b=a.ma;let c=a.ha,d=b[c++],e=d&127;if(d&128&&(d=b[c++],e|=(d&127)<<7,d&128&&(d=b[c++],e|=(d&127)<<14,d&128&&(d=b[c++],e|=(d&127)<<21,d&128&&(d=b[c++],e|=d<<28,d&128&&b[c++]&128&&b[c++]&128&&b[c++]&128&&b[c++]&128&&b[c++]&128)))))throw Error("R");_.hg(a,c);return e};_.kg=function(a){return _.Wc(a)>>>0};_.lg=function(a){return _.ig(a,Oaa)};_.mg=function(a){var b=a.ma;const c=a.ha,d=b[c],e=b[c+1],f=b[c+2];b=b[c+3];_.hg(a,a.ha+4);return(d<<0|e<<8|f<<16|b<<24)>>>0};
_.ng=function(a){var b=_.mg(a);const c=_.mg(a);a=(c>>31)*2+1;const d=c>>>20&2047;b=4294967296*(c&1048575)+b;return d==2047?b?NaN:a*Infinity:d==0?a*4.9E-324*b:a*Math.pow(2,d-1075)*(b+4503599627370496)};Wca=function(a){return _.Wc(a)};_.hg=function(a,b){a.ha=b;if(b>a.ka)throw Error("S`"+b+"`"+a.ka);};Jfa=function(a,b){if(b<0)throw Error("T`"+b);const c=a.ha,d=c+b;if(d>a.ka)throw Error("S`"+(a.ka-c)+"`"+b);a.ha=d;return c};
Kfa=function(a,b){if(b==0)return Yc();var c=Jfa(a,b);a.kEa&&a.oa?c=a.ma.subarray(c,c+b):(a=a.ma,b=c+b,c=c===b?new Uint8Array(0):a.slice(c,b));return c.length==0?Yc():new _.ib(c,_.lf)};
Lfa=class{constructor(a,b,c,d){this.ma=null;this.oa=!1;this.ha=this.ka=this.na=0;this.init(a,b,c,d)}init(a,b,c,{kEa:d=!1,zVa:e=!1}={}){this.kEa=d;this.zVa=e;a&&(a=_.$ba(a,this.zVa),this.ma=a.buffer,this.oa=a.xda,this.na=b||0,this.ka=c!==void 0?this.na+c:this.ma.length,this.ha=this.na)}clear(){this.ma=null;this.oa=!1;this.ha=this.ka=this.na=0;this.kEa=!1}reset(){this.ha=this.na}};Mfa=[];var vca,wca,Ofa,Nfa;vca=function(a,b,c,d){if(Nfa.length){const e=Nfa.pop();e.setOptions(d);e.ka.init(a,b,c,d);return e}return new Ofa(a,b,c,d)};wca=function(a){a.ka.clear();a.oa=-1;a.na=-1;a.ha=-1;Nfa.length<100&&Nfa.push(a)};_.qca=function(a){var b=a.ka;if(b.ha==b.ka)return!1;a.ma=a.ka.ha;b=_.kg(a.ka);const c=b>>>3,d=b&7;if(!(d>=0&&d<=5))throw Error("L`"+d+"`"+a.ma);if(c<1)throw Error("M`"+c+"`"+a.ma);a.oa=b;a.na=c;a.ha=d;return!0};
_.og=function(a){switch(a.ha){case 0:a.ha!=0?_.og(a):_.jg(a.ka);break;case 1:a=a.ka;_.hg(a,a.ha+8);break;case 2:_.Pfa(a);break;case 5:a=a.ka;_.hg(a,a.ha+4);break;case 3:const b=a.na;do{if(!_.qca(a))throw Error("O");if(a.ha==4){if(a.na!=b)throw Error("P");break}_.og(a)}while(1);break;default:throw Error("L`"+a.ha+"`"+a.ma);}};_.Pfa=function(a){if(a.ha!=2)_.og(a);else{var b=_.kg(a.ka);a=a.ka;_.hg(a,a.ha+b)}};_.Qfa=function(a,b){if(!a.X7a){const c=a.ka.ha-b;a.ka.ha=b;return Kfa(a.ka,c)}};
_.sca=function(a){const b=a.ma;_.og(a);return _.Qfa(a,b)};_.pg=function(a,b,c){const d=a.ka.ka,e=_.kg(a.ka),f=a.ka.ha+e;let g=f-d;g<=0&&(a.ka.ka=f,c(b,a,void 0,void 0,void 0),g=f-a.ka.ha);if(g)throw Error("K`"+e+"`"+(e-g));a.ka.ha=f;a.ka.ka=d;return b};
_.Rfa=function(a){var b=_.kg(a.ka);a=a.ka;var c=Jfa(a,b);a=a.ma;if(Jea){var d=a,e;(e=Iea)||(e=Iea=new TextDecoder("utf-8",{fatal:!0}));b=c+b;d=c===0&&b===d.length?d:d.subarray(c,b);try{var f=e.decode(d)}catch(h){if(Hea===void 0){try{e.decode(new Uint8Array([128]))}catch(m){}try{e.decode(new Uint8Array([97])),Hea=!0}catch(m){Hea=!1}}!Hea&&(Iea=void 0);throw h;}}else{f=c;b=f+b;c=[];let h=null;let m;for(;f<b;){var g=a[f++];g<128?c.push(g):g<224?f>=b?ea():(m=a[f++],g<194||(m&192)!==128?(f--,ea()):c.push((g&
31)<<6|m&63)):g<240?f>=b-1?ea():(m=a[f++],(m&192)!==128||g===224&&m<160||g===237&&m>=160||((e=a[f++])&192)!==128?(f--,ea()):c.push((g&15)<<12|(m&63)<<6|e&63)):g<=244?f>=b-2?ea():(m=a[f++],(m&192)!==128||(g<<28)+(m-144)>>30!==0||((e=a[f++])&192)!==128||((d=a[f++])&192)!==128?(f--,ea()):(g=(g&7)<<18|(m&63)<<12|(e&63)<<6|d&63,g-=65536,c.push((g>>10&1023)+55296,(g&1023)+56320))):ea();c.length>=8192&&(h=caa(h,c),c.length=0)}f=caa(h,c)}return f};_.Uca=function(a){const b=_.kg(a.ka);return Kfa(a.ka,b)};
_.Vc=function(a,b,c){var d=_.kg(a.ka);for(d=a.ka.ha+d;a.ka.ha<d;)c.push(b(a.ka))};Ofa=class{constructor(a,b,c,d){if(Mfa.length){const e=Mfa.pop();e.init(a,b,c,d);a=e}else a=new Lfa(a,b,c,d);this.ka=a;this.ma=this.ka.ha;this.ha=this.oa=this.na=-1;this.setOptions(d)}setOptions({X7a:a=!1}={}){this.X7a=a}reset(){this.ka.reset();this.ma=this.ka.ha;this.ha=this.na=this.oa=-1}};Nfa=[];var Sfa,Tfa,Vfa;_.Sc=function(a){a=BigInt.asUintN(64,a);return new _.qg(Number(a&BigInt(4294967295)),Number(a>>BigInt(32)))};_.Cc=function(a){if(!a)return Sfa||(Sfa=new _.qg(0,0));if(!/^\d+$/.test(a))return null;Raa(a);return new _.qg(_.rb,_.sb)};_.qg=class{constructor(a,b){this.ka=a>>>0;this.ha=b>>>0}};_.Ufa=function(a){a=BigInt.asUintN(64,a);return new Tfa(Number(a&BigInt(4294967295)),Number(a>>BigInt(32)))};
_.Jca=function(a){if(!a)return Vfa||(Vfa=new Tfa(0,0));if(!/^-?\d+$/.test(a))return null;Raa(a);return new Tfa(_.rb,_.sb)};Tfa=class{constructor(a,b){this.ka=a>>>0;this.ha=b>>>0}};var Wfa;_.rg=function(a,b,c){for(;c>0||b>127;)a.ha.push(b&127|128),b=(b>>>7|c<<25)>>>0,c>>>=7;a.ha.push(b)};_.Rc=function(a,b){a.ha.push(b>>>0&255);a.ha.push(b>>>8&255);a.ha.push(b>>>16&255);a.ha.push(b>>>24&255)};_.sg=function(a,b){for(;b>127;)a.ha.push(b&127|128),b>>>=7;a.ha.push(b)};_.Qc=function(a,b){if(b>=0)_.sg(a,b);else{for(let c=0;c<9;c++)a.ha.push(b&127|128),b>>=7;a.ha.push(1)}};
_.Hca=function(a,b){const c=_.Cfa||(_.Cfa=new DataView(new ArrayBuffer(8)));c.setFloat64(0,+b,!0);_.rb=c.getUint32(0,!0);_.sb=c.getUint32(4,!0);_.Rc(a,_.rb);_.Rc(a,_.sb)};Wfa=class{constructor(){this.ha=[]}length(){return this.ha.length}end(){const a=this.ha;this.ha=[];return a}writeUint8(a){this.ha.push(a>>>0&255)}writeInt8(a){this.ha.push(a>>>0&255)}};var Ic,Xfa;Ic=function(a,b){b.length!==0&&(a.ma.push(b),a.ka+=b.length)};_.Oc=function(a,b,c){_.sg(a.ha,b*8+c)};_.tg=function(a,b){_.Oc(a,b,2);b=a.ha.end();Ic(a,b);b.push(a.ka);return b};_.vg=function(a,b){var c=b.pop();for(c=a.ka+a.ha.length()-c;c>127;)b.push(c&127|128),c>>>=7,a.ka++;b.push(c);a.ka++};
_.Kca=function(a,b,c){if(c!=null)switch(_.Oc(a,b,0),typeof c){case "number":a=a.ha;_.ub(c);_.rg(a,_.rb,_.sb);break;case "bigint":c=_.Ufa(c);_.rg(a.ha,c.ka,c.ha);break;default:c=_.Jca(c),_.rg(a.ha,c.ka,c.ha)}};_.Rca=function(a,b,c){c!=null&&(c=parseInt(c,10),_.Oc(a,b,0),_.Qc(a.ha,c))};_.Tc=function(a,b,c){_.Oc(a,b,2);_.sg(a.ha,c.length);Ic(a,a.ha.end());Ic(a,c)};_.dca=function(a,b,c,d){c!=null&&(b=_.tg(a,b),d(c,a),_.vg(a,b))};Xfa=class{constructor(){this.ma=[];this.ka=0;this.ha=new Wfa}};var bca;bca=Dc();_.Yfa=Dc();_.wg=Dc();_.Zfa=Dc();_.xg=Dc();_.yg=Dc();_.$fa=Dc();_.aga=Dc();_.zg=Dc();_.Ag=Dc();_.bga=Dc();_.Bg=Dc();_.Cg=Dc();_.Eg=Dc();_.Fg=Dc();_.bd=function(a,b){if(b==null||b=="")return new a;b=JSON.parse(b);if(!Array.isArray(b))throw Error("V");return new a(_.Daa(b))};_.l=class{constructor(a,b,c){this.Be=Hba(a,b,c)}toJSON(){return Dba(this)}serialize(a){return JSON.stringify(Dba(this,a))}clone(){const a=this.Be,b=a[_.Ua]|0;return _.hc(this,a,b)?_.Nba(this,a,!0):new this.constructor(_.ec(a,b,!1))}xda(){return _.fb(this)}};_.l.prototype.IW=_.aa(0);_.l.prototype[_.Za]=_.$a;_.l.prototype.toString=function(){return this.Be.toString()};var Ec,jca,kca,yca,pca,Bca,gca,hca;Ec=class{constructor(a,b,c){this.ha=a;this.ka=b;a=_.Vb(bca);(a=!!a&&c===a)||(a=_.Vb(_.Yfa),a=!!a&&c===a);this.ma=a}};jca=_.cca(function(a,b,c,d,e){if(a.ha!==2)return!1;_.pg(a,_.Hfa(b,d,c),e);return!0},fca);kca=_.cca(function(a,b,c,d,e){if(a.ha!==2)return!1;_.pg(a,_.Hfa(b,d,c),e);return!0},fca);yca=Symbol();_.Gc=Symbol();pca=Symbol();Bca=Symbol();_.tca=Symbol();_.cga=(a,b)=>{const c=new Xfa;Cca(a.Be,c,_.Fc(yca,xca,zca,b));Ic(c,c.ha.end());a=new Uint8Array(c.ka);b=c.ma;const d=b.length;let e=0;for(let f=0;f<d;f++){const g=b[f];a.set(g,e);e+=g.length}c.ma=[a];return a};_.Gg=_.Kc(function(a,b,c,d){if(a.ha!==1)return!1;_.zf(b,c,d,_.ng(a.ka));return!0},_.Ica,_.Cg);_.Hg=_.Kc(function(a,b,c){if(a.ha!==0)return!1;_.Nc(b,c,_.lg(a.ka));return!0},_.Pc,_.zg);_.Kg=_.Kc(function(a,b,c){if(a.ha!==0)return!1;a=_.lg(a.ka);_.Nc(b,c,a===0?void 0:a);return!0},_.Pc,_.zg);
_.dga=_.Kc(function(a,b,c,d){if(a.ha!==0)return!1;_.zf(b,c,d,_.lg(a.ka));return!0},_.Pc,_.zg);_.Lg=_.Kc(function(a,b,c){if(a.ha!==0)return!1;_.Nc(b,c,_.Wc(a.ka));return!0},_.Lca,_.xg);_.Mg=_.Lc(_.Tca,function(a,b,c){b=_.Jc(_.Fb,b,!0);if(b!=null)for(let g=0;g<b.length;g++){var d=a,e=c,f=b[g];f!=null&&(_.Oc(d,e,0),_.Qc(d.ha,f))}},_.xg);_.Ng=_.Kc(function(a,b,c){if(a.ha!==0)return!1;a=_.Wc(a.ka);_.Nc(b,c,a===0?void 0:a);return!0},_.Lca,_.xg);
_.Og=_.Kc(function(a,b,c){if(a.ha!==0)return!1;_.Nc(b,c,_.jg(a.ka));return!0},_.Nca,_.wg);_.Pg=_.Kc(function(a,b,c,d){if(a.ha!==0)return!1;_.zf(b,c,d,_.jg(a.ka));return!0},_.Nca,_.wg);_.q=_.Kc(function(a,b,c){if(a.ha!==2)return!1;_.Nc(b,c,_.Rfa(a));return!0},Oca,_.Zfa);_.Qg=_.Kc(function(a,b,c){if(a.ha!==2)return!1;a=_.Rfa(a);_.Nc(b,c,a===""?void 0:a);return!0},Oca,_.Zfa);_.Rg=_.Kc(function(a,b,c,d){if(a.ha!==2)return!1;_.zf(b,c,d,_.Rfa(a));return!0},Oca,_.Zfa);
_.r=_.Fca(function(a,b,c,d,e){if(a.ha!==2)return!1;_.pg(a,_.Gca(b,d,c),e);return!0},function(a,b,c,d,e){if(Array.isArray(b))for(let f=0;f<b.length;f++)Pca(a,b[f],c,d,e)});_.t=_.cca(function(a,b,c,d,e,f){if(a.ha!==2)return!1;let g=b[_.Ua]|0;_.Yba(b,g,f,c,_.mb(g));b=_.Hfa(b,d,c);_.pg(a,b,e);return!0},Pca);_.Sg=_.Kc(function(a,b,c){if(a.ha!==2)return!1;_.Nc(b,c,_.Uca(a));return!0},_.Qca,_.Eg);_.Tg=_.Kc(function(a,b,c){if(a.ha!==0)return!1;_.Nc(b,c,_.Wc(a.ka));return!0},_.Sca,_.Fg);_.ega=new Map;var Yca=class{constructor(a,b,c){this.ha=a;this.ctor=c;this.ma=_.n;this.na=_.Ff;this.defaultValue=void 0;this.ka=b.messageId!=null?_.lb:void 0}register(){Vea(this)}};_.Ug=class extends _.l{constructor(a){super(a)}getTypeName(){return _.p(this,1).split("/").pop()}getValue(){const a=_.qf(this,2);if(Array.isArray(a)||a instanceof _.l)throw Error("W");return _.xf(this,2)}setValue(a){if(a==null)a=this;else if(Array.isArray(a))a=_.rf(this,2,Yb(a,0,Zb));else if(typeof a==="string"||a instanceof _.ib||_.Pa(a))a=_.eg(this,2,a);else throw Error("U`"+a);return a}};_.fga=[0,_.Qg,_.Kc(_.Vca,function(a,b,c){if(b!=null){if(b instanceof _.l){const d=b.Fpc;d?(b=d(b),b!=null&&_.Tc(a,c,_.$ba(b,!0).buffer)):_.Sa(Eca,3);return}if(Array.isArray(b)){_.Sa(Eca,3);return}}_.Qca(a,b,c)},_.Eg)];_.Vg=class{constructor(a,b){this.key=a;this.defaultValue=b;this.flagNameForDebugging=void 0}ctor(a){return typeof a==="boolean"?a:this.defaultValue}};_.Wg=class{constructor(a,b){this.key=a;this.defaultValue=b;this.flagNameForDebugging=void 0}ctor(a){return typeof a==="number"?a:this.defaultValue}};_.Xg=class{constructor(a,b){this.key=a;this.defaultValue=b;this.flagNameForDebugging=void 0}ctor(a){return typeof a==="string"?a:this.defaultValue}};var gga=class extends _.l{constructor(a){super(a)}},hga=[1];var iga=class extends _.l{constructor(a){super(a)}setBooleanValue(a){return _.Ifa(this,3,Yg,a)}},Yg=[2,3,4,5,6,8];var jga=class extends _.l{constructor(a){super(a)}g3(){return _.xf(this,3)}};var kga=class extends _.l{constructor(a){super(a)}},lga=_.cd(kga);var mga=_.$c(kga,[0,_.r,[0,[0,_.q],_.r,[0,Yg,_.Hg,_.dga,_.Pg,_.Gg,_.Rg,_.t,_.fga,_.q,_.t,[0,hga,_.Rg]],_.Sg,-1]]);var ah,pga,qga,nga,oga;ah=function(a,b){return new _.$g(a,b)};_.ch=function(a){return a>0?a>=0x7fffffffffffffff?nga:new _.$g(a,a/4294967296):a<0?a<=-0x7fffffffffffffff?oga:(new _.$g(-a,-a/4294967296)).negate():_.bh};
_.$g=class{constructor(a,b){this.Wj=a|0;this.mi=b|0}toNumber(){return this.mi*4294967296+(this.Wj>>>0)}isSafeInteger(){const a=this.mi>>21;return a==0||a==-1&&!(this.Wj==0&&this.mi==-2097152)}toString(a){a=a||10;if(a<2||36<a)throw Error("aa`"+a);if(this.isSafeInteger()){var b=this.toNumber();return a==10?""+b:b.toString(a)}b=14-(a>>2);var c=Math.pow(a,b),d=ah(c,c/4294967296);c=this.div(d);d=Math.abs(this.subtract(c.multiply(d)).toNumber());let e=a==10?""+d:d.toString(a);e.length<b&&(e="0000000000000".slice(e.length-
b)+e);d=c.toNumber();return(a==10?d:d.toString(a))+e}J4(){return this.Wj==0&&this.mi==0}En(){return this.Wj^this.mi}equals(a){return this.Wj==a.Wj&&this.mi==a.mi}compare(a){return this.mi==a.mi?this.Wj==a.Wj?0:this.Wj>>>0>a.Wj>>>0?1:-1:this.mi>a.mi?1:-1}negate(){const a=~this.Wj+1|0;return ah(a,~this.mi+!a|0)}add(a){const b=this.mi>>>16,c=this.mi&65535;var d=this.Wj>>>16;const e=a.mi>>>16,f=a.mi&65535;var g=a.Wj>>>16;a=(this.Wj&65535)+(a.Wj&65535);g=(a>>>16)+(d+g);d=g>>>16;d+=c+f;return ah((g&65535)<<
16|a&65535,((d>>>16)+(b+e)&65535)<<16|d&65535)}subtract(a){return this.add(a.negate())}multiply(a){if(this.J4())return this;if(a.J4())return a;const b=this.mi>>>16,c=this.mi&65535,d=this.Wj>>>16,e=this.Wj&65535,f=a.mi>>>16,g=a.mi&65535,h=a.Wj>>>16;a=a.Wj&65535;let m,u,x,A;A=e*a;x=(A>>>16)+d*a;u=x>>>16;x=(x&65535)+e*h;u+=x>>>16;u+=c*a;m=u>>>16;u=(u&65535)+d*h;m+=u>>>16;u=(u&65535)+e*g;m=m+(u>>>16)+(b*a+c*h+d*g+e*f)&65535;return ah((x&65535)<<16|A&65535,m<<16|u&65535)}div(a){if(a.J4())throw Error("ba");
if(this.mi<0){if(this.equals(oga)){if(a.equals(pga)||a.equals(qga))return oga;if(a.equals(oga))return pga;var b=this.mi;b=ah(this.Wj>>>1|b<<31,b>>1);b=b.div(a).shiftLeft(1);if(b.equals(_.bh))return a.mi<0?pga:qga;var c=this.subtract(a.multiply(b));return b.add(c.div(a))}return a.mi<0?this.negate().div(a.negate()):this.negate().div(a).negate()}if(this.J4())return _.bh;if(a.mi<0)return a.equals(oga)?_.bh:this.div(a.negate()).negate();b=_.bh;for(c=this;c.compare(a)>=0;){let e=Math.max(1,Math.floor(c.toNumber()/
a.toNumber()));var d=Math.ceil(Math.log(e)/Math.LN2);d=d<=48?1:Math.pow(2,d-48);let f=_.ch(e),g=f.multiply(a);for(;g.mi<0||g.compare(c)>0;)e-=d,f=_.ch(e),g=f.multiply(a);f.J4()&&(f=pga);b=b.add(f);c=c.subtract(g)}return b}not(){return ah(~this.Wj,~this.mi)}and(a){return ah(this.Wj&a.Wj,this.mi&a.mi)}or(a){return ah(this.Wj|a.Wj,this.mi|a.mi)}xor(a){return ah(this.Wj^a.Wj,this.mi^a.mi)}shiftLeft(a){a&=63;if(a==0)return this;const b=this.Wj;return a<32?ah(b<<a,this.mi<<a|b>>>32-a):ah(0,b<<a-32)}};
_.bh=ah(0,0);pga=ah(1,0);qga=ah(-1,-1);nga=ah(4294967295,2147483647);oga=ah(0,**********);_.bda=class{constructor(a){this.ka=!1;a?(a=mga(a),a=_.Ef(a,jga,1,_.wf())[0]):(this.ka=!0,a=lga("["+_.$ca("TSDtV",window).substring(4)),a=_.Ef(a,jga,1,_.wf())[0]);if(a)for(var b of _.Ef(a,iga,2,_.wf()))if(_.uf(b,_.Ug,6,Yg))throw Error();var c=a;if(c){b={};for(d of _.Ef(c,iga,2,_.wf()))switch(c=_.Pf(d,1).toString(),_.Af(d,Yg)){case 3:b[c]=_.Nf(d,_.tf(d,Yg,3));break;case 2:b[c]=_.dd(_.Pf(d,_.tf(d,Yg,2)));break;case 4:b[c]=_.Qf(d,_.tf(d,Yg,4));break;case 5:b[c]=_.Vf(d,5,Yg);break;case 6:b[c]=_.Wf(d,_.Ug,
6,Yg);break;case 8:const e=_.Cf(d,gga,8,Yg);switch(_.Af(e,hga)){case 1:b[c]=_.Vf(e,1,hga);break;default:throw Error("ca`"+_.Af(e,hga));}break;default:throw Error("ca`"+_.Af(d,Yg));}var d=b}else d={};this.ha=d;this.token=a?a.g3():null}Ba(a){return!this.ka||a.key in this.ha?a.ctor(this.ha[a.key]):a.defaultValue}g3(){return this.token}};var ada;_.rga=new _.Vg("45656894",!1);var sga=new _.Vg("45659183",!1);var cda;_.tga=function(a,b,c=!1){a.Saa=a.Saa.concat(b);if(c){if(!a.p5)throw Error("da`"+a.pRa);b.map(d=>d.l3()).forEach(d=>{_.dda(e=>{e.nma(a.p5,d)})})}};_.dh=class{constructor(a,b,c,d=!1){c=c||[];this.pRa=a;this.p5=b||null;this.Saa=[];_.tga(this,c,d)}toString(){return this.pRa}l3(){return this.p5}fK(){return this.Saa}};_.uga=new _.dh("n73qwf","n73qwf");_.eh=function(a,b){this.x=a!==void 0?a:0;this.y=b!==void 0?b:0};_.eh.prototype.clone=function(){return new _.eh(this.x,this.y)};_.eh.prototype.equals=function(a){return a instanceof _.eh&&_.fh(this,a)};_.fh=function(a,b){return a==b?!0:a&&b?a.x==b.x&&a.y==b.y:!1};_.k=_.eh.prototype;_.k.ceil=function(){this.x=Math.ceil(this.x);this.y=Math.ceil(this.y);return this};_.k.floor=function(){this.x=Math.floor(this.x);this.y=Math.floor(this.y);return this};
_.k.round=function(){this.x=Math.round(this.x);this.y=Math.round(this.y);return this};_.k.translate=function(a,b){a instanceof _.eh?(this.x+=a.x,this.y+=a.y):(this.x+=Number(a),typeof b==="number"&&(this.y+=b));return this};_.k.scale=function(a,b){this.x*=a;this.y*=typeof b==="number"?b:a;return this};_.gh=function(a,b){this.width=a;this.height=b};_.k=_.gh.prototype;_.k.clone=function(){return new _.gh(this.width,this.height)};_.k.area=function(){return this.width*this.height};_.k.aspectRatio=function(){return this.width/this.height};_.k.isEmpty=function(){return!this.area()};_.k.ceil=function(){this.width=Math.ceil(this.width);this.height=Math.ceil(this.height);return this};_.k.floor=function(){this.width=Math.floor(this.width);this.height=Math.floor(this.height);return this};
_.k.round=function(){this.width=Math.round(this.width);this.height=Math.round(this.height);return this};_.k.scale=function(a,b){this.width*=a;this.height*=typeof b==="number"?b:a;return this};var fda="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");_.td={};var vga=globalThis.trustedTypes,gda=vga,ida;_.sd=class{constructor(a,b){this.ha=b}toString(){return this.ha+""}};lda(a=>a``)||lda(a=>a`\0`)||lda(a=>a`\n`)||lda(a=>a`\u0000`);_.mda=class{constructor(a,b){this.ha=b}toString(){return this.ha}};_.wga=_.wd("about:blank");_.Fd=_.wd("about:invalid#zClosurez");var Bd,nda,pda,xga,rda;Bd=class{constructor(a){this.eu=a}};
_.hh={Zic:Cd("tel"),icc:new Bd(a=>/^callto:\+?\d*$/i.test(a)),Jic:new Bd(a=>a.indexOf("ssh://")===0),Ohc:Cd("rtsp"),qtb:Cd("data"),Txb:Cd("http"),Uxb:Cd("https"),EXTENSION:new Bd(a=>a.indexOf("chrome-extension://")===0||a.indexOf("moz-extension://")===0||a.indexOf("ms-browser-extension://")===0),Pwb:Cd("ftp"),sCb:new Bd(a=>/^[^:]*([/?#]|$)/.test(a)),aAb:Cd("mailto"),efc:Cd("intent"),fgc:Cd("market"),wfc:Cd("itms"),xfc:Cd("itms-appss"),yfc:Cd("itms-services"),vdc:Cd("fb-messenger"),Pjc:Cd("whatsapp"),
wic:new Bd(a=>a.indexOf("sip:")===0||a.indexOf("sips:")===0),Bic:Cd("sms"),Jjc:Cd("vnd.youtube"),xec:Cd("googlehome"),yec:Cd("googlehomesdk")};nda=[_.hh.qtb,_.hh.Txb,_.hh.Uxb,_.hh.aAb,_.hh.Pwb,_.hh.sCb];pda=typeof URL==="function";xga=["data:","http:","https:","mailto:","ftp:"];rda=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;_.oda=()=>{};_.Kd=class{constructor(a,b){this.ha=b}toString(){return this.ha+""}};_.ih=new _.Kd(_.td,vga?vga.emptyHTML:"");_.yga={fec:0,adc:1,bdc:2,0:"FORMATTED_HTML_CONTENT",1:"EMBEDDED_INTERNAL_CONTENT",2:"EMBEDDED_TRUSTED_EXTERNAL_CONTENT"};_.Sd=class extends Error{constructor(a,b){super(`${a} cannot be used with intent ${_.yga[b]}`);this.type=a;this.intent=b;this.name="TypeCannotBeUsedWithIframeIntentError"}};_.Yd=class{constructor(a,b){this.ha=b}toString(){return this.ha+""}};_.zga=new _.Yd(_.td,vga?vga.emptyScript:"");_.xda=class{constructor(a,b){this.ha=b}toString(){return this.ha}};var Bda="alternate author bookmark canonical cite help icon license modulepreload next prefetch dns-prefetch prerender preconnect preload prev search subresource".split(" ");_.jh=function(a){return encodeURIComponent(String(a))};_.Aga=function(a){return decodeURIComponent(a.replace(/\+/g," "))};_.Bga=Math.random()***********|0;_.kh=function(a){return String(a).replace(/([A-Z])/g,"-$1").toLowerCase()};var Jda=/^[a-z][a-z\d-]*$/i,Kda="APPLET BASE EMBED IFRAME LINK MATH META OBJECT SCRIPT STYLE SVG TEMPLATE".split(" "),Nda="AREA BR COL COMMAND HR IMG INPUT KEYGEN PARAM SOURCE TRACK WBR".split(" "),Oda=["action","formaction","href"];var Cga;Cga=function(a,b,c){c=a.ha.get(c);return(c==null?0:c.has(b))?c.get(b):a.na.has(b)?{Pp:1}:(c=a.oa.get(b))?c:a.ka&&[...a.ka].some(d=>b.indexOf(d)===0)?{Pp:1}:{Pp:0}};_.lh=class{constructor(a,b,c,d,e){this.ma=a;this.ha=b;this.na=c;this.oa=d;this.ka=e}};var Dga="ARTICLE SECTION NAV ASIDE H1 H2 H3 H4 H5 H6 HEADER FOOTER ADDRESS P HR PRE BLOCKQUOTE OL UL LH LI DL DT DD FIGURE FIGCAPTION MAIN DIV EM STRONG SMALL S CITE Q DFN ABBR RUBY RB RT RTC RP DATA TIME CODE VAR SAMP KBD SUB SUP I B U MARK BDI BDO SPAN BR WBR NOBR INS DEL PICTURE PARAM TRACK MAP TABLE CAPTION COLGROUP COL TBODY THEAD TFOOT TR TD TH SELECT DATALIST OPTGROUP OPTION OUTPUT PROGRESS METER FIELDSET LEGEND DETAILS SUMMARY MENU DIALOG SLOT CANVAS FONT CENTER ACRONYM BASEFONT BIG DIR HGROUP STRIKE TT".split(" "),
Ega=[["A",new Map([["href",{Pp:2}]])],["AREA",new Map([["href",{Pp:2}]])],["LINK",new Map([["href",{Pp:5,conditions:new Map([["rel",new Set("alternate author bookmark canonical cite help icon license next prefetch dns-prefetch prerender preconnect preload prev search subresource".split(" "))]])}]])],["SOURCE",new Map([["src",{Pp:5}],["srcset",{Pp:6}]])],["IMG",new Map([["src",{Pp:5}],["srcset",{Pp:6}]])],["VIDEO",new Map([["src",{Pp:5}]])],["AUDIO",new Map([["src",{Pp:5}]])]],Fga="title aria-atomic aria-autocomplete aria-busy aria-checked aria-current aria-disabled aria-dropeffect aria-expanded aria-haspopup aria-hidden aria-invalid aria-label aria-level aria-live aria-multiline aria-multiselectable aria-orientation aria-posinset aria-pressed aria-readonly aria-relevant aria-required aria-selected aria-setsize aria-sort aria-valuemax aria-valuemin aria-valuenow aria-valuetext alt align autocapitalize autocomplete autocorrect autofocus autoplay bgcolor border cellpadding cellspacing checked cite color cols colspan controls controlslist coords crossorigin datetime disabled download draggable enctype face formenctype frameborder height hreflang hidden inert ismap label lang loop max maxlength media minlength min multiple muted nonce open playsinline placeholder poster preload rel required reversed role rows rowspan selected shape size sizes slot span spellcheck start step summary translate type usemap valign value width wrap itemscope itemtype itemid itemprop itemref".split(" "),
Gga=[["dir",{Pp:3,conditions:new Map([["dir",new Set(["auto","ltr","rtl"])]])}],["async",{Pp:3,conditions:new Map([["async",new Set(["async"])]])}],["loading",{Pp:3,conditions:new Map([["loading",new Set(["eager","lazy"])]])}],["target",{Pp:3,conditions:new Map([["target",new Set(["_self","_blank"])]])}]],Hga=new _.lh(new Set(Dga),new Map(Ega),new Set(Fga),new Map(Gga)),Iga=new _.lh(new Set(Dga.concat(["BUTTON","INPUT"])),new Map(Ega),new Set(Fga.concat(["class","id","name"])),new Map(Gga.concat([["style",
{Pp:1}]]))),Jga=new _.lh(new Set(Dga.concat("STYLE TITLE INPUT TEXTAREA BUTTON LABEL".split(" "))),new Map(Ega),new Set(Fga.concat(["class","id","tabindex","contenteditable","name"])),new Map(Gga.concat([["style",{Pp:1}]])),new Set(["data-","aria-"]));var Kga,Mga;
_.Lga=function(a,b,c){b=Qda(b,c);b=document.createTreeWalker(b,5,g=>{if(g.nodeType===3)g=1;else if(Sda(g))if(g=Rda(g),g===null)g=2;else{var h=a.na;g=g!=="FORM"&&(h.ma.has(g)||h.ha.has(g))?1:2}else g=2;return g});var d=b.nextNode();const e=c.createDocumentFragment();let f=e;for(;d!==null;){let g;if(d.nodeType===3)a.ma&&f.nodeName==="STYLE"?(d=a.ma(d.data),g=a.createTextNode(d)):g=a.createTextNode(d.data);else if(Sda(d))g=Kga(a,d,c);else throw Error("ga");f.appendChild(g);if(d=b.firstChild())f=g;else for(;!(d=
b.nextSibling())&&(d=b.parentNode());)f=f.parentNode}return e};
Kga=function(a,b,c){const d=Rda(b);c=c.createElement(d);b=b.attributes;for(const {name:h,value:m}of b){var e=Cga(a.na,h,d),f;a:{if(f=e.conditions)for(const [u,x]of f){f=x;let A;var g=(A=b.getNamedItem(u))==null?void 0:A.value;if(g&&!f.has(g)){f=!1;break a}}f=!0}if(f)switch(e.Pp){case 1:te(c,h,m);break;case 2:e=_.qda(m);e=e!==void 0&&xga.indexOf(e.toLowerCase())!==-1?m:"about:invalid#zClosurez";te(c,h,e);break;case 3:te(c,h,m.toLowerCase());break;case 4:a.ka?(e=a.ka(m),te(c,h,e)):te(c,h,m);break;case 5:a.ha?
(e={type:2,attributeName:h,SOb:d},f=Pda(m),(e=a.ha(f,e))&&te(c,h,e.toString())):te(c,h,m);break;case 6:if(a.ha){e={type:2,attributeName:h,SOb:d};f=[];for(const u of m.split(",")){const [x,A]=u.trim().split(/\s+/,2);f.push({url:x,OGa:A})}g=f;f={ym:[]};for(const u of g)g=Pda(u.url),(g=a.ha(g,e))&&f.ym.push({url:g.toString(),OGa:u.OGa});te(c,h,Tda(f))}else te(c,h,m)}}return c};
Mga=class{constructor(a,b,c,d){this.na=a;this.ma=b;this.ka=c;this.ha=d;this.changes=[]}sanitize(a){const b=document.implementation.createHTMLDocument("");return _.Ida(_.Lga(this,a,b),b.body)}createTextNode(a){return document.createTextNode(a)}};_.ue=new Mga(Hga);_.Nga=new Mga(Iga);_.Oga=new Mga(Jga);var Pga;Pga=class{constructor(){this.ka=!1;this.ha=Hga}};_.oh=class extends Pga{build(){if(this.ka)throw Error("ka");this.ka=!0;return new Mga(this.ha,void 0,void 0,this.ma)}};var Qga,Sga,dha,eha,fha;_.ze=function(a){return a?new _.ph(_.qh(a)):Gea||(Gea=new _.ph)};_.rh=function(a,b){return typeof b==="string"?a.getElementById(b):b};_.sh=function(a,b){return(b||document).getElementsByTagName(String(a))};_.th=function(a,b){_.kd(b,function(c,d){d=="style"?a.style.cssText=c:d=="class"?a.className=c:d=="for"?a.htmlFor=c:Qga.hasOwnProperty(d)?a.setAttribute(Qga[d],c):_.ka(d,"aria-")||_.ka(d,"data-")?a.setAttribute(d,c):a[d]=c})};
Qga={cellpadding:"cellPadding",cellspacing:"cellSpacing",colspan:"colSpan",frameborder:"frameBorder",height:"height",maxlength:"maxLength",nonce:"nonce",role:"role",rowspan:"rowSpan",type:"type",usemap:"useMap",valign:"vAlign",width:"width"};_.vh=function(a){a=(a||window).document;a=a.compatMode=="CSS1Compat"?a.documentElement:a.body;return new _.gh(a.clientWidth,a.clientHeight)};
_.Tga=function(a,b){const c=b[1],d=_.Rga(a,String(b[0]));c&&(typeof c==="string"?d.className=c:Array.isArray(c)?d.className=c.join(" "):_.th(d,c));b.length>2&&Sga(a,d,b,2);return d};Sga=function(a,b,c,d){function e(f){f&&b.appendChild(typeof f==="string"?a.createTextNode(f):f)}for(;d<c.length;d++){const f=c[d];!_.Ka(f)||_.Pe(f)&&f.nodeType>0?e(f):_.Ve(f&&typeof f.length=="number"&&typeof f.item=="function"?_.Ja(f):f,e)}};_.wh=function(a){return _.Rga(document,a)};
_.Rga=function(a,b){b=String(b);a.contentType==="application/xhtml+xml"&&(b=b.toLowerCase());return a.createElement(b)};_.Uga=function(a,b){Sga(_.qh(a),a,arguments,1)};_.xh=function(a){let b;for(;b=a.firstChild;)a.removeChild(b)};_.Vga=function(a,b){b.parentNode&&b.parentNode.insertBefore(a,b)};_.Wga=function(a,b){b.parentNode&&b.parentNode.insertBefore(a,b.nextSibling)};_.Xga=function(a,b,c){a.insertBefore(b,a.childNodes[c]||null)};
_.yh=function(a){return a&&a.parentNode?a.parentNode.removeChild(a):null};_.Yga=function(a,b){const c=b.parentNode;c&&c.replaceChild(a,b)};_.Zga=function(a){return a.children!=void 0?a.children:Array.prototype.filter.call(a.childNodes,function(b){return b.nodeType==1})};_.aha=function(a){return a.firstElementChild!==void 0?a.firstElementChild:_.$ga(a.firstChild,!0)};_.bha=function(a){return a.nextElementSibling!==void 0?a.nextElementSibling:_.$ga(a.nextSibling,!0)};
_.cha=function(a){return a.previousElementSibling!==void 0?a.previousElementSibling:_.$ga(a.previousSibling,!1)};_.$ga=function(a,b){for(;a&&a.nodeType!=1;)a=b?a.nextSibling:a.previousSibling;return a};_.zh=function(a){return _.Pe(a)&&a.nodeType==1};_.Ah=function(a){return a.parentElement||null};
_.Bh=function(a,b){if(!a||!b)return!1;if(a.contains&&b.nodeType==1)return a==b||a.contains(b);if(typeof a.compareDocumentPosition!="undefined")return a==b||!!(a.compareDocumentPosition(b)&16);for(;b&&a!=b;)b=b.parentNode;return b==a};_.qh=function(a){return a.nodeType==9?a:a.ownerDocument||a.document};
_.Ch=function(a,b){if("textContent"in a)a.textContent=b;else if(a.nodeType==3)a.data=String(b);else if(a.firstChild&&a.firstChild.nodeType==3){for(;a.lastChild!=a.firstChild;)a.removeChild(a.lastChild);a.firstChild.data=String(b)}else _.xh(a),a.appendChild(_.qh(a).createTextNode(String(b)))};dha={SCRIPT:1,STYLE:1,HEAD:1,IFRAME:1,OBJECT:1};eha={IMG:" ",BR:"\n"};_.gha=function(a){return a.hasAttribute("tabindex")&&fha(a)};_.Dh=function(a,b){b?a.tabIndex=0:(a.tabIndex=-1,a.removeAttribute("tabIndex"))};
_.hha=function(a){return a.tagName=="A"&&a.hasAttribute("href")||a.tagName=="INPUT"||a.tagName=="TEXTAREA"||a.tagName=="SELECT"||a.tagName=="BUTTON"?!a.disabled&&(!a.hasAttribute("tabindex")||fha(a)):_.gha(a)};fha=function(a){a=a.tabIndex;return typeof a==="number"&&a>=0&&a<32768};_.Eh=function(a){const b=[];_.kha(a,b,!0);a=b.join("");a=a.replace(/ \xAD /g," ").replace(/\xAD/g,"");a=a.replace(/\u200B/g,"");a=a.replace(/ +/g," ");a!=" "&&(a=a.replace(/^\s*/,""));return a};
_.kha=function(a,b,c){if(!(a.nodeName in dha))if(a.nodeType==3)c?b.push(String(a.nodeValue).replace(/(\r\n|\r|\n)/g,"")):b.push(a.nodeValue);else if(a.nodeName in eha)b.push(eha[a.nodeName]);else for(a=a.firstChild;a;)_.kha(a,b,c),a=a.nextSibling};_.ph=function(a){this.ji=a||_.ia.document||document};_.k=_.ph.prototype;_.k.Za=_.ze;_.k.Qd=function(){return this.ji};_.k.Ma=function(a){return _.rh(this.ji,a)};_.k.Ebc=_.ph.prototype.Ma;_.k.getElementsByTagName=function(a,b){return(b||this.ji).getElementsByTagName(String(a))};
_.k.Tw=_.aa(2);_.k.Mb=_.aa(4);_.k.Vb=_.aa(6);_.k.setProperties=_.th;_.k.Tg=function(a){return _.vh(a||this.getWindow())};_.k.ub=function(a,b,c){return _.Tga(this.ji,arguments)};_.k.createElement=function(a){return _.Rga(this.ji,a)};_.k.createTextNode=function(a){return this.ji.createTextNode(String(a))};_.k.getWindow=function(){return this.ji.defaultView};_.k.uj=_.aa(7);_.k.appendChild=function(a,b){a.appendChild(b)};_.k.append=_.Uga;_.k.canHaveChildren=function(a){if(a.nodeType!=1)return!1;switch(a.tagName){case "APPLET":case "AREA":case "BASE":case "BR":case "COL":case "COMMAND":case "EMBED":case "FRAME":case "HR":case "IMG":case "INPUT":case "IFRAME":case "ISINDEX":case "KEYGEN":case "LINK":case "NOFRAMES":case "NOSCRIPT":case "META":case "OBJECT":case "PARAM":case "SCRIPT":case "SOURCE":case "STYLE":case "TRACK":case "WBR":return!1}return!0};
_.k.Ji=_.xh;_.k.pcb=_.Vga;_.k.mda=_.Wga;_.k.lda=_.Xga;_.k.removeNode=_.yh;_.k.DQa=_.Yga;_.k.getChildren=_.Zga;_.k.uca=_.aha;_.k.zJa=_.bha;_.k.hab=_.cha;_.k.isElement=_.zh;_.k.isWindow=function(a){return _.Pe(a)&&a.window==a};_.k.o3=_.Ah;_.k.contains=_.Bh;_.k.bqa=_.qh;_.k.wi=_.Ch;_.k.Vp=_.Dh;_.k.Jp=_.hha;_.k.pG=_.Eh;_.Fh=function(){this.VF=this.VF;this.FX=this.FX};_.Fh.prototype.VF=!1;_.Fh.prototype.isDisposed=function(){return this.VF};_.Fh.prototype.dispose=function(){this.VF||(this.VF=!0,this.Cb())};_.Fh.prototype[Symbol.dispose]=function(){this.dispose()};_.Fh.prototype.tb=function(a){this.addOnDisposeCallback(_.Se(_.Ae,a))};_.Fh.prototype.addOnDisposeCallback=function(a,b){this.VF?b!==void 0?a.call(b):a():(this.FX||(this.FX=[]),b&&(a=a.bind(b)),this.FX.push(a))};_.Fh.prototype.Cb=function(){if(this.FX)for(;this.FX.length;)this.FX.shift()()};Oe=Oe||{};var lha=function(){_.Fh.call(this)};_.Ue(lha,_.Fh);lha.prototype.initialize=function(){};_.mha=[];_.nha=[];_.oha=!1;_.pha=function(a){_.mha[_.mha.length]=a;if(_.oha)for(let b=0;b<_.nha.length;b++)a((0,_.Re)(_.nha[b].wrap,_.nha[b]))};_.Gh=function(a,b){this.ha=a;this.ka=b};_.Gh.prototype.execute=function(a){this.ha&&(this.ha.call(this.ka||null,a),this.ha=this.ka=null)};_.Gh.prototype.abort=function(){this.ka=this.ha=null};_.pha(function(a){_.Gh.prototype.execute=a(_.Gh.prototype.execute)});_.Hh=function(a,b){_.Fh.call(this);this.oa=a;this.xh=b;this.na=[];this.ma=[];this.ha=[]};_.Ue(_.Hh,_.Fh);_.Hh.prototype.qa=lha;_.Hh.prototype.ka=null;_.Hh.prototype.fK=function(){return this.oa};_.Hh.prototype.getId=function(){return this.xh};var qha=function(a,b,c){a.na.push(new _.Gh(b,c))},rha=function(a,b){a.ma.push(new _.Gh(b))};_.Hh.prototype.isLoaded=function(){return!!this.ka};
_.Hh.prototype.onLoad=function(a){var b=new this.qa;b.initialize(a());this.ka=b;b=(b=sha(this.ha,a()))||sha(this.na,a());b||(this.ma.length=0);return b};_.Hh.prototype.onError=function(a){(a=sha(this.ma,a))&&_.ja(Error("la`"+a));this.ha.length=0;this.na.length=0};var sha=function(a,b){const c=[];for(let d=0;d<a.length;d++)try{a[d].execute(b)}catch(e){_.ja(e),c.push(e)}a.length=0;return c.length?c:null};_.Hh.prototype.Cb=function(){_.Hh.Nb.Cb.call(this);_.Ae(this.ka)};_.tha=function(){this.qa=null};_.k=_.tha.prototype;_.k.Gjb=function(){};_.k.Uva=function(){};_.k.Hga=function(){};_.k.nma=function(){throw Error("ma");};_.k.fga=function(){throw Error("na");};_.k.bab=function(){return null};_.k.isActive=function(){return!1};_.k.heb=function(){return!1};_.k.kY=_.aa(11);var uha;uha=function(a){var b=document;const c=b.styleSheets.length,d=Zda(a,new _.ph(b));d.setAttribute("data-late-css","");b.styleSheets.length==c+1&&_.Ca(b.styleSheets,e=>(e.ownerNode||e.owningElement)==d)};_.vha=class{init(){_.Fea("_F_installCss",a=>{a&&uha(a)})}};var wha,xha,zha;wha=function(a){throw Error("oa`"+a.ka);};xha=function(a,b){return new TypeError("pa`"+b+"`"+a.ka+"`"+a.ha+"`"+typeof a.ha)};_.Jh=function(a){const b=_.Ih(a);b===null&&wha(a);return b};_.Lh=function(a,b){let c;return(c=_.Ih(a))!=null?c:b};_.Ih=function(a){const b=a.ha;if(b==null)return null;if(typeof b==="string")return b;throw xha(a,"string");};
_.yha=function(a){let b=a.ha;if(b==null)return null;if(typeof b==="boolean")return b;if(typeof b==="string"){b=b.toLowerCase();if(b==="true"||b==="1")return!0;if(b==="false"||b==="0")return!1}throw xha(a,"boolean");};_.Mh=function(a,b){let c;return(c=_.yha(a))!=null?c:b};_.Aha=function(a){const b=zha(a);b===null&&wha(a);return b};_.Nh=function(a,b){let c;return(c=zha(a))!=null?c:b};
zha=function(a){let b=a.ha;if(b==null)return null;if(typeof b==="number")return b;if(typeof b==="string"){const c=Number(b);if(!isNaN(c)&&(c!==0||b.trim().length!==0))return c}throw xha(a,"number");};_.Cha=function(a){var b=a.ha;if(b==null)return null;Array.isArray(b)||(typeof b!=="string"?b=[b]:(b=b.trim(),b=b==""?[]:b.split(",").map(c=>c.trim())));return _.Bha(a,b)};_.Bha=function(a,b){const c=a.ka+"[";return Array.from(b,(d,e)=>new _.Ce(c+e+"]",d))};
_.Ce=class{constructor(a,b){this.ka=a;this.ha=b}string(a){return arguments.length==0?_.Jh(this):_.Lh(this,a)}number(a){return arguments.length==0?_.Aha(this):_.Nh(this,a)}toString(){return _.Jh(this)}enum(a,b){let c=!0,d=void 0;for(const e in a){const f=a[e];c&&(c=!1,d=typeof f==="number"?_.Nh(this,b):_.Lh(this,b));if(f==d)return d}JSON.stringify(a);return d}array(a){if(arguments.length==0){var b=_.Cha(this);b===null&&wha(this);return b}b=_.Cha(this);return b==null?a:b}object(a){let b=this.ha;if(b==
null)return a===void 0&&wha(this),a;if(typeof b==="object"&&b.constructor===Object){a={};const c=this.ka+".";for(const d in b)a[d]=new _.Ce(c+d,b[d]);return a}throw xha(this,"object");}};_.Dha=function(a,b,c,d,e,f,g){let h="";a&&(h+=a+":");c&&(h+="//",b&&(h+=b+"@"),h+=c,d&&(h+=":"+d));e&&(h+=e);f&&(h+="?"+f);g&&(h+="#"+g);return h};_.Oh=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$");_.Ph=function(a){return a?decodeURI(a):a};_.Qh=function(a,b){return b.match(_.Oh)[a]||null};
_.Eha=function(a){a=_.Qh(1,a);!a&&_.ia.self&&_.ia.self.location&&(a=_.ia.self.location.protocol.slice(0,-1));return a?a.toLowerCase():""};_.Rh=function(a){a=a.match(_.Oh);return _.Dha(a[1],a[2],a[3],a[4])};_.Fha=function(a,b){if(a){a=a.split("&");for(let c=0;c<a.length;c++){const d=a[c].indexOf("=");let e,f=null;d>=0?(e=a[c].substring(0,d),f=a[c].substring(d+1)):e=a[c];b(e,f?_.Aga(f):"")}}};
_.Gha=function(a,b){if(!b)return a;var c=a.indexOf("#");c<0&&(c=a.length);let d=a.indexOf("?"),e;d<0||d>c?(d=c,e=""):e=a.substring(d+1,c);a=[a.slice(0,d),e,a.slice(c)];c=a[1];a[1]=b?c?c+"&"+b:b:c;return a[0]+(a[1]?"?"+a[1]:"")+a[2]};_.Hha=function(a,b,c){if(Array.isArray(b))for(let d=0;d<b.length;d++)_.Hha(a,String(b[d]),c);else b!=null&&c.push(a+(b===""?"":"="+_.jh(b)))};_.Iha=function(a){const b=[];for(const c in a)_.Hha(c,a[c],b);return b.join("&")};
_.Jha=function(a,b,c,d){const e=c.length;for(;(b=a.indexOf(c,b))>=0&&b<d;){var f=a.charCodeAt(b-1);if(f==38||f==63)if(f=a.charCodeAt(b+e),!f||f==61||f==38||f==35)return b;b+=e+1}return-1};_.Kha=/#|$/;var Lha,aea,Sh,Oha,Rha,Pha,Qha,Sha,Tha,Uha,Vha,eea,Wha,Mha,Nha,Xha;_.Ee=function(a,b=!0){const c=Lha(a),d=new Mha,e=c.match(_.Oh)[5];_.kd(Nha,function(g){const h=e.match("/"+g+"=([^/]+)");h&&Sh(d,g,h[1])});let f="";f=a.indexOf("_/ss/")!=-1?"_/ss/":"_/js/";Oha(d,a.substr(0,a.indexOf(f)+f.length));if(!b)return d;(a=_.Qh(6,c))&&_.Fha(a,(g,h)=>{d.ma[g]=h});return d};Lha=function(a){return a.startsWith("https://uberproxy-pen-redirect.corp.google.com/uberproxy/pen?url=")?a.substr(65):a};
aea=function(a){a=Lha(a);a=_.Ph(_.Qh(5,a));return a===null?!1:RegExp("(/_/js/)|(/_/ss/)","g").test(a)?/\/k=/.test(a):!1};Sh=function(a,b,c){c?a.ha[b]=c:delete a.ha[b]};Oha=function(a,b){a.ka=b};
Rha=function(a){const b=[],c=(0,_.Re)(function(d){this.ha[d]!==void 0&&b.push(d+"="+this.ha[d])},a);Pha(a)?(c("md"),c("k"),c("ck"),c("am"),c("rs"),c("gssmodulesetproto"),c("slk"),c("dti")):(c("sdch"),c("k"),c("ck"),c("am"),c("rt"),"d"in a.ha||Sh(a,"d","0"),c("d"),c("exm"),c("excm"),(a.ha.excm||a.ha.exm)&&b.push("ed=1"),c("im"),c("dg"),c("sm"),_.Th(a,"br")!="1"&&_.Th(a,"br")!="0"||c("br"),c("br-d"),_.Th(a,"rb")=="1"&&c("rb"),_.Th(a,"zs")!=="0"&&c("zs"),Qha(a)!==""&&c("wt"),c("gssmodulesetproto"),c("ujg"),
c("sp"),c("rs"),c("cb"),c("ee"),c("slk"),c("dti"),c("m"));return b.join("/")};_.Th=function(a,b){return a.ha[b]?a.ha[b]:null};Pha=function(a){a=_.Th(a,"md");return!!a&&a!=="0"};Qha=function(a){switch(_.Th(a,"wt")){case "0":return"0";case "1":return"1";case "2":return"2";default:return""}};Sha=function(a,b){b&&b.length>0?(b.sort(),Sh(a,"exm",b.join(","))):Sh(a,"exm",null)};Tha=function(a,b){b&&b.length>0?(b.sort(),Sh(a,"excm",b.join(","))):Sh(a,"excm",null)};
Uha=function(a){return(a=_.Th(a,"m"))?a.split(","):[]};Vha=function(a,b){const c=Object.keys(b).filter(d=>!!Object.keys(b[d]).length).map(d=>{const e=Object.keys(b[d]);e.length>1&&e.sort();return d+":"+e.join(",")});c.sort();Sh(a,"ee",c.join(";"))};eea=function(a){var b=_.Th(a,"ee");if(!b)return{};a={};b=b.split(";");for(const c of b){const [d,e]=c.split(":");a[d]={};for(const f of e.split(","))a[d][f]=!0}return a};Wha=function(a){delete a.ha.m;delete a.ha.exm;delete a.ha.ed};
Mha=class{constructor(){this.ha={};this.ka="";this.ma={}}toString(){var a=this.ka+Rha(this);const b=_.Iha(this.ma);let c="";b!=""&&(c="?"+b);return a+c}clone(){const a=new Mha;a.ha=Object.assign({},this.ha);a.ka=this.ka;a.ma=Object.assign({},this.ma);return a}};
Nha={Mhc:"k",Dcc:"ck",ygc:"m",mdc:"exm",kdc:"excm",Ibc:"am",ggc:"mm",Lhc:"rt",Xec:"d",ldc:"ed",Hic:"sv",Mcc:"deob",hcc:"cb",uic:"rs",Rhc:"sdch",dfc:"im",Ncc:"dg",ddc:"br",cdc:"br-d",edc:"rb",Xjc:"zs",Ujc:"wt",udc:"ee",Gic:"sm",METADATA:"md",Bec:"gssmodulesetproto",Bjc:"ujg",Ajc:"sp",tic:"slk",Vcc:"dti"};Xha=RegExp("^loaded_(g|h)?[_\\d]+$");var cea=!1,dea=!1;var Yha=a=>{a=a.clone();Wha(a);Sh(a,"dg",null);Sh(a,"d","0");Sha(a,null);Tha(a,null);return a},Zha=!0,$ha=(a,b,{cssRowKey:c,bU:d,QO:e,callback:f}={})=>{Sh(a,"m",b.join(","));e&&Vha(a,e);c&&(Sh(a,"ck",c),d?Sh(a,"rs",d):Zha&&(Zha=!1));if(f){if(f!=null&&!Xha.test(f))throw Error("qa`"+f);Sh(a,"cb",f)}a=a.toString();_.ka(a,"/")&&(a=_.Rh(document.location.href)+a);return _.ud(a)},aia=(a,b,{FQa:c=[],cssRowKey:d,bU:e,QO:f,callback:g}={})=>{a=Yha(a);Tha(a,c);return $ha(a,b,{cssRowKey:d,bU:e,QO:f,callback:g})},
bia=(a,b,{GQa:c=[],FQa:d=[],cssRowKey:e,bU:f,QO:g,callback:h}={})=>{a=Yha(a);Sh(a,"d","1");Sha(a,c);Tha(a,d);return $ha(a,b,{cssRowKey:e,bU:f,QO:g,callback:h})};_.cia=function(a){switch(a){case 200:case 201:case 202:case 204:case 206:case 304:case 1223:return!0;default:return!1}};_.eia=function(){};_.Ue(_.eia,_.gea);_.eia.prototype.IF=function(){return new XMLHttpRequest};_.dia=new _.eia;_.Uh=typeof AsyncContext!=="undefined"&&typeof AsyncContext.Snapshot==="function"?a=>a&&AsyncContext.Snapshot.wrap(a):a=>a;var iea=function(a,b){a.na(b);a.ka<100&&(a.ka++,b.next=a.ha,a.ha=b)},fia=class{constructor(a,b){this.ma=a;this.na=b;this.ka=0;this.ha=null}get(){let a;this.ka>0?(this.ka--,a=this.ha,this.ha=a.next,a.next=null):a=this.ma();return a}};_.Vh=function(a,b){let c=a;b&&(c=(0,_.Re)(a,b));c=_.Vh.gqb(c);_.Vh.KFb?setTimeout(c,0):(c=_.Vh.J2b(c),_.Vh.Yfb||(_.Vh.Yfb=_.Vh.pSb()),_.Vh.Yfb(c))};_.Vh.J2b=_.Uh;_.Vh.KFb=!1;_.Vh.pSb=function(){if(typeof MessageChannel!=="undefined"){const a=new MessageChannel;let b={},c=b;a.port1.onmessage=function(){if(b.next!==void 0){b=b.next;const d=b.cb;b.cb=null;d()}};return function(d){c.next={cb:d};c=c.next;a.port2.postMessage(0)}}return function(a){_.ia.setTimeout(a,0)}};_.Vh.gqb=a=>a;
_.pha(function(a){_.Vh.gqb=a});var gia=class{constructor(){this.ka=this.ha=null}add(a,b){const c=jea.get();c.set(a,b);this.ka?this.ka.next=c:this.ha=c;this.ka=c}remove(){let a=null;this.ha&&(a=this.ha,this.ha=this.ha.next,this.ha||(this.ka=null),a.next=null);return a}},jea=new fia(()=>new hia,a=>a.reset()),hia=class{constructor(){this.next=this.scope=this.fn=null}set(a,b){this.fn=a;this.scope=b;this.next=null}reset(){this.next=this.scope=this.fn=null}};var iia,kea,hea,jia;kea=!1;hea=new gia;_.Wh=(a,b)=>{iia||jia();kea||(iia(),kea=!0);hea.add(a,b)};jia=()=>{const a=Promise.resolve(void 0);iia=()=>{a.then(lea)}};_.Xh=function(){};var kia=function(a){if(!a)return!1;try{return!!a.$goog_Thenable}catch(b){return!1}};var lia,mia,via,sia,xia,Bia,zia,Cia;_.Zh=function(a){this.ka=0;this.va=void 0;this.na=this.ma=this.Og=null;this.oa=this.qa=!1;if(a!=_.Xh)try{const b=this;a.call(void 0,function(c){Yh(b,2,c)},function(c){Yh(b,3,c)})}catch(b){Yh(this,3,b)}};lia=function(){this.next=this.context=this.ha=this.ma=this.child=null;this.ka=!1};lia.prototype.reset=function(){this.context=this.ha=this.ma=this.child=null;this.ka=!1};mia=new fia(function(){return new lia},function(a){a.reset()});
_.nia=function(a,b,c){const d=mia.get();d.ma=a;d.ha=b;d.context=c;return d};_.$h=function(a){if(a instanceof _.Zh)return a;const b=new _.Zh(_.Xh);Yh(b,2,a);return b};_.ai=function(a){return new _.Zh(function(b,c){c(a)})};_.pia=function(a,b,c){oia(a,b,c,null)||_.Wh(_.Se(b,a))};_.qia=function(a){return new _.Zh(function(b,c){let d=a.length;const e=[];if(d){var f=function(m,u){d--;e[m]=u;d==0&&b(e)},g=function(m){c(m)};for(let m=0;m<a.length;m++){var h=a[m];_.pia(h,_.Se(f,m),g)}}else b(e)})};
_.bi=function(){let a,b;const c=new _.Zh(function(d,e){a=d;b=e});return new ria(c,a,b)};_.Zh.prototype.then=function(a,b,c){return sia(this,(0,_.Uh)(typeof a==="function"?a:null),(0,_.Uh)(typeof b==="function"?b:null),c)};_.Zh.prototype.$goog_Thenable=!0;var uia=function(a,b,c,d){_.tia(a,_.nia(b||_.Xh,c||null,d))};_.Zh.prototype.finally=function(a){a=(0,_.Uh)(a);return new Promise((b,c)=>{uia(this,d=>{a();b(d)},d=>{a();c(d)})})};_.Zh.prototype.ha=function(a,b){return sia(this,null,(0,_.Uh)(a),b)};
_.Zh.prototype.catch=_.Zh.prototype.ha;_.Zh.prototype.cancel=function(a){if(this.ka==0){const b=new _.ci(a);_.Wh(function(){via(this,b)},this)}};via=function(a,b){if(a.ka==0)if(a.Og){var c=a.Og;if(c.ma){var d=0,e=null,f=null;for(let g=c.ma;g&&(g.ka||(d++,g.child==a&&(e=g),!(e&&d>1)));g=g.next)e||(f=g);e&&(c.ka==0&&d==1?via(c,b):(f?(d=f,d.next==c.na&&(c.na=d),d.next=d.next.next):wia(c),xia(c,e,3,b)))}a.Og=null}else Yh(a,3,b)};
_.tia=function(a,b){a.ma||a.ka!=2&&a.ka!=3||yia(a);a.na?a.na.next=b:a.ma=b;a.na=b};sia=function(a,b,c,d){const e=_.nia(null,null,null);e.child=new _.Zh(function(f,g){e.ma=b?function(h){try{const m=b.call(d,h);f(m)}catch(m){g(m)}}:f;e.ha=c?function(h){try{const m=c.call(d,h);m===void 0&&h instanceof _.ci?g(h):f(m)}catch(m){g(m)}}:g});e.child.Og=a;_.tia(a,e);return e.child};_.Zh.prototype.Ca=function(a){this.ka=0;Yh(this,2,a)};_.Zh.prototype.Da=function(a){this.ka=0;Yh(this,3,a)};
var Yh=function(a,b,c){a.ka==0&&(a===c&&(b=3,c=new TypeError("ua")),a.ka=1,oia(c,a.Ca,a.Da,a)||(a.va=c,a.ka=b,a.Og=null,yia(a),b!=3||c instanceof _.ci||zia(a,c)))},oia=function(a,b,c,d){if(a instanceof _.Zh)return uia(a,b,c,d),!0;if(kia(a))return a.then(b,c,d),!0;if(_.Pe(a))try{const e=a.then;if(typeof e==="function")return Aia(a,e,b,c,d),!0}catch(e){return c.call(d,e),!0}return!1},Aia=function(a,b,c,d,e){let f=!1;const g=function(m){f||(f=!0,c.call(e,m))},h=function(m){f||(f=!0,d.call(e,m))};try{b.call(a,
g,h)}catch(m){h(m)}},yia=function(a){a.qa||(a.qa=!0,_.Wh(a.Aa,a))},wia=function(a){let b=null;a.ma&&(b=a.ma,a.ma=b.next,b.next=null);a.ma||(a.na=null);return b};_.Zh.prototype.Aa=function(){let a;for(;a=wia(this);)xia(this,a,this.ka,this.va);this.qa=!1};xia=function(a,b,c,d){if(c==3&&b.ha&&!b.ka)for(;a&&a.oa;a=a.Og)a.oa=!1;if(b.child)b.child.Og=null,Bia(b,c,d);else try{b.ka?b.ma.call(b.context):Bia(b,c,d)}catch(e){Cia.call(null,e)}iea(mia,b)};
Bia=function(a,b,c){b==2?a.ma.call(a.context,c):a.ha&&a.ha.call(a.context,c)};zia=function(a,b){a.oa=!0;_.Wh(function(){a.oa&&Cia.call(null,b)})};Cia=_.ja;_.ci=function(a){_.ca.call(this,a);this.ha=!1};_.Ue(_.ci,_.ca);_.ci.prototype.name="cancel";var ria=function(a,b,c){this.promise=a;this.resolve=b;this.reject=c};var Qia=function(a){return Dia(a).then(function(b){return JSON.parse(b.responseText)})},Dia=function(a){const b={},c=b.gS?b.gS.IF():_.dia.IF();return(new _.Zh(function(d,e){let f;try{c.open("GET",a,!0)}catch(g){e(new di("Error opening XHR: "+g.message,a,c))}c.onreadystatechange=function(){if(c.readyState==4){_.ia.clearTimeout(f);var g;!(g=_.cia(c.status))&&(g=c.status===0)&&(g=_.Eha(a),g=!(g=="http"||g=="https"||g==""));g?d(c):e(new Ria(c.status,a,c))}};c.onerror=function(){e(new di("Network error",
a,c))};if(b.headers)for(let g in b.headers){const h=b.headers[g];h!=null&&c.setRequestHeader(g,h)}b.withCredentials&&(c.withCredentials=b.withCredentials);b.responseType&&(c.responseType=b.responseType);b.mimeType&&c.overrideMimeType(b.mimeType);b.Yp>0&&(f=_.ia.setTimeout(function(){c.onreadystatechange=()=>{};c.abort();e(new Sia(a,c))},b.Yp));try{c.send(null)}catch(g){c.onreadystatechange=()=>{},_.ia.clearTimeout(f),e(new di("Error sending XHR: "+g.message,a,c))}})).ha(function(d){d instanceof _.ci&&
c.abort();throw d;})},di=function(a,b,c){_.ca.call(this,a+", url="+b);this.url=b;this.yr=c};_.Ue(di,_.ca);di.prototype.name="XhrError";var Ria=function(a,b,c){di.call(this,"Request Failed, status="+a,b,c);this.status=a};_.Ue(Ria,di);Ria.prototype.name="XhrHttpError";var Sia=function(a,b){di.call(this,"Request timed out",a,b)};_.Ue(Sia,di);Sia.prototype.name="XhrTimeoutError";var Via,Xia,Yia,Wia,cja,dja,$ia,Zia,aja,bja;_.Tia=function(a,b,c,d,e=b){let f=b.length,g=()=>{f=0;a.onload=null;a.onerror=null;h=()=>{}},h=()=>{g();const u=e.filter(x=>!_.hd().eo(x).isLoaded());u.length!==0?d(u,`Response was successful but was missing module(s) ${u}.`):c()},m=()=>{f--;f==0&&h()};b.forEach(u=>{u=_.hd().eo(u);u.isLoaded()?m():(u.ha.push(new _.Gh(m)),rha(u,m))});a.onload=()=>h();a.onerror=()=>{g();d(b)}};
_.Uia=function(a,b){let c=!1,d=[];for(let e=0;e<b.length;++e){const f=b[e];a.ma[f]||(a.ma[f]=!0,a.Aa.push(f),c=!0,d.push(f))}c&&(a.La=!1)};Via=function(a,b){if(b.href||b.getAttribute("data-href"))if(b=b.href||b.getAttribute("data-href"),aea(b)&&!_.Ee(b).ka.endsWith("_/js/")){b=Uha(_.Ee(b));for(const c of b)a.Ea.includes(c)||a.Ea.push(c)}};Xia=function(a,b,c,d=()=>{},e=()=>{},f=!1){Wia(a,b,(g,h,m=h)=>{a.Ya&&f?a.lb(g,h,d,e,m):a.load(g,h,d,e,m,c)},c)||d(-1)};Yia=function(a,b){return b.filter(c=>!a.ma[c])};
Wia=function(a,b,c,d){if(a.ka)return a.ka.then(()=>{Wia(a,b,c,d)}),!0;if(!a.va){const f=[];var e=Object.assign({},a.ma);Zia(a,b,g=>{f.push(g.getId())},d,g=>!g.isLoaded(),e);b=f}for(e=0;e<b.length;){let f=b.length-e,g=e==0?b:b.slice(e,b.length),h=$ia(a,g,d),m=_.vd(h).toString();for(;m.length>a.Iia;)if(f>1)f-=Math.ceil((m.length-a.Iia)/6),f=Math.max(f,1),g=b.slice(e,e+f),h=$ia(a,g,d),m=_.vd(h).toString();else return a.va?(a.va=!1,a.ka=aja(a).then(u=>{bja(a,u,d)}),Wia(a,b.slice(e),c,d)):!1;e+=f;a.va?
c(h,g):c(h,g,e===b.length?b:[])}return!0};cja=function(a){a.La||(a.La=!0,a.Aa.sort());return a.Aa};dja=function(a){a=a.Ea;a.sort();return a};$ia=function(a,b,c){return a.va?bia(a.na,b,{cssRowKey:a.Ta,bU:a.Pa,QO:c,GQa:cja(a),FQa:dja(a)}):aia(a.na,b,{cssRowKey:a.Ta,bU:a.Pa,GQa:cja(a),FQa:dja(a)})};_.ei=function(a,b){let c=[];for(let d=0;d<b.length;++d){const e=b[d];a.ma[e]&&(delete a.ma[e],_.Ia(a.Aa,e),c.push(e))}};
_.eja=function(a,b,c,d,e,f,g=d){a.qa=c;a.Da.insertBefore(c,a.Da.firstChild);_.Tia(c,d,()=>{c.parentElement.removeChild(c);a.qa==c&&(a.qa=null);const h=new Set;d.map(m=>h.add(m));for(const m in a.Ca)a.Ca[m].isLoaded()&&h.add(m);Array.from(h);f()},h=>{c.parentElement.removeChild(c);a.qa==c&&(a.qa=null);_.ei(a,h);a.ka?a.ka.then(()=>{e(-1,b)}):e(-1,b)},g)};
Zia=function(a,b,c,d,e,f={}){const g=_.hd();for(let h of b){b=g.eo(h);if(f[h]||e&&!e(b))continue;f[h]=!0;let m=b.fK()||[];if(d){let u=[];d[h]&&(u=Object.keys(d[h]));m=m.concat(u)}Zia(a,m,c,d,e,f);c(b)}};aja=function(a){a=a.na.clone();Wha(a);Sh(a,"dg",null);Sh(a,"md","1");return Qia(a.toString())};bja=function(a,b,c){_.hd().Hga((b||{}).moduleGraph);Zia(a,cja(a),d=>{_.Uia(a,[d.getId()])},c);a.ka=null};
_.Ge=class{constructor(a,b,c,d=!1,e=!1){this.qb=a;this.na=_.Ee(_.vd(a).toString(),!0);this.Ta=b;this.Pa=c;this.va=d;this.ma={};this.Ca={};this.Aa=[];this.La=!0;this.Ea=(a=_.Th(this.na,"excm"))?a.split(","):[];this.rb=e;this.Lia=!1;this.Haa="anonymous";this.Iia=4043;this.Da=document.head||document.documentElement;this.ka=this.qa=null;this.nb=!0;_.fea();this.logger=null;_.Uia(this,Uha(this.na));this.fetchPriority=void 0;this.Ya=!1;this.Ha()}hb(a,b,{QO:c,onError:d,XOa:e,FRb:f}={}){this.Ca=b;if(!a)throw Error("va");
if(this.rb){for(const g of document.getElementsByTagName("style"))Via(this,g);for(const g of document.getElementsByTagName("link"))Via(this,g)}Xia(this,Yia(this,a),c,d,e,f)}lb(){_.Je(function*(){throw Error("wa");})}Ha(){}load(a,b,c,d,e=b){_.vd(a);var f=this.Lia,g=this.Haa,h=this.fetchPriority;const m=_.wh("SCRIPT");_.de(m,a);f&&(m.crossOrigin=g);m.async=!1;h&&m.setAttribute("fetchpriority",h);_.Uia(this,b);_.eja(this,a,m,b,c,d,e)}};var mea=new Uint8Array(123);var fja=[];/*

 Copyright 2005, 2007 Bob Ippolito. All Rights Reserved.
 Copyright The Closure Library Authors.
 SPDX-License-Identifier: MIT
*/
_.fi=function(a,b){this.va=[];this.lb=a;this.Pa=b||null;this.oa=this.ka=!1;this.ma=void 0;this.Ha=this.nb=this.Da=!1;this.Aa=0;this.Og=null;this.qa=0};_.fi.prototype.cancel=function(a){if(this.ka)this.ma instanceof _.fi&&this.ma.cancel();else{if(this.Og){const b=this.Og;delete this.Og;a?b.cancel(a):(b.qa--,b.qa<=0&&b.cancel())}this.lb?this.lb.call(this.Pa,this):this.Ha=!0;this.ka||this.ha(new _.hi(this))}};_.fi.prototype.La=function(a,b){this.Da=!1;gja(this,a,b)};
var gja=function(a,b,c){a.ka=!0;a.ma=c;a.oa=!b;hja(a)},jja=function(a){if(a.ka){if(!a.Ha)throw new ija(a);a.Ha=!1}};_.fi.prototype.callback=function(a){jja(this);gja(this,!0,a)};_.fi.prototype.ha=function(a){jja(this);gja(this,!1,a)};_.ji=function(a,b,c){return _.ii(a,b,null,c)};_.fi.prototype.finally=function(a){return new Promise((b,c)=>{_.ii(this,d=>{a();b(d)},d=>{a();c(d)})})};
_.ii=function(a,b,c,d){const e=a.ka;e||(b===c?b=c=(0,_.Uh)(b):(b=(0,_.Uh)(b),c=(0,_.Uh)(c)));a.va.push([b,c,d]);e&&hja(a);return a};_.fi.prototype.then=function(a,b,c){let d,e;const f=new _.Zh(function(g,h){e=g;d=h});_.ii(this,e,function(g){g instanceof _.hi?f.cancel():d(g);return kja},this);return f.then(a,b,c)};_.fi.prototype.$goog_Thenable=!0;_.fi.prototype.na=_.aa(12);_.fi.prototype.isError=function(a){return a instanceof Error};
var lja=function(a){return _.Ze(a.va,function(b){return typeof b[1]==="function"})},kja={},hja=function(a){if(a.Aa&&a.ka&&lja(a)){var b=a.Aa,c=mja[b];c&&(_.ia.clearTimeout(c.xh),delete mja[b]);a.Aa=0}a.Og&&(a.Og.qa--,delete a.Og);b=a.ma;for(var d=c=!1;a.va.length&&!a.Da;){var e=a.va.shift(),f=e[0];const h=e[1];e=e[2];if(f=a.oa?h:f)try{var g=f.call(e||a.Pa,b);g===kja&&(g=void 0);g!==void 0&&(a.oa=a.oa&&(g==b||a.isError(g)),a.ma=b=g);if(kia(b)||typeof _.ia.Promise==="function"&&b instanceof _.ia.Promise)d=
!0,a.Da=!0}catch(m){b=m,a.oa=!0,lja(a)||(c=!0)}}a.ma=b;d&&(g=(0,_.Re)(a.La,a,!0),d=(0,_.Re)(a.La,a,!1),b instanceof _.fi?(_.ii(b,g,d),b.nb=!0):b.then(g,d));c&&(b=new nja(b),mja[b.xh]=b,a.Aa=b.xh)},ija=function(a){_.ca.call(this);this.Gw=a};_.Ue(ija,_.ca);ija.prototype.message="Deferred has already fired";ija.prototype.name="AlreadyCalledError";_.hi=function(a){_.ca.call(this);this.Gw=a};_.Ue(_.hi,_.ca);_.hi.prototype.message="Deferred was canceled";_.hi.prototype.name="CanceledError";
var nja=function(a){this.xh=_.ia.setTimeout((0,_.Re)(this.throwError,this),0);this.Po=a};nja.prototype.throwError=function(){delete mja[this.xh];throw this.Po;};var mja={};var oja=function(a){switch(a.type){case ki.Type.J0a:return"Unauthorized";case ki.Type.lza:return"Consecutive load failures";case ki.Type.TIMEOUT:return"Timed out";case ki.Type.B_a:return"Out of date module id";case ki.Type.Nza:return"Init error";default:return`Unknown failure type ${a.type}`}},ki=class extends Error{constructor(a,b,c,d,e){super();this.name="ModuleLoadFailure";this.type=a;this.status=b;this.GQa=c;this.url=d;this.cause=e;this.message=this.toString()}toString(){return`${oja(this)} (${this.status!=
void 0?this.status:"?"})`}};Oe.Ws=ki;Oe.Ws.Type={J0a:0,lza:1,TIMEOUT:2,B_a:3,Nza:4};_.li=function(){this.Ya=this.qa=null;this.ha={};this.oa=[];this.va=[];this.hb=[];this.ka=[];this.Ca=[];this.na={};this.lb={};this.ma=this.La=new _.Hh([],"");this.rb=null;this.Ea=new _.fi;this.qb=this.nb=!1;this.Pa=0;this.Db=this.Kb=this.Jb=!1};_.Ue(_.li,_.tha);var pja=function(a,b){_.ca.call(this,`Error loading ${a}: ${b}`)};_.Ue(pja,_.ca);_.li.prototype.Gjb=function(a){this.nb=a};_.li.prototype.Uva=function(a){this.qb=a};
_.li.prototype.Hga=function(a,b){if(!(this instanceof _.li))this.Hga(a,b);else if(typeof a==="string"){if(a.startsWith("d$")){a=a.substring(2);for(var c=[],d=0,e=a.indexOf("/"),f=0,g=!1,h=0;;){var m=g?a.substring(f):a.substring(f,e);if(m.length===0)d++,f="sy"+d.toString(36),m=[];else{var u=m.indexOf(":");if(u<0)f=m,m=[];else if(u===m.length-1)f=m.substring(0,u),m=Array(c[h-1]);else{f=m.substring(0,u);m=m.substring(u+1).split(",");u=h;for(let x=0;x<m.length;x++)u-=m[x].length===0?1:Number(m[x]),m[x]=
c[u]}u=0;if(f.length===0)u=1;else if(f.charAt(0)==="+"||f.charAt(0)==="-")u=Number(f);u!==0&&(d+=u,f="sy"+d.toString(36))}c.push(f);qja(this,f,m);if(g)break;f=e+1;e=a.indexOf("/",f);e===-1&&(g=!0);h++}this.Ya=c}else if(a.startsWith("p$"))rja(this,a);else{a=a.split("/");c=[];for(d=0;d<a.length;d++){h=a[d].split(":");e=h[0];g=[];if(h[1])for(g=h[1].split(","),h=0;h<g.length;h++)g[h]=c[parseInt(g[h],36)];c.push(e);qja(this,e,g)}this.Ya=c}b&&b.length?(_.La(this.oa,b),this.rb=_.taa(b)):this.Ea.ka||this.Ea.callback();
Object.freeze(this.Ya);this.ma==this.La&&(this.ma=null,(b=this.La.onLoad((0,_.Re)(this.bab,this)))&&b.length&&sja(this,new Oe.Ws(Oe.Ws.Type.Nza,void 0,void 0,void 0,b[0])),mi(this))}};
var rja=function(a,b){var c=b.substring(2);for(b=0;b<64;b++)mea["ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".charCodeAt(b)]=b;b={buf:c,ob:0};Fe(b);const d=Fe(b);var e=Fe(b)+1;const f=Array(d),g=Array(d),h=Array(d);e=Array(e);var m=0,u=0,x=b.ob,A=b.buf.indexOf("|",b.ob);b.ob=A+1;for(A=0;A<d;A++){var E=Fe(b),K=E&2,N=E&1;E>>>=2;N?(m+=E>>>1^-(E&1),E="sy"+m.toString(36)):(N=x,x+=E,E=c.substring(N,x));f[A]=E;K&&(e[u++]=E)}e[u]="";b.ob++;u=d&-2;c=d&1;for(m=0;m<u;m+=2)x=nea(b),h[m]=
x&7,h[m+1]=x>>>3&7;c&&(c=nea(b),h[u]=c&7);b.ob++;for(u=0;u<d;u++)h[u]===7&&(h[u]=Fe(b));b.ob++;u=0;for(c=0;c<d;c++){m=h[c];x=m===0?fja:Array(m);g[c]=x;A=u;for(K=0;K<m;K++)A-=Fe(b),x[K]=e[A];e[u]===f[c]&&u++}const {ids:X,dependencies:fa}={ids:f,dependencies:g};for(b=0;b<X.length;b++)qja(a,X[b],fa[b]);a.Ya=X};_.k=_.li.prototype;_.k.eo=function(a){return this.ha[a]};_.k.nma=function(a,b){const c=this.eo(a);c&&c.isLoaded()?this.load(b):(this.na[a]||(this.na[a]={}),this.na[a][b]=!0)};
_.k.fga=function(a,b){if(this.na[a]){delete this.na[a][b];for(const c in this.na[a])return;delete this.na[a]}};_.k.isActive=function(){return this.oa.length>0};_.k.heb=function(){return this.Ca.length>0};
var mi=function(a){var b=a.Jb;const c=a.isActive();c!=b&&(tja(a,c?"active":"idle"),a.Jb=c);b=a.heb();b!=a.Kb&&(tja(a,b?"userActive":"userIdle"),a.Kb=b)},qja=function(a,b,c){a.ha[b]?(a=a.ha[b].fK(),a!=c&&a.splice(0,a.length,...c)):a.ha[b]=new _.Hh(c,b)},vja=function(a,b,c){const d=[];_.Na(b,d);b=[];const e={};for(let f=0;f<d.length;f++){const g=d[f],h=a.eo(g);if(!h)throw Error("xa`"+g);const m=new _.fi;e[g]=m;h.isLoaded()?m.callback(null):(uja(a,g,h,!!c,m),a.Ha(g)||b.push(g))}b.length>0&&(a.qb?_.ji(a.Ea,
(0,_.Re)(a.Ta,a,b)):a.oa.length===0?a.Ta(b):(a.ka.push(b),mi(a)));return e},uja=function(a,b,c,d,e){qha(c,e.callback,e);rha(c,function(f){e.ha(new pja(b,f))});a.Ha(b)?d&&(wja(a,b),mi(a)):d&&wja(a,b)};
_.li.prototype.Ta=function(a,b,c){b||(this.Pa=0);const d=xja(this,a);this.qb?_.La(this.oa,d):this.oa=d;this.va=this.nb?a:_.Ja(d);mi(this);if(d.length!==0){this.hb.push.apply(this.hb,d);if(Object.keys(this.na).length>0&&!this.qa.nb)throw Error("ya");a=(0,_.Re)(this.qa.hb,this.qa,_.Ja(d),this.ha,{QO:this.na,FRb:!!c,onError:(e,f)=>{var g=this.va;e=e!=null?e:void 0;this.Pa++;const h=_.Ja(d);this.va=g;d.forEach(_.Se(_.Ia,this.hb),this);e==401?(sja(this,new Oe.Ws(Oe.Ws.Type.J0a,e)),this.ka.length=0):e==
410?(yja(this,new Oe.Ws(Oe.Ws.Type.B_a,e)),zja(this)):this.Pa>=3?(yja(this,new Oe.Ws(Oe.Ws.Type.lza,e,h,f)),zja(this)):this.Ta(this.va,!0,e==8001||!1)},YOa:(0,_.Re)(this.Lb,this)});(b=Math.pow(this.Pa,2)*5E3)?_.ia.setTimeout(a,b):a()}};
var xja=function(a,b){b=b.filter(d=>a.ha[d].isLoaded()?(_.ia.setTimeout(()=>Error("za`"+d),0),!1):!0);let c=[];for(let d=0;d<b.length;d++)c=c.concat(Aja(a,b[d]));_.Na(c);return!a.nb&&c.length>1?(b=c.shift(),a.ka=c.map(function(d){return[d]}).concat(a.ka),[b]):c},Aja=function(a,b){const c=_.rd(a.hb),d=[];c[b]||d.push(b);b=[b];for(let e=0;e<b.length;e++){const f=a.eo(b[e]).fK();for(let g=f.length-1;g>=0;g--){const h=f[g];a.eo(h).isLoaded()||c[h]||(d.push(h),b.push(h))}}d.reverse();_.Na(d);return d};
_.li.prototype.Da=function(){if(this.ma){var a=this.ma.getId(),b=[];if(this.na[a]){for(const c of Object.keys(this.na[a])){const d=this.eo(c);d&&!d.isLoaded()&&(this.fga(a,c),b.push(c))}this.Aa(b)}this.isDisposed()||((b=this.ha[a].onLoad((0,_.Re)(this.bab,this)))&&b.length&&sja(this,new Oe.Ws(Oe.Ws.Type.Nza,void 0,void 0,void 0,b[0])),_.Ia(this.Ca,a),_.Ia(this.oa,a),this.oa.length===0&&zja(this),this.rb&&a==this.rb&&(this.Ea.ka||this.Ea.callback()),mi(this),this.ma=null)}};
_.li.prototype.Ha=function(a){if(_.Fa(this.oa,a))return!0;for(let b=0;b<this.ka.length;b++)if(_.Fa(this.ka[b],a))return!0;return!1};_.li.prototype.load=function(a,b){return vja(this,[a],b)[a]};_.li.prototype.Aa=function(a){return vja(this,a)};var wja=function(a,b){_.Fa(a.Ca,b)||a.Ca.push(b)};
_.li.prototype.wb=function(a){this.ma&&this.ma.getId()==="synthetic_module_overhead"&&(this.Da(),delete this.ha.synthetic_module_overhead);this.ha[a]&&Bja(this,this.ha[a].fK()||[],b=>{b.ka=new lha;_.Ia(this.oa,b.getId())},b=>!b.isLoaded());this.ma=this.eo(a)};_.li.prototype.kY=_.aa(10);_.li.prototype.Lb=function(){yja(this,new Oe.Ws(Oe.Ws.Type.TIMEOUT));zja(this)};
var yja=function(a,b){a.va.length>1?a.ka=a.va.map(function(c){return[c]}).concat(a.ka):sja(a,b)},sja=function(a,b){const c=a.va;a.oa.length=0;var d=[];for(var e=0;e<a.ka.length;e++){var f=a.ka[e].filter(function(g){const h=Aja(this,g);return _.Ze(c,function(m){return _.Fa(h,m)})},a);_.La(d,f)}for(e=0;e<c.length;e++)_.Ga(d,c[e]);for(e=0;e<d.length;e++){for(f=0;f<a.ka.length;f++)_.Ia(a.ka[f],d[e]);_.Ia(a.Ca,d[e])}if(e=a.lb.error)for(f=0;f<e.length;f++){const g=e[f];for(let h=0;h<d.length;h++)g("error",
d[h],b)}for(d=0;d<c.length;d++)if(a.ha[c[d]])a.ha[c[d]].onError(b);a.va.length=0;mi(a)},zja=function(a){for(;a.ka.length;){const b=a.ka.shift().filter(function(c){return!this.eo(c).isLoaded()},a);if(b.length>0){a.Ta(b);return}}mi(a)},tja=function(a,b){a=a.lb[b];for(let c=0;a&&c<a.length;c++)a[c](b)},Bja=function(a,b,c,d=()=>!0,e={}){for(const f of b)b=a.eo(f),!e[f]&&d(b)&&(e[f]=!0,Bja(a,b.fK()||[],c,d,e),c(b))};
_.li.prototype.dispose=function(){_.Be(_.md(this.ha),this.La);this.ha={};this.oa=[];this.va=[];this.Ca=[];this.ka=[];this.lb={};this.Db=!0};_.li.prototype.isDisposed=function(){return this.Db};_.gd=function(){return new _.li};var Cja,Fja,Gja,Hja,Ija,Dja,Eja;Cja=[5E3,2E4];Fja=function(a,b){b=b.filter(c=>!a.Ha(c)&&!a.eo(c).isLoaded());b.length>0&&(Dja(a,...b),a.na.push(b),Eja(a))};Gja=function(a,b){return new _.Zh((c,d)=>{const e=a.eo(b);e.isLoaded()?c(null):(qha(e,()=>{c(null)}),rha(e,f=>{let g=`Error loading ${b}: ${f}`;f instanceof Oe.Ws&&f.url&&(g=`${g}, requested url: ${f.url.toString()}`);d(Error(g))}))})};Hja=function(a,b,c,d=()=>!0,e={}){for(const f of b)b=a.eo(f),!e[f]&&d(b)&&(e[f]=!0,Hja(a,b.fK()||[],c,d,e),c(b))};
Ija=function(a,...b){b.forEach(c=>{delete a.va[c]})};Dja=function(a,...b){b.forEach(c=>{a.va[c]=!0})};
Eja=function(a){for(;a.oa<a.Ca&&a.na.length>0;){const b=a.na.shift().filter(c=>!a.eo(c).isLoaded());if(b.length>0){a.oa++;let c=()=>{a.oa--;Eja(a);c=()=>{}};_.qia(b.map(f=>Gja(a,f))).then(()=>{c()});let d=0;const e=()=>{if(Object.keys(a.ka).length>0&&!a.qa.nb)throw Error("ya");a.qa.hb(b,a.ma,{QO:a.ka,onError:(f,g)=>{const h=Cja[d++];h!==void 0?setTimeout(()=>{e()},h):(Ija(a,...b),c(),b.forEach(m=>{m=a.eo(m);if(!m.isLoaded())m.onError(new Oe.Ws(Oe.Ws.Type.lza,f,b,g))}))}})};e()}}};
_.Jja=class extends _.tha{constructor(){super();this.ma=Object.create(null);this.va=Object.create(null);this.Ca=Infinity;this.oa=0;this.na=[];this.ka=Object.create(null);this.ha=this.eo("{base}")}Hga(){this.ha&&this.ha.getId()=="{base}"&&this.Da()}eo(a){let b=this.ma[a];b||(b=new _.Hh([],a),this.ma[a]=b);return b}nma(a,b){this.eo(a).isLoaded()?this.load(b):(this.ka[a]||(this.ka[a]={}),this.ka[a][b]=!0)}fga(a,b){if(this.ka[a]){delete this.ka[a][b];for(const c in this.ka[a])return;delete this.ka[a]}}Ha(a){return!!this.va[a]}load(a){Fja(this,
[a]);return Gja(this,a)}Aa(a){const b=Object.create(null),c=[];a.forEach(d=>{b[d]||(b[d]=Gja(this,d),c.push(d))});Fja(this,c);return b}wb(a){let b;((b=this.ha)==null?void 0:b.getId())==="synthetic_module_overhead"&&this.Da();let c,d;Hja(this,(d=(c=this.ma[a])==null?void 0:c.fK())!=null?d:[],e=>{e.ka=new lha},e=>!e.isLoaded());this.ha=this.eo(a)}Da(){if(this.ha){var a=this.ha.getId(),b=[];if(this.ka[a]){for(const c of Object.keys(this.ka[a]))this.eo(c).isLoaded()||(this.fga(a,c),b.push(c));this.Aa(b)}this.ha.onLoad(()=>
null);this.ha=null;(!this.Ha(a)&&/^sy[0-9a-z]{0,4}$/.test(a)||a==="synthetic_module_overhead")&&delete this.ma[a];Ija(this,a)}}Uva(a){this.Ca=a?Infinity:1}};_.Jja.prototype.kY=_.aa(9);var Kja=new _.Jja;Kja.Uva(!0);_.eda(Kja);(new _.vha).init();_.oea();var Lja=_.ed();Lja.Ba(sga)?(0,_.He)("Bi6EHd").then(()=>{}):_.Mh(_.De("dLc0B"),!1)?(0,_.He)("bYMqif").then(()=>{}):Lja.Ba(_.rga)?(0,_.He)("LQaXg").then(()=>{}):Kja.Aa(["LQaXg","HwBxOc","ZQlXXb","OpU7Tc"]);
_._ModuleManager_initialize=function(a,b){if(!_.fd){if(!_.gd)return;_.eda(_.gd())}_.fd.Hga(a,b)};
_._ModuleManager_initialize('',[]);
}catch(e){_._DumpException(e)}
}).call(this,this.default_BardChatUi);
// Google Inc.
