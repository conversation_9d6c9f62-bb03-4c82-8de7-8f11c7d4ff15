using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Input;
using Microsoft.Extensions.Logging;
using ExamBuilder.Core.Models;
using ExamBuilder.Core.Services;
using ExamBuilder.Security.Services;

namespace ExamBuilder.UI.ViewModels
{
    /// <summary>
    /// ViewModel للنافذة الرئيسية
    /// </summary>
    public class MainViewModel : INotifyPropertyChanged
    {
        private readonly IExamService _examService;
        private readonly IQuestionService _questionService;
        private readonly IAuthenticationService _authenticationService;
        private readonly ILogger<MainViewModel> _logger;

        private string _currentUser = string.Empty;
        private string _statusMessage = "مرحباً بك في برنامج إنشاء الامتحانات";
        private bool _isLoading = false;
        private Exam? _selectedExam;
        private Question? _selectedQuestion;

        public MainViewModel(
            IExamService examService,
            IQuestionService questionService,
            IAuthenticationService authenticationService,
            ILogger<MainViewModel> logger)
        {
            _examService = examService;
            _questionService = questionService;
            _authenticationService = authenticationService;
            _logger = logger;

            // تهيئة المجموعات
            Exams = new ObservableCollection<Exam>();
            Questions = new ObservableCollection<Question>();
            RecentExams = new ObservableCollection<Exam>();

            // تهيئة الأوامر
            InitializeCommands();

            // تحميل البيانات الأولية
            _ = LoadInitialDataAsync();
        }

        #region Properties

        public string CurrentUser
        {
            get => _currentUser;
            set => SetProperty(ref _currentUser, value);
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        public Exam? SelectedExam
        {
            get => _selectedExam;
            set
            {
                if (SetProperty(ref _selectedExam, value))
                {
                    _ = LoadQuestionsForSelectedExamAsync();
                    UpdateCommandStates();
                }
            }
        }

        public Question? SelectedQuestion
        {
            get => _selectedQuestion;
            set
            {
                if (SetProperty(ref _selectedQuestion, value))
                {
                    UpdateCommandStates();
                }
            }
        }

        public ObservableCollection<Exam> Exams { get; }
        public ObservableCollection<Question> Questions { get; }
        public ObservableCollection<Exam> RecentExams { get; }

        #endregion

        #region Commands

        public ICommand CreateExamCommand { get; private set; } = null!;
        public ICommand EditExamCommand { get; private set; } = null!;
        public ICommand DeleteExamCommand { get; private set; } = null!;
        public ICommand DuplicateExamCommand { get; private set; } = null!;
        public ICommand ExportExamCommand { get; private set; } = null!;

        public ICommand AddQuestionCommand { get; private set; } = null!;
        public ICommand EditQuestionCommand { get; private set; } = null!;
        public ICommand DeleteQuestionCommand { get; private set; } = null!;
        public ICommand MoveQuestionUpCommand { get; private set; } = null!;
        public ICommand MoveQuestionDownCommand { get; private set; } = null!;

        public ICommand RefreshCommand { get; private set; } = null!;
        public ICommand SettingsCommand { get; private set; } = null!;
        public ICommand AboutCommand { get; private set; } = null!;
        public ICommand LogoutCommand { get; private set; } = null!;

        #endregion

        #region Command Initialization

        private void InitializeCommands()
        {
            // أوامر الامتحانات
            CreateExamCommand = new RelayCommand(async () => await CreateExamAsync());
            EditExamCommand = new RelayCommand(async () => await EditExamAsync(), () => SelectedExam != null);
            DeleteExamCommand = new RelayCommand(async () => await DeleteExamAsync(), () => SelectedExam != null);
            DuplicateExamCommand = new RelayCommand(async () => await DuplicateExamAsync(), () => SelectedExam != null);
            ExportExamCommand = new RelayCommand(async () => await ExportExamAsync(), () => SelectedExam != null);

            // أوامر الأسئلة
            AddQuestionCommand = new RelayCommand(async () => await AddQuestionAsync(), () => SelectedExam != null);
            EditQuestionCommand = new RelayCommand(async () => await EditQuestionAsync(), () => SelectedQuestion != null);
            DeleteQuestionCommand = new RelayCommand(async () => await DeleteQuestionAsync(), () => SelectedQuestion != null);
            MoveQuestionUpCommand = new RelayCommand(async () => await MoveQuestionUpAsync(), () => CanMoveQuestionUp());
            MoveQuestionDownCommand = new RelayCommand(async () => await MoveQuestionDownAsync(), () => CanMoveQuestionDown());

            // أوامر عامة
            RefreshCommand = new RelayCommand(async () => await RefreshDataAsync());
            SettingsCommand = new RelayCommand(() => ShowSettings());
            AboutCommand = new RelayCommand(() => ShowAbout());
            LogoutCommand = new RelayCommand(() => Logout());
        }

        private void UpdateCommandStates()
        {
            ((RelayCommand)EditExamCommand).RaiseCanExecuteChanged();
            ((RelayCommand)DeleteExamCommand).RaiseCanExecuteChanged();
            ((RelayCommand)DuplicateExamCommand).RaiseCanExecuteChanged();
            ((RelayCommand)ExportExamCommand).RaiseCanExecuteChanged();
            ((RelayCommand)AddQuestionCommand).RaiseCanExecuteChanged();
            ((RelayCommand)EditQuestionCommand).RaiseCanExecuteChanged();
            ((RelayCommand)DeleteQuestionCommand).RaiseCanExecuteChanged();
            ((RelayCommand)MoveQuestionUpCommand).RaiseCanExecuteChanged();
            ((RelayCommand)MoveQuestionDownCommand).RaiseCanExecuteChanged();
        }

        #endregion

        #region Command Implementations

        private async Task CreateExamAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "جارٍ إنشاء امتحان جديد...";

                var newExam = new Exam
                {
                    Title = "امتحان جديد",
                    Subject = "مادة دراسية",
                    CreatedBy = CurrentUser,
                    CreatedAt = DateTime.Now
                };

                var createdExam = await _examService.CreateExamAsync(newExam);
                Exams.Add(createdExam);
                SelectedExam = createdExam;

                StatusMessage = "تم إنشاء امتحان جديد بنجاح";
                _logger.LogInformation($"تم إنشاء امتحان جديد: {createdExam.Title}");
            }
            catch (Exception ex)
            {
                StatusMessage = "حدث خطأ أثناء إنشاء الامتحان";
                _logger.LogError(ex, "خطأ في إنشاء امتحان جديد");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task EditExamAsync()
        {
            if (SelectedExam == null) return;

            try
            {
                IsLoading = true;
                StatusMessage = "جارٍ تحديث الامتحان...";

                // TODO: فتح نافذة تحرير الامتحان
                // var editWindow = new ExamEditWindow(SelectedExam);
                // if (editWindow.ShowDialog() == true)
                // {
                //     var updatedExam = await _examService.UpdateExamAsync(editWindow.Exam);
                //     // تحديث القائمة
                // }

                StatusMessage = "تم تحديث الامتحان بنجاح";
            }
            catch (Exception ex)
            {
                StatusMessage = "حدث خطأ أثناء تحديث الامتحان";
                _logger.LogError(ex, "خطأ في تحديث الامتحان");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task DeleteExamAsync()
        {
            if (SelectedExam == null) return;

            try
            {
                // TODO: إظهار رسالة تأكيد
                // var result = MessageBox.Show("هل أنت متأكد من حذف هذا الامتحان؟", "تأكيد الحذف", MessageBoxButton.YesNo);
                // if (result != MessageBoxResult.Yes) return;

                IsLoading = true;
                StatusMessage = "جارٍ حذف الامتحان...";

                var success = await _examService.DeleteExamAsync(SelectedExam.Id);
                if (success)
                {
                    Exams.Remove(SelectedExam);
                    SelectedExam = null;
                    StatusMessage = "تم حذف الامتحان بنجاح";
                }
                else
                {
                    StatusMessage = "فشل في حذف الامتحان";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = "حدث خطأ أثناء حذف الامتحان";
                _logger.LogError(ex, "خطأ في حذف الامتحان");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task DuplicateExamAsync()
        {
            if (SelectedExam == null) return;

            try
            {
                IsLoading = true;
                StatusMessage = "جارٍ استنساخ الامتحان...";

                var newTitle = $"{SelectedExam.Title} - نسخة";
                var duplicatedExam = await _examService.DuplicateExamAsync(SelectedExam.Id, newTitle);

                Exams.Add(duplicatedExam);
                SelectedExam = duplicatedExam;

                StatusMessage = "تم استنساخ الامتحان بنجاح";
            }
            catch (Exception ex)
            {
                StatusMessage = "حدث خطأ أثناء استنساخ الامتحان";
                _logger.LogError(ex, "خطأ في استنساخ الامتحان");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task ExportExamAsync()
        {
            if (SelectedExam == null) return;

            try
            {
                IsLoading = true;
                StatusMessage = "جارٍ تصدير الامتحان...";

                // TODO: تنفيذ تصدير PDF
                // var pdfPath = await _pdfService.GenerateExamPdfAsync(SelectedExam);
                // Process.Start(new ProcessStartInfo(pdfPath) { UseShellExecute = true });

                StatusMessage = "تم تصدير الامتحان بنجاح";
            }
            catch (Exception ex)
            {
                StatusMessage = "حدث خطأ أثناء تصدير الامتحان";
                _logger.LogError(ex, "خطأ في تصدير الامتحان");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task AddQuestionAsync()
        {
            if (SelectedExam == null) return;

            try
            {
                IsLoading = true;
                StatusMessage = "جارٍ إضافة سؤال جديد...";

                var newQuestion = new Question
                {
                    Text = "سؤال جديد",
                    Type = QuestionType.MultipleChoice,
                    Points = 1,
                    Order = Questions.Count + 1,
                    ExamId = SelectedExam.Id
                };

                var addedQuestion = await _examService.AddQuestionToExamAsync(SelectedExam.Id, newQuestion);
                Questions.Add(addedQuestion);
                SelectedQuestion = addedQuestion;

                StatusMessage = "تم إضافة السؤال بنجاح";
            }
            catch (Exception ex)
            {
                StatusMessage = "حدث خطأ أثناء إضافة السؤال";
                _logger.LogError(ex, "خطأ في إضافة سؤال جديد");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task EditQuestionAsync()
        {
            if (SelectedQuestion == null) return;

            try
            {
                // TODO: فتح نافذة تحرير السؤال
                StatusMessage = "جارٍ تحديث السؤال...";
                await Task.Delay(100); // مؤقت
                StatusMessage = "تم تحديث السؤال بنجاح";
            }
            catch (Exception ex)
            {
                StatusMessage = "حدث خطأ أثناء تحديث السؤال";
                _logger.LogError(ex, "خطأ في تحديث السؤال");
            }
        }

        private async Task DeleteQuestionAsync()
        {
            if (SelectedQuestion == null || SelectedExam == null) return;

            try
            {
                IsLoading = true;
                StatusMessage = "جارٍ حذف السؤال...";

                var success = await _examService.RemoveQuestionFromExamAsync(SelectedExam.Id, SelectedQuestion.Id);
                if (success)
                {
                    Questions.Remove(SelectedQuestion);
                    SelectedQuestion = null;
                    StatusMessage = "تم حذف السؤال بنجاح";
                }
                else
                {
                    StatusMessage = "فشل في حذف السؤال";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = "حدث خطأ أثناء حذف السؤال";
                _logger.LogError(ex, "خطأ في حذف السؤال");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task MoveQuestionUpAsync()
        {
            if (SelectedQuestion == null) return;
            // TODO: تنفيذ نقل السؤال لأعلى
            await Task.Delay(100);
        }

        private async Task MoveQuestionDownAsync()
        {
            if (SelectedQuestion == null) return;
            // TODO: تنفيذ نقل السؤال لأسفل
            await Task.Delay(100);
        }

        private bool CanMoveQuestionUp()
        {
            return SelectedQuestion != null && Questions.IndexOf(SelectedQuestion) > 0;
        }

        private bool CanMoveQuestionDown()
        {
            return SelectedQuestion != null && Questions.IndexOf(SelectedQuestion) < Questions.Count - 1;
        }

        private async Task RefreshDataAsync()
        {
            await LoadInitialDataAsync();
        }

        private void ShowSettings()
        {
            // TODO: فتح نافذة الإعدادات
        }

        private void ShowAbout()
        {
            // TODO: فتح نافذة حول البرنامج
        }

        private void Logout()
        {
            // TODO: تسجيل الخروج والعودة لنافذة تسجيل الدخول
        }

        #endregion

        #region Data Loading

        private async Task LoadInitialDataAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "جارٍ تحميل البيانات...";

                // تحميل الامتحانات
                var exams = await _examService.GetExamsByUserAsync(CurrentUser);
                Exams.Clear();
                foreach (var exam in exams)
                {
                    Exams.Add(exam);
                }

                // تحميل الامتحانات الحديثة
                var recentExams = await _examService.GetRecentExamsAsync(5);
                RecentExams.Clear();
                foreach (var exam in recentExams)
                {
                    RecentExams.Add(exam);
                }

                StatusMessage = $"تم تحميل {Exams.Count} امتحان";
            }
            catch (Exception ex)
            {
                StatusMessage = "حدث خطأ أثناء تحميل البيانات";
                _logger.LogError(ex, "خطأ في تحميل البيانات الأولية");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task LoadQuestionsForSelectedExamAsync()
        {
            if (SelectedExam == null) return;

            try
            {
                Questions.Clear();

                // تحميل أسئلة الامتحان المحدد
                foreach (var question in SelectedExam.Questions.OrderBy(q => q.Order))
                {
                    Questions.Add(question);
                }

                StatusMessage = $"تم تحميل {Questions.Count} سؤال";
            }
            catch (Exception ex)
            {
                StatusMessage = "حدث خطأ أثناء تحميل الأسئلة";
                _logger.LogError(ex, "خطأ في تحميل أسئلة الامتحان");
            }
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value))
                return false;

            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        #endregion
    }
}
