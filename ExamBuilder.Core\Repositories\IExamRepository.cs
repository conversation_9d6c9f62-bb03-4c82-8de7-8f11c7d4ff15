using ExamBuilder.Core.Models;

namespace ExamBuilder.Core.Services
{
    /// <summary>
    /// واجهة مستودع الامتحانات
    /// </summary>
    public interface IExamRepository
    {
        Task<Exam> AddAsync(Exam exam);
        Task<Exam?> GetByIdAsync(int id);
        Task<IEnumerable<Exam>> GetAllAsync();
        Task<IEnumerable<Exam>> GetByUserAsync(string userId);
        Task<Exam> UpdateAsync(Exam exam);
        Task<bool> DeleteAsync(int id);
        Task<IEnumerable<Exam>> SearchAsync(string searchTerm);
        Task<IEnumerable<Exam>> GetBySubjectAsync(string subject);
        Task<IEnumerable<Exam>> GetByGradeAsync(string grade);
        Task<IEnumerable<Exam>> GetRecentAsync(int count);
    }

    /// <summary>
    /// واجهة مستودع الأسئلة
    /// </summary>
    public interface IQuestionRepository
    {
        Task<Question> AddAsync(Question question);
        Task<Question?> GetByIdAsync(int id);
        Task<IEnumerable<Question>> GetByExamIdAsync(int examId);
        Task<Question> UpdateAsync(Question question);
        Task<bool> DeleteAsync(int id);
        Task<bool> ReorderAsync(int examId, List<int> questionIds);
    }

    /// <summary>
    /// تنفيذ مؤقت لمستودع الامتحانات
    /// </summary>
    public class ExamRepository : IExamRepository
    {
        private static readonly List<Exam> _exams = new();
        private static int _nextId = 1;

        public async Task<Exam> AddAsync(Exam exam)
        {
            await Task.Delay(100);
            exam.Id = _nextId++;
            exam.CreatedAt = DateTime.Now;
            _exams.Add(exam);
            return exam;
        }

        public async Task<Exam?> GetByIdAsync(int id)
        {
            await Task.Delay(100);
            return _exams.FirstOrDefault(e => e.Id == id);
        }

        public async Task<IEnumerable<Exam>> GetAllAsync()
        {
            await Task.Delay(100);
            return _exams.ToList();
        }

        public async Task<IEnumerable<Exam>> GetByUserAsync(string userId)
        {
            await Task.Delay(100);
            return _exams.Where(e => e.CreatedBy == userId).ToList();
        }

        public async Task<Exam> UpdateAsync(Exam exam)
        {
            await Task.Delay(100);
            var existing = _exams.FirstOrDefault(e => e.Id == exam.Id);
            if (existing != null)
            {
                var index = _exams.IndexOf(existing);
                exam.UpdatedAt = DateTime.Now;
                _exams[index] = exam;
            }
            return exam;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            await Task.Delay(100);
            var exam = _exams.FirstOrDefault(e => e.Id == id);
            if (exam != null)
            {
                _exams.Remove(exam);
                return true;
            }
            return false;
        }

        public async Task<IEnumerable<Exam>> SearchAsync(string searchTerm)
        {
            await Task.Delay(100);
            return _exams.Where(e => 
                e.Title.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                e.Subject.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)
            ).ToList();
        }

        public async Task<IEnumerable<Exam>> GetBySubjectAsync(string subject)
        {
            await Task.Delay(100);
            return _exams.Where(e => e.Subject == subject).ToList();
        }

        public async Task<IEnumerable<Exam>> GetByGradeAsync(string grade)
        {
            await Task.Delay(100);
            return _exams.Where(e => e.Grade == grade).ToList();
        }

        public async Task<IEnumerable<Exam>> GetRecentAsync(int count)
        {
            await Task.Delay(100);
            return _exams.OrderByDescending(e => e.CreatedAt).Take(count).ToList();
        }
    }

    /// <summary>
    /// تنفيذ مؤقت لمستودع الأسئلة
    /// </summary>
    public class QuestionRepository : IQuestionRepository
    {
        private static readonly List<Question> _questions = new();
        private static int _nextId = 1;

        public async Task<Question> AddAsync(Question question)
        {
            await Task.Delay(100);
            question.Id = _nextId++;
            question.CreatedAt = DateTime.Now;
            _questions.Add(question);
            return question;
        }

        public async Task<Question?> GetByIdAsync(int id)
        {
            await Task.Delay(100);
            return _questions.FirstOrDefault(q => q.Id == id);
        }

        public async Task<IEnumerable<Question>> GetByExamIdAsync(int examId)
        {
            await Task.Delay(100);
            return _questions.Where(q => q.ExamId == examId).OrderBy(q => q.Order).ToList();
        }

        public async Task<Question> UpdateAsync(Question question)
        {
            await Task.Delay(100);
            var existing = _questions.FirstOrDefault(q => q.Id == question.Id);
            if (existing != null)
            {
                var index = _questions.IndexOf(existing);
                question.UpdatedAt = DateTime.Now;
                _questions[index] = question;
            }
            return question;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            await Task.Delay(100);
            var question = _questions.FirstOrDefault(q => q.Id == id);
            if (question != null)
            {
                _questions.Remove(question);
                return true;
            }
            return false;
        }

        public async Task<bool> ReorderAsync(int examId, List<int> questionIds)
        {
            await Task.Delay(100);
            var examQuestions = _questions.Where(q => q.ExamId == examId).ToList();
            
            for (int i = 0; i < questionIds.Count; i++)
            {
                var question = examQuestions.FirstOrDefault(q => q.Id == questionIds[i]);
                if (question != null)
                {
                    question.Order = i + 1;
                }
            }
            
            return true;
        }
    }
}
