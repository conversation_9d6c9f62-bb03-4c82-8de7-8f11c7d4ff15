<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>بوابة مشاهدة الدروس</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; display: flex; justify-content: center; align-items: center; height: 100vh; margin: 0; background-color: #f4f7f6; }
        .container { text-align: center; padding: 40px; background-color: white; border-radius: 8px; box-shadow: 0 4px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; }
        input { padding: 10px; font-size: 16px; border-radius: 4px; border: 1px solid #ccc; margin: 10px 0; width: 250px; }
        button { padding: 10px 20px; font-size: 16px; color: white; background-color: #007bff; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background-color: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>أدخل كود المشاهدة</h1>
        <form id="codeForm">
            <input type="text" id="codeInput" placeholder="ادخل الكود هنا" required>
            <br>
            <button type="submit">مشاهدة</button>
        </form>
    </div>

    <script>
        document.getElementById('codeForm').addEventListener('submit', function(event) {
            event.preventDefault();
            const code = document.getElementById('codeInput').value;
            // يتم توجيه الطالب إلى الرابط المخصص له
            window.location.href = `/watch/${code}`;
        });
    </script>
</body>
</html>