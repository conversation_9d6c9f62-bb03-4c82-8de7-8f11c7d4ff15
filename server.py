import gspread
from flask import Flask, request, jsonify

app = Flask(__name__)

# إعداد Google Sheets API
SERVICE_ACCOUNT_FILE = "service_account.json"  # ملف JSON الخاص بـ Service Account
SHEET_NAME = "Authorized Devices"  # اسم Google Sheets

gc = gspread.service_account(filename=SERVICE_ACCOUNT_FILE)
sheet = gc.open(SHEET_NAME).sheet1

@app.route('/check_device', methods=['POST'])
def check_device():
    data = request.json
    hashed_id = data.get("hashed_id")
    authorized_devices = sheet.col_values(1)  # قراءة جميع الأجهزة المصرح بها من العمود الأول
    if hashed_id in authorized_devices:
        return jsonify({"authorized": True})
    else:
        return jsonify({"authorized": False}), 403

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)
