<Application x:Class="ExamBuilder.UI.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mah="http://metro.mahapps.com/winfx/xaml/controls"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             StartupUri="Views/LoginWindow.xaml">

    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- MahApps.Metro -->
                <ResourceDictionary Source="pack://application:,,,/MahApps.Metro;component/Styles/Controls.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MahApps.Metro;component/Styles/Fonts.xaml" />

                <!-- Material Design -->
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Light.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignColors;component/Themes/Recommended/Primary/MaterialDesignColor.DeepPurple.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignColors;component/Themes/Recommended/Accent/MaterialDesignColor.Lime.xaml" />

                <!-- Custom Themes -->
                <ResourceDictionary Source="Themes/RedTheme.xaml" />
                <ResourceDictionary Source="Themes/GreenTheme.xaml" />
                <ResourceDictionary Source="Themes/DarkTheme.xaml" />

                <!-- Custom Styles will be added later -->
            </ResourceDictionary.MergedDictionaries>

            <!-- Global RTL Support -->
            <Style TargetType="{x:Type Window}">
                <Setter Property="FlowDirection" Value="RightToLeft" />
                <Setter Property="FontFamily" Value="Segoe UI, Tahoma, Arial" />
                <Setter Property="FontSize" Value="14" />
            </Style>

            <!-- Arabic Font Support -->
            <FontFamily x:Key="ArabicFont">Segoe UI, Tahoma, Arial, Traditional Arabic</FontFamily>

            <!-- Application Colors -->
            <SolidColorBrush x:Key="PrimaryBrush" Color="#E94560" />
            <SolidColorBrush x:Key="SecondaryBrush" Color="#0F3460" />
            <SolidColorBrush x:Key="AccentBrush" Color="#16213E" />
            <SolidColorBrush x:Key="SuccessBrush" Color="#55FF55" />
            <SolidColorBrush x:Key="ErrorBrush" Color="#FF5555" />
            <SolidColorBrush x:Key="WarningBrush" Color="#FFB347" />
            <SolidColorBrush x:Key="InfoBrush" Color="#00FFFF" />

            <!-- Text Styles -->
            <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
                <Setter Property="FontFamily" Value="{StaticResource ArabicFont}" />
                <Setter Property="FontSize" Value="28" />
                <Setter Property="FontWeight" Value="Bold" />
                <Setter Property="Foreground" Value="{StaticResource InfoBrush}" />
                <Setter Property="TextAlignment" Value="Center" />
                <Setter Property="FlowDirection" Value="RightToLeft" />
            </Style>

            <Style x:Key="SubHeaderTextStyle" TargetType="TextBlock">
                <Setter Property="FontFamily" Value="{StaticResource ArabicFont}" />
                <Setter Property="FontSize" Value="18" />
                <Setter Property="FontWeight" Value="SemiBold" />
                <Setter Property="Foreground" Value="White" />
                <Setter Property="TextAlignment" Value="Center" />
                <Setter Property="FlowDirection" Value="RightToLeft" />
            </Style>

            <Style x:Key="BodyTextStyle" TargetType="TextBlock">
                <Setter Property="FontFamily" Value="{StaticResource ArabicFont}" />
                <Setter Property="FontSize" Value="14" />
                <Setter Property="Foreground" Value="White" />
                <Setter Property="TextAlignment" Value="Right" />
                <Setter Property="FlowDirection" Value="RightToLeft" />
            </Style>

            <!-- Button Styles -->
            <Style x:Key="PrimaryButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
                <Setter Property="FontFamily" Value="{StaticResource ArabicFont}" />
                <Setter Property="FontSize" Value="16" />
                <Setter Property="FontWeight" Value="Bold" />
                <Setter Property="Foreground" Value="White" />
                <Setter Property="Background" Value="{StaticResource PrimaryBrush}" />
                <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}" />
                <Setter Property="Height" Value="45" />
                <Setter Property="Margin" Value="5" />
                <Setter Property="Cursor" Value="Hand" />
                <Setter Property="FlowDirection" Value="RightToLeft" />
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Background" Value="#FF2E63" />
                        <Setter Property="BorderBrush" Value="#FF2E63" />
                    </Trigger>
                </Style.Triggers>
            </Style>

            <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
                <Setter Property="FontFamily" Value="{StaticResource ArabicFont}" />
                <Setter Property="FontSize" Value="16" />
                <Setter Property="FontWeight" Value="Bold" />
                <Setter Property="Foreground" Value="White" />
                <Setter Property="Background" Value="{StaticResource SecondaryBrush}" />
                <Setter Property="BorderBrush" Value="{StaticResource SecondaryBrush}" />
                <Setter Property="Height" Value="45" />
                <Setter Property="Margin" Value="5" />
                <Setter Property="Cursor" Value="Hand" />
                <Setter Property="FlowDirection" Value="RightToLeft" />
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Background" Value="#533483" />
                        <Setter Property="BorderBrush" Value="#533483" />
                    </Trigger>
                </Style.Triggers>
            </Style>

            <!-- Card Style -->
            <Style x:Key="CardStyle" TargetType="Border">
                <Setter Property="Background" Value="{StaticResource AccentBrush}" />
                <Setter Property="CornerRadius" Value="10" />
                <Setter Property="Margin" Value="10" />
                <Setter Property="Padding" Value="20" />
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect Color="Black" Direction="315" ShadowDepth="5" Opacity="0.3" BlurRadius="10"/>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- Input Styles -->
            <Style x:Key="InputTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignTextBox}">
                <Setter Property="FontFamily" Value="{StaticResource ArabicFont}" />
                <Setter Property="FontSize" Value="14" />
                <Setter Property="Height" Value="40" />
                <Setter Property="Margin" Value="5" />
                <Setter Property="FlowDirection" Value="RightToLeft" />
                <Setter Property="TextAlignment" Value="Right" />
            </Style>

            <Style x:Key="InputPasswordBoxStyle" TargetType="PasswordBox" BasedOn="{StaticResource MaterialDesignPasswordBox}">
                <Setter Property="FontFamily" Value="{StaticResource ArabicFont}" />
                <Setter Property="FontSize" Value="14" />
                <Setter Property="Height" Value="40" />
                <Setter Property="Margin" Value="5" />
                <Setter Property="FlowDirection" Value="RightToLeft" />
            </Style>

        </ResourceDictionary>
    </Application.Resources>
</Application>
