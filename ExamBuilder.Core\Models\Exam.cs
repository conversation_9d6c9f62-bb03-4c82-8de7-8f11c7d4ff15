using System.ComponentModel.DataAnnotations;

namespace ExamBuilder.Core.Models
{
    /// <summary>
    /// نموذج الامتحان
    /// </summary>
    public class Exam
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "عنوان الامتحان مطلوب")]
        [StringLength(200, ErrorMessage = "عنوان الامتحان يجب أن يكون أقل من 200 حرف")]
        public string Title { get; set; } = string.Empty;

        [StringLength(1000, ErrorMessage = "وصف الامتحان يجب أن يكون أقل من 1000 حرف")]
        public string? Description { get; set; }

        [Required(ErrorMessage = "اسم المادة مطلوب")]
        [StringLength(100, ErrorMessage = "اسم المادة يجب أن يكون أقل من 100 حرف")]
        public string Subject { get; set; } = string.Empty;

        [StringLength(100, ErrorMessage = "اسم المدرس يجب أن يكون أقل من 100 حرف")]
        public string? TeacherName { get; set; }

        [StringLength(100, ErrorMessage = "اسم المدرسة يجب أن يكون أقل من 100 حرف")]
        public string? SchoolName { get; set; }

        [StringLength(50, ErrorMessage = "الصف الدراسي يجب أن يكون أقل من 50 حرف")]
        public string? Grade { get; set; }

        [Range(1, 300, ErrorMessage = "مدة الامتحان يجب أن تكون بين 1 و 300 دقيقة")]
        public int DurationMinutes { get; set; } = 60;

        [Range(1, 1000, ErrorMessage = "إجمالي الدرجات يجب أن يكون بين 1 و 1000")]
        public decimal TotalMarks { get; set; } = 100;

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }

        [Required]
        public string CreatedBy { get; set; } = string.Empty;

        public string? UpdatedBy { get; set; }

        // خصائص التخصيص
        public ExamSettings Settings { get; set; } = new();

        // العلاقات
        public virtual ICollection<Question> Questions { get; set; } = new List<Question>();
    }

    /// <summary>
    /// إعدادات الامتحان
    /// </summary>
    public class ExamSettings
    {
        // إعدادات التخطيط
        public int QuestionsPerPage { get; set; } = 5;
        public bool ShowQuestionNumbers { get; set; } = true;
        public bool ShowPageNumbers { get; set; } = true;
        public bool CreateMultipleVersions { get; set; } = false;
        public int NumberOfVersions { get; set; } = 1;

        // إعدادات الخط والألوان
        public string FontFamily { get; set; } = "Traditional Arabic";
        public int FontSize { get; set; } = 14;
        public string FontColor { get; set; } = "#000000";
        public string NumberingColor { get; set; } = "#E94560";

        // إعدادات الغلاف
        public bool IncludeCover { get; set; } = true;
        public CoverType CoverType { get; set; } = CoverType.Regular;
        public string? LogoPath { get; set; }
        public string? FramePath { get; set; }

        // إعدادات الورقة
        public PageSize PageSize { get; set; } = PageSize.A4;
        public PageOrientation Orientation { get; set; } = PageOrientation.Portrait;
        public int MarginTop { get; set; } = 20;
        public int MarginBottom { get; set; } = 20;
        public int MarginLeft { get; set; } = 20;
        public int MarginRight { get; set; } = 20;

        // إعدادات الأمان
        public bool WatermarkEnabled { get; set; } = false;
        public string? WatermarkText { get; set; }
        public bool PasswordProtected { get; set; } = false;
        public string? Password { get; set; }
    }

    public enum CoverType
    {
        Regular = 0,
        BubbleSheet = 1
    }

    public enum PageSize
    {
        A4 = 0,
        A3 = 1,
        Letter = 2,
        Legal = 3
    }

    public enum PageOrientation
    {
        Portrait = 0,
        Landscape = 1
    }
}
