@echo off
echo ========================================
echo    Exam Builder - Telegram Auth
echo ========================================
echo.

echo Building project...
dotnet build --configuration Release

if %errorlevel% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo Copying settings...
copy ExamBuilder.UI\appsettings.json ExamBuilder.UI\bin\Release\net8.0-windows\ >nul

echo.
echo Starting application with Telegram auth...
echo.
echo NEW FEATURES:
echo - Telegram permission request system
echo - No auto-fill credentials
echo - Working theme buttons
echo - Fixed all warnings and errors
echo.
echo HOW IT WORKS:
echo 1. Enter username: جو (or joe or Jo)
echo 2. Enter password: جو (or joe or Jo)
echo 3. Click login button
echo 4. App will simulate sending request to Telegram
echo 5. You will see permission dialog (simulation)
echo 6. Click Yes to approve and open main window
echo.

cd ExamBuilder.UI\bin\Release\net8.0-windows
start ExamBuilder.UI.exe

echo.
echo Application launched with Telegram auth!
echo Check the app for permission request simulation.
pause
