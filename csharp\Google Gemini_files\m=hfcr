"use strict";this.default_IdentityRotateCookiesHttp=this.default_IdentityRotateCookiesHttp||{};(function(_){var window=this;
try{
_._F_toggles_initialize=function(a){(typeof globalThis!=="undefined"?globalThis:typeof self!=="undefined"?self:this)._F_toggles=a||[]};(0,_._F_toggles_initialize)([0xc000, ]);
/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var aa=function(a){g.setTimeout(()=>{throw a;},0)},l=function(a,b){a.__closure__error__context__984382||(a.__closure__error__context__984382={});a.__closure__error__context__984382.severity=b},m=function(a){a=Error(a);l(a,"warning");return a},n=function(a,b=!1){return b&&Symbol.for&&a?Symbol.for(a):a!=null?Symbol(a):Symbol()},q=function(){return typeof BigInt==="function"},v=function(a,b){return b===void 0?a.i!==r&&!!(2&(a.h[u]|0)):!!(2&b)&&a.i!==r},z=function(a){a.v=!0;return a},C=function(a){var b=
a;if(A(b)){if(!/^\s*(?:-?[1-9]\d*|0)?\s*$/.test(b))throw Error(String(b));}else if(ba(b)&&!Number.isSafeInteger(b))throw Error(String(b));return B?BigInt(a):a=ca(a)?a?"1":"0":A(a)?a.trim()||"0":String(a)},D=function(a,b){if(a.length>b.length)return!1;if(a.length<b.length||a===b)return!0;for(let c=0;c<a.length;c++){const d=a[c],e=b[c];if(d>e)return!1;if(d<e)return!0}},da=function(a){const b=a>>>0;F=b;G=(a-b)/**********>>>0},I=function(a){if(a<0){da(-a);const [b,c]=H(F,G);F=b>>>0;G=c>>>0}else da(a)},
J=function(a,b){b>>>=0;a>>>=0;if(b<=2097151)var c=""+(***********b+a);else q()?c=""+(BigInt(b)<<BigInt(32)|BigInt(a)):(c=(a>>>24|b<<8)&16777215,b=b>>16&65535,a=(a&16777215)+c*6777216+b*6710656,c+=b*8147497,b*=2,a>=1E7&&(c+=a/1E7>>>0,a%=1E7),c>=1E7&&(b+=c/1E7>>>0,c%=1E7),c=b+ea(c)+ea(a));return c},ea=function(a){a=String(a);return"0000000".slice(a.length)+a},fa=function(){var a=F,b=G;if(b&2147483648)if(q())a=""+(BigInt(b|0)<<BigInt(32)|BigInt(a>>>0));else{const [c,d]=H(a,b);a="-"+J(c,d)}else a=J(a,
b);return a},H=function(a,b){b=~b;a?a=~a+1:b+=1;return[a,b]},la=function(a){switch(typeof a){case "bigint":return!0;case "number":return ha(a);case "string":return ia.test(a);default:return!1}},ma=function(a){const b=a.length;return a[0]==="-"?b<20?!0:b===20&&Number(a.substring(0,7))>-922337:b<19?!0:b===19&&Number(a.substring(0,6))<922337},na=function(a){if(ma(a))return a;if(a.length<16)I(Number(a));else if(q())a=BigInt(a),F=Number(a&BigInt(4294967295))>>>0,G=Number(a>>BigInt(32)&BigInt(4294967295));
else{const b=+(a[0]==="-");G=F=0;const c=a.length;for(let d=b,e=(c-b)%6+b;e<=c;d=e,e+=6){const k=Number(a.slice(d,e));G*=1E6;F=F*1E6+k;F>=**********&&(G+=Math.trunc(F/**********),G>>>=0,F>>>=0)}if(b){const [d,e]=H(F,G);F=d;G=e}}return fa()},oa=function(a){a=K(a);if(!L(a)){I(a);var b=F,c=G;if(a=c&2147483648)b=~b+1>>>0,c=~c>>>0,b==0&&(c=c+1>>>0);const d=c***********+(b>>>0);b=Number.isSafeInteger(d)?d:J(b,c);a=typeof b==="number"?a?-b:b:a?"-"+b:b}return a},pa=function(a){return a},M=function(a,b,c,
d){var e=d!==void 0;d=!!d;const k=[];var h=a.length;let f,t=4294967295,ja=!1;const E=!!(b&64),w=E?b&128?0:-1:void 0;if(!(b&1||(f=h&&a[h-1],f!=null&&typeof f==="object"&&f.constructor===Object?(h--,t=h):f=void 0,!E||b&128||e))){ja=!0;var p;t=((p=qa)!=null?p:pa)(t-w,w,a,f)+w}b=void 0;for(e=0;e<h;e++)if(p=a[e],p!=null&&(p=c(p,d))!=null)if(E&&e>=t){const x=e-w;let y;((y=b)!=null?y:b={})[x]=p}else k[e]=p;if(f)for(let x in f){a=f[x];if(a==null||(a=c(a,d))==null)continue;h=+x;let y;if(E&&!Number.isNaN(h)&&
(y=h+w)<t)k[y]=a;else{let ka;((ka=b)!=null?ka:b={})[x]=a}}b&&(ja?k.push(b):k[t]=b);return k},sa=function(a){switch(typeof a){case "number":return Number.isFinite(a)?a:""+a;case "bigint":return ra(a)?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){const b=a[u]|0;return a.length===0&&b&1?void 0:M(a,b,sa)}if(a!=null&&a[N]===O)return P(a);return}return a},P=function(a){a=a.h;return M(a,a[u]|0,sa)},ua=function(a,b,c){if(a==null){var d=32;c?(a=[c],d|=128):a=[];b&&(d=d&-8380417|
(b&1023)<<13)}else{if(!Array.isArray(a))throw Error("a");d=a[u]|0;2048&d&&!(2&d)&&ta();if(d&256)throw Error("c");if(d&64)return d&2048||(a[u]=d|2048),a;if(c&&(d|=128,c!==a[0]))throw Error("d");a:{c=a;d|=64;var e=c.length;if(e){var k=e-1;const f=c[k];if(f!=null&&typeof f==="object"&&f.constructor===Object){b=d&128?0:-1;k-=b;if(k>=1024)throw Error("f");for(var h in f)if(e=+h,e<k)c[e+b]=f[h],delete f[h];else break;d=d&-8380417|(k&1023)<<13;break a}}if(b){h=Math.max(b,e-(d&128?0:-1));if(h>1024)throw Error("g");
d=d&-8380417|(h&1023)<<13}}}a[u]=d|2112;return a},ta=function(){if(Q!=null){var a;var b=(a=va)!=null?a:va={};a=b[Q]||0;a>=5||(b[Q]=a+1,b=Error(),l(b,"incident"),aa(b))}},wa=function(a,b){if(typeof a!=="object")return a;if(Array.isArray(a)){var c=a[u]|0;a.length===0&&c&1?a=void 0:c&2||(!b||4096&c||16&c?a=R(a,c,!1,b&&!(c&16)):(a[u]|=34,c&4&&Object.freeze(a)));return a}if(a!=null&&a[N]===O){c=a.h;const d=c[u]|0;v(a,d)||(d&2?b=!0:d&32&&!(d&4096)?(c[u]=d|2,a.i=r,b=!0):b=!1,b?(a=new a.constructor(c),a.u=
r):a=R(c,d));return a}},R=function(a,b,c,d){d!=null||(d=!!(34&b));a=M(a,b,wa,d);d=32;c&&(d|=2);b=b&8380609|d;a[u]=b;return a},xa=function(a){return ra(a)?Number(a):String(a)};var g=this||self;var va=void 0;var Q=n(),N=n("m_m",!0);var u=n("jas",!0);var O={},r={},ya={};var ba=z(a=>typeof a==="number"),A=z(a=>typeof a==="string"),ca=z(a=>typeof a==="boolean");var B=typeof g.BigInt==="function"&&typeof g.BigInt(0)==="bigint";var ra=z(a=>B?a>=za&&a<=Aa:a[0]==="-"?D(a,Ba):D(a,Ca)),Ba=Number.MIN_SAFE_INTEGER.toString(),za=B?BigInt(Number.MIN_SAFE_INTEGER):void 0,Ca=Number.MAX_SAFE_INTEGER.toString(),Aa=B?BigInt(Number.MAX_SAFE_INTEGER):void 0;var F=0,G=0;var S=typeof BigInt==="function"?BigInt.asIntN:void 0,L=Number.isSafeInteger,ha=Number.isFinite,K=Math.trunc,ia=/^-?([1-9][0-9]*|0)(\.[0-9]+)?$/;var qa;var Da=C(0),Ea=function(a,b,c){if(a.i===r){var d=a.h;d=R(d,d[u]|0);d[u]|=2048;a.h=d;a.i=void 0;a.u=void 0;d=!0}else d=!1;if(!d&&v(a,a.h[u]|0))throw Error();d=a.h;a:{var e=d[u]|0;const k=b+-1,h=d.length-1;if(h>=0&&k>=h){const f=d[h];if(f!=null&&typeof f==="object"&&f.constructor===Object){f[b]=c;break a}}k<=h?d[k]=c:c!==void 0&&(e=(e!=null?e:d[u]|0)>>13&1023||536870912,b>=e?c!=null&&(d[e+-1]={[b]:c}):d[k]=c)}return a},Fa=function(a){var b=a.h;a=1+(ya?0:-1);var c=b.length-1;c<1+(ya?0:-1)?a=void 0:a>=
c?(b=b[c],b!=null&&typeof b==="object"&&b.constructor===Object?a=b[1]:a===c?a=b:a=void 0):a=b[a];a=a!==null?a:void 0;c=typeof a;a!=null&&(c==="bigint"?a=C(S(64,a)):la(a)?c==="string"?(c=K(Number(a)),L(c)?a=C(c):(c=a.indexOf("."),c!==-1&&(a=a.substring(0,c)),a=q()?C(S(64,BigInt(a))):C(na(a)))):L(a)?a=C(oa(a)):(a=K(a),L(a)?a=String(a):(c=String(a),ma(c)?a=c:(I(a),a=fa())),a=C(a)):a=void 0);return a!=null?a:Da},T=function(a,b,c){if(c!=null){if(typeof c!=="number")throw m("int32");if(!ha(c))throw m("int32");
c|=0}Ea(a,b,c)};var U=class{constructor(a,b,c){this.h=ua(a,b,c)}toJSON(){return P(this)}};U.prototype[N]=O;U.prototype.toString=function(){return this.h.toString()};var Ga=class extends U{constructor(a){super(a)}};var Ha=function(a){return b=>{if(b==null||b=="")b=new a;else{b=JSON.parse(b);if(!Array.isArray(b))throw Error("h");b[u]|=32;b=new a(b)}return b}}(class extends U{constructor(a){super(a,0,"identity.hfcr")}});var Ia=function(a){a={method:"POST",credentials:"same-origin",cache:"no-store",mode:"same-origin",headers:{"Content-Type":"application/json"},body:JSON.stringify(P(a))};if(typeof AbortController!=="undefined"){const b=new AbortController;setTimeout(()=>{b.abort()},3E4);a.signal=b.signal}return fetch(new Request("/RotateCookies",a)).then(b=>b.text()).then(b=>Ha(JSON.stringify(JSON.parse(b.substring(5))[0])))},Ja=function(){try{const a=window.localStorage;if(!a)return!1;a.setItem("cookieRotationStorageAccessTest",
"1");a.removeItem("cookieRotationStorageAccessTest");return!0}catch(a){return!1}},V=function(){try{const a=window.localStorage.getItem("nextRotationAttemptTs");if(!a)return null;const b=Math.floor(Number(a));return Number.isNaN(b)?null:b}catch(a){return null}},W=function(a){try{window.localStorage.setItem("nextRotationAttemptTs",a.toString())}catch(b){}},La=function(a){const b=V();if(!b||Date.now()>=b){const c=Math.floor(Math.random()*1E3);return new Promise(d=>{setTimeout(()=>{const e=V();!e||Date.now()>=
e?d(Ka(a)):d()},c)})}return Promise.resolve()},Na=function(a){Ma(a).then(()=>{const b=()=>{Ma(a).then(()=>{setTimeout(b,a.g*1E3)})};setTimeout(()=>{b()},a.g*1E3)})},Ma=function(a){const b=Oa(a);return Ia(b).then(c=>{c=Pa(xa(Fa(c)));c!==a.g&&(a.g=c)}).catch(()=>{a.g*=2})},Oa=function(a){var b=new Ga;var c=a.o;if(c!=null)a:{if(!la(c))throw m("int64");switch(typeof c){case "string":var d=K(Number(c));L(d)?c=String(d):(d=c.indexOf("."),d!==-1&&(c=c.substring(0,d)),c=na(c));break a;case "bigint":c=C(S(64,
c));break a;default:c=oa(c)}}b=Ea(b,2,c);a.l!==0&&T(b,1,a.l);a.m!==0&&T(b,3,a.m);a.j!==0&&T(b,4,a.j);return b},Pa=function(a){a<60&&(a=60);return a},Ka=function(a){W(Date.now()+a.g*1E3);const b=Oa(a);return Ia(b).then(c=>{c=Pa(xa(Fa(c)));c!==a.g&&(W(Date.now()+c*1E3),a.g=c)}).catch(()=>{a.g*=2;W(Date.now()+a.g*1E3)})},Qa=class{constructor(a,b,c,d,e){this.o=a;this.l=b;this.m=c;this.j=d;this.g=e}start(){if(typeof fetch!=="undefined")if(Ja()){var a=V(),b=Date.now();a&&a>b+this.g*1E3&&(a=Date.now()+this.g*
1E3,W(a));var c=()=>{La(this).then(()=>{setTimeout(c,this.g*1E3)})};setTimeout(()=>{c()},a&&a>b?a-b:0)}else Na(this)}};for(var Ra=function(a,b,c,d,e){(new Qa(a,b,c,d,e)).start()},X=["init"],Y=g,Z;X.length&&(Z=X.shift());)X.length||Ra===void 0?Y[Z]&&Y[Z]!==Object.prototype[Z]?Y=Y[Z]:Y=Y[Z]={}:Y[Z]=Ra;
}catch(e){_._DumpException(e)}
}).call(this,this.default_IdentityRotateCookiesHttp);
// Google Inc.
