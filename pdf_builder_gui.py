import customtkinter as ctk
import tkinter.filedialog as fd
from PIL import Image, ImageDraw, ImageFont
import os
import telegram
from telegram.ext import Application, CommandHandler, MessageHandler, filters
from telegram import Update
from telegram.ext.filters import TEXT
import threading
import time

# إعدادات الثيم
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("dark-blue")  # خلفية داكنة مع أزرار زرقاء

class PDFBuilderApp(ctk.CTk):
    def __init__(self):
        super().__init__()
        self.title("امتحانك بوك")
        self.geometry("1024x768")
        self.minsize(600, 450)

        # مسارات الصور وعدد الأسئلة
        self.question_images = []
        self.frame_image_path = None
        self.per_page = ctk.IntVar(value=4)
        # إضافة متغير لتخزين مسار اللوجو
        self.logo_image_path = None

        self.configure(fg_color="#1A1A2E")  # خلفية كحلي داكن

        self.create_widgets()

    def create_widgets(self):
        # عنوان التطبيق
        self.header = ctk.CTkLabel(
            self, text="النبي علي صلي ",  # Reversed word order
            font=ctk.CTkFont(size=28, weight="bold"),  # Increased font size
            text_color="#00FFFF", anchor="center"
        )
        self.header.grid(row=0, column=0, columnspan=2, pady=(20, 10), sticky="nsew")

        # بطاقة اختيار صور الأسئلة
        self.card_q = ctk.CTkFrame(self, fg_color="#16213E", corner_radius=10)
        self.card_q.grid(row=1, column=0, padx=20, pady=15, sticky="nsew")
        self.btn_q = ctk.CTkButton(
            self.card_q, command=self.select_questions,
            text="الأسئلة صور اختر",  # Reversed word order
            text_color="#FFFFFF",
            font=ctk.CTkFont(size=16),  # Increased font size
            fg_color="#0F3460", hover_color="#533483",
            corner_radius=8, height=60
        )
        self.btn_q.pack(expand=True, fill="both")

        # بطاقة اختيار صورة الفريم
        self.card_f = ctk.CTkFrame(self, fg_color="#16213E", corner_radius=10)
        self.card_f.grid(row=1, column=1, padx=20, pady=15, sticky="nsew")
        self.btn_f = ctk.CTkButton(
            self.card_f, command=self.select_frame,
            text="الفريم صورة اختر",  # Reversed word order
            text_color="#FFFFFF",
            font=ctk.CTkFont(size=16),  # Increased font size
            fg_color="#533483", hover_color="#862E9C",
            corner_radius=8, height=60
        )
        self.btn_f.pack(expand=True, fill="both")

        # بطاقة عدد الأسئلة
        self.card_n = ctk.CTkFrame(self, fg_color="#16213E", corner_radius=10)
        self.card_n.grid(row=2, column=0, padx=20, pady=15, sticky="nsew")
        lbl_n = ctk.CTkLabel(
            self.card_n, text="الصفحة في الأسئلة عدد",  # Reversed word order
            text_color="#FFFFFF", anchor="e",
            font=ctk.CTkFont(size=16)  # Increased font size
        )
        lbl_n.pack(pady=(10, 5), padx=10, fill="x")

        self.num_per_page_entry = ctk.CTkEntry(
            self.card_n, width=100, justify="center",
            text_color="#FFFFFF", fg_color="#1A1A2E",
            font=ctk.CTkFont(size=14)  # Increased font size
        )
        self.num_per_page_entry.insert(0, "4")  # Default value
        self.num_per_page_entry.pack(pady=(0, 10), padx=10, fill="x")

        # نقل التشك بوكس مباشرة أسفل صندوق إدخال عدد الصور
        self.numbering_var = ctk.BooleanVar(value=True)  # Default: numbering enabled
        self.numbering_checkbox = ctk.CTkCheckBox(
            self.card_n, text="الترقيم تفعيل", variable=self.numbering_var,  # Reversed word order
            font=ctk.CTkFont(size=14), text_color="#FFFFFF"
        )
        self.numbering_checkbox.pack(pady=(0, 10), padx=10, anchor="w")  # Adjusted to pack within the same card

        # بطاقة إنشاء PDF
        self.card_g = ctk.CTkFrame(self, fg_color="#16213E", corner_radius=10)
        self.card_g.grid(row=2, column=1, padx=20, pady=15, sticky="nsew")
        self.btn_g = ctk.CTkButton(
            self.card_g, command=self.generate_pdf,
            text="PDF إنشاء",  # Reversed word order
            text_color="#FFFFFF",
            font=ctk.CTkFont(size=16),  # Increased font size
            fg_color="#E94560", hover_color="#FF2E63",
            corner_radius=8, height=60
        )
        self.btn_g.pack(expand=True, fill="both")

        # تعديل موقع زر تحميل اللوجو
        self.card_logo = ctk.CTkFrame(self, fg_color="#16213E", corner_radius=10)
        self.card_logo.grid(row=4, column=0, columnspan=2, padx=20, pady=3, sticky="nsew")  # Adjusted row to move it higher

        # إطار الأزرار
        logo_buttons_frame = ctk.CTkFrame(self.card_logo, fg_color="transparent")
        logo_buttons_frame.pack(expand=True, fill="both", padx=10, pady=10)

        # زر اختيار اللوجو
        self.btn_logo = ctk.CTkButton(
            logo_buttons_frame, command=self.select_logo,
            text="اللوجو اختر",  # Reversed word order
            text_color="#FFFFFF",
            font=ctk.CTkFont(size=16),  # Increased font size
            fg_color="#0F3460", hover_color="#533483",
            corner_radius=8, height=60
        )
        self.btn_logo.pack(side="left", padx=(0, 5), fill="both", expand=True)

        # زر إزالة اللوجو
        self.btn_remove_logo = ctk.CTkButton(
            logo_buttons_frame, command=self.remove_logo,
            text="اللوجو إزالة",  # Reversed word order
            text_color="#FFFFFF",
            font=ctk.CTkFont(size=16),  # Increased font size
            fg_color="#E94560", hover_color="#FF2E63",
            corner_radius=8, height=60, state="disabled"
        )
        self.btn_remove_logo.pack(side="right", padx=(5, 0), fill="both", expand=True)

        # شريط الحالة
        self.status = ctk.CTkLabel(
            self, text="", text_color="#FFFFFF",
            anchor="center", font=ctk.CTkFont(size=14)  # Increased font size
        )
        self.status.grid(row=3, column=0, columnspan=2, pady=(0, 20), sticky="nsew")

        # إضافة إطار أزرار التواصل الاجتماعي
        social_frame = ctk.CTkFrame(self, fg_color="#16213E", corner_radius=10, border_width=2, border_color="#533483")
        social_frame.grid(row=5, column=0, columnspan=2, padx=20, pady=(10, 20), sticky="nsew")

        # عنوان التواصل
        ctk.CTkLabel(
            social_frame, text="المطور مع تواصل",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color="#FFFFFF", anchor="center"
        ).pack(pady=(10, 5))

        # إطار الأزرار
        buttons_frame = ctk.CTkFrame(social_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", padx=20, pady=(5, 15))

        # إنشاء ثلاثة أقسام متساوية للأزرار
        left_section = ctk.CTkFrame(buttons_frame, fg_color="transparent")
        left_section.pack(side="left", fill="both", expand=True, padx=5)

        middle_section = ctk.CTkFrame(buttons_frame, fg_color="transparent")
        middle_section.pack(side="left", fill="both", expand=True, padx=5)

        right_section = ctk.CTkFrame(buttons_frame, fg_color="transparent")
        right_section.pack(side="left", fill="both", expand=True, padx=5)

        # زر واتساب
        self.whatsapp_button = ctk.CTkButton(
            left_section, text="واتساب",
            command=lambda: self.open_social_link("https://wa.me/+201062680608"),
            font=ctk.CTkFont(size=16, weight="bold"), fg_color="#25D366", hover_color="#128C7E",
            text_color="#FFFFFF", corner_radius=8, height=40, border_width=1,
            border_color="#128C7E"
        )
        self.whatsapp_button.pack(fill="both", expand=True, pady=5)

        # زر فيسبوك
        self.facebook_button = ctk.CTkButton(
            middle_section, text="فيسبوك",
            command=lambda: self.open_social_link("https://www.facebook.com/profile.php?id=61555408544326"),
            font=ctk.CTkFont(size=16, weight="bold"), fg_color="#1877F2", hover_color="#0E5AA7",
            text_color="#FFFFFF", corner_radius=8, height=40, border_width=1,
            border_color="#0E5AA7"
        )
        self.facebook_button.pack(fill="both", expand=True, pady=5)

        # زر انستجرام
        self.instagram_button = ctk.CTkButton(
            right_section, text="انستجرام",
            command=lambda: self.open_social_link("https://www.instagram.com/yousef_.ghanam/"),
            font=ctk.CTkFont(size=16, weight="bold"), fg_color="#E1306C", hover_color="#C13584",
            text_color="#FFFFFF", corner_radius=8, height=40, border_width=1,
            border_color="#C13584"
        )
        self.instagram_button.pack(fill="both", expand=True, pady=5)

        # تعديل grid لتوسيع الصفوف
        for i in range(6):  # تعديل النطاق ليشمل الصف الجديد لأزرار التواصل الاجتماعي
            self.grid_rowconfigure(i, weight=1)
        for j in range(2):
            self.grid_columnconfigure(j, weight=1)



    def select_questions(self):
        files = fd.askopenfilenames(title="اختر صور الأسئلة", filetypes=[("Images","*.png *.jpg *.jpeg")])
        self.question_images = list(files)
        self.status.configure(text=f"تم اختيار {len(self.question_images)} صورة.")

    def select_frame(self):
        file = fd.askopenfilename(title="اختر صورة الفريم", filetypes=[("Images","*.png *.jpg *.jpeg")])
        self.frame_image_path = file
        self.status.configure(text="تم اختيار صورة الفريم.")

    # تعديل وظيفة select_logo لتحميل اللوجو
    def select_logo(self):
        file = fd.askopenfilename(title="اختر صورة اللوجو", filetypes=[("Images","*.png *.jpg *.jpeg")])
        if file:
            self.logo_image_path = file
            self.status.configure(text="تم اختيار صورة اللوجو.")
            # تفعيل زر إزالة اللوجو
            self.btn_remove_logo.configure(state="normal")
        else:
            self.status.configure(text="لم يتم اختيار صورة اللوجو.")

    def remove_logo(self):
        """إزالة اللوجو المختار"""
        if hasattr(self, 'logo_image_path') and self.logo_image_path:
            self.logo_image_path = None
            self.status.configure(text="تم إزالة اللوجو بنجاح.")
            # تعطيل زر إزالة اللوجو
            self.btn_remove_logo.configure(state="disabled")

            # عرض رسالة تأكيد
            try:
                from customtkinter import CTkMessagebox
                CTkMessagebox(
                    title="إزالة اللوجو",
                    message="تم إزالة اللوجو بنجاح",
                    icon="info",
                    option_1="موافق"
                )
            except:
                pass
        else:
            self.status.configure(text="لا يوجد لوجو لإزالته.")

    def open_social_link(self, url):
        """فتح رابط التواصل الاجتماعي في المتصفح"""
        try:
            import webbrowser
            webbrowser.open_new(url)
            # عرض رسالة نجاح
            if hasattr(self, 'status_label'):
                self.status_label.configure(text=f"تم فتح الرابط بنجاح")
            elif hasattr(self, 'status'):
                self.status.configure(text=f"تم فتح الرابط بنجاح")

            # إظهار رسالة تأكيد
            try:
                from customtkinter import CTkMessagebox
                CTkMessagebox(
                    title="فتح الرابط",
                    message="تم فتح الرابط في المتصفح",
                    icon="info",
                    option_1="موافق"
                )
            except:
                pass
        except Exception as e:
            # عرض رسالة الخطأ
            error_message = f"حدث خطأ أثناء فتح الرابط: {e}"
            if hasattr(self, 'status_label'):
                self.status_label.configure(text=error_message)
            elif hasattr(self, 'status'):
                self.status.configure(text=error_message)

            # إظهار رسالة خطأ
            try:
                from customtkinter import CTkMessagebox
                CTkMessagebox(
                    title="خطأ",
                    message=error_message,
                    icon="cancel",
                    option_1="موافق"
                )
            except:
                pass

    def generate_pdf(self):
        if not self.question_images or not self.frame_image_path:
            self.status.configure(text="اختر صور الأسئلة وصورة الفريم أولاً.")
            return

        try:
            per_page = int(self.num_per_page_entry.get())
            if per_page < 1 or per_page > 20:
                raise ValueError
        except ValueError:
            self.status.configure(text="أدخل عددًا صحيحًا بين 1 و 20.")
            return

        self.status.configure(text="جارٍ إنشاء PDF...")  # Show progress message
        self.update_idletasks()  # Force UI update

        try:
            # Load frame image
            frame_img = Image.open(self.frame_image_path).convert("RGB")
            frame_width, frame_height = frame_img.size
            margin_x = int(frame_width * 0.1)
            margin_y = int(frame_height * 0.1)
            question_height = (frame_height - 2 * margin_y) // per_page
            pages = []

            # Prepare fonts
            font_size_questions = int(question_height * 0.15)  # Reduced font size for numbering
            font_size_pages = int(frame_height * 0.035)
            font_questions = ImageFont.truetype("arial.ttf", font_size_questions)
            font_pages = ImageFont.truetype("arial.ttf", font_size_pages)

            # Generate pages
            for i in range(0, len(self.question_images), per_page):
                page = frame_img.copy()
                draw = ImageDraw.Draw(page)
                for j, q_img_path in enumerate(self.question_images[i:i + per_page]):
                    q_img = Image.open(q_img_path).resize((frame_width - 2 * margin_x, question_height - 10))
                    y = margin_y + j * question_height + 5
                    x = margin_x
                    page.paste(q_img, (x, y))

                    # Draw question number if numbering is enabled
                    if self.numbering_var.get():
                        draw.text((x + frame_width - 2 * margin_x + 10, y + question_height // 2 - font_size_questions // 2),
                                  str(i + j + 1), fill="black", font=font_questions)

                    # Draw separator line
                    if j < per_page - 1:
                        line_y = y + question_height - 5
                        draw.line([(margin_x, line_y), (frame_width - margin_x, line_y)], fill="black", width=2)

                # Draw page number
                page_number_text = str(i // per_page + 1)
                text_bbox = draw.textbbox((0, 0), page_number_text, font=font_pages)
                text_width = text_bbox[2] - text_bbox[0]
                text_height = text_bbox[3] - text_bbox[1]
                draw.text(((frame_width - text_width) // 2, frame_height - margin_y // 2 - text_height // 2),
                          page_number_text, fill="black", font=font_pages)

                pages.append(page)

            # Add logo as watermark if available
            if self.logo_image_path:
                logo_img = Image.open(self.logo_image_path).convert("RGBA")
                logo_width, logo_height = logo_img.size

                # Resize logo to fit within the page with margins similar to question images
                max_logo_width = frame_width - 2 * margin_x
                max_logo_height = frame_height - 2 * margin_y
                aspect_ratio = min(max_logo_width / logo_width, max_logo_height / logo_height)
                new_logo_width = int(logo_width * aspect_ratio)
                new_logo_height = int(logo_height * aspect_ratio)
                logo_img = logo_img.resize((new_logo_width, new_logo_height))

                # Reduce transparency
                alpha = logo_img.split()[3]  # Extract alpha channel
                alpha = alpha.point(lambda p: p * 0.5)  # Reduce opacity to 50%
                logo_img.putalpha(alpha)

                for page in pages:
                    # Center the logo within the margins
                    x_offset = margin_x + (max_logo_width - new_logo_width) // 2
                    y_offset = margin_y + (max_logo_height - new_logo_height) // 2
                    page.paste(logo_img, (x_offset, y_offset), logo_img)

            # Save PDF
            pdf_path = fd.asksaveasfilename(defaultextension=".pdf", filetypes=[("PDF files", "*.pdf")])
            if pdf_path:
                pages[0].save(pdf_path, save_all=True, append_images=pages[1:])
                self.status.configure(text="تم إنشاء ملف PDF بنجاح!")
            else:
                self.status.configure(text="تم إلغاء حفظ ملف PDF.")
        except Exception as e:
            self.status.configure(text=f"حدث خطأ أثناء إنشاء PDF: {e}")

class LoginApp(ctk.CTk):
    def __init__(self):
        super().__init__()
        self.title("  تسجيل الدخول")
        self.geometry("1024x768")
        self.minsize(600, 450)
        self.configure(fg_color="#1A1A2E")  # خلفية كحلي داكن

        # واجهة تسجيل الدخول
        self.header = ctk.CTkLabel(
            self, text="  الدخول تسجيل",
            font=ctk.CTkFont(size=24, weight="bold"),
            text_color="#00FFFF", anchor="center"
        )
        self.header.pack(pady=(30, 20))

        self.username_entry = ctk.CTkEntry(
            self, placeholder_text="   المستخدم اسم ",
            font=ctk.CTkFont(size=14), fg_color="#16213E",
            text_color="#FFFFFF"
        )
        self.username_entry.pack(pady=(10, 10), padx=20, fill="x")

        self.password_entry = ctk.CTkEntry(
            self, placeholder_text="  المرور كلمة ", show="*",
            font=ctk.CTkFont(size=14), fg_color="#16213E",
            text_color="#FFFFFF"
        )
        self.password_entry.pack(pady=(10, 20), padx=20, fill="x")

        self.login_button = ctk.CTkButton(
            self, text=" الدخول تسجيل", command=self.authenticate,
            font=ctk.CTkFont(size=16), fg_color="#E94560",
            hover_color="#FF2E63", corner_radius=8
        )
        self.login_button.pack(pady=(10, 20))

        self.status_label = ctk.CTkLabel(
            self, text="", text_color="#FFFFFF",
            font=ctk.CTkFont(size=14), anchor="center"
        )
        self.status_label.pack(pady=(10, 20))

        # Footer
        self.footer = ctk.CTkLabel(
            self, text="( دائمًا  تفوق في يجعله و يكرمه  ربنا   غنام يوسف / المهندس  بوسطة  البرنامج هذا  إنشاء تم)",
            font=ctk.CTkFont(size=18), text_color="#FFFFFF", anchor="center"
        )
        self.footer.pack(pady=(10, 10))

        # إضافة إطار أزرار التواصل الاجتماعي
        social_frame = ctk.CTkFrame(self, fg_color="#16213E", corner_radius=10, border_width=2, border_color="#533483")
        social_frame.pack(fill="x", padx=20, pady=(20, 20))

        # عنوان التواصل
        ctk.CTkLabel(
            social_frame, text="المطور مع تواصل",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color="#FFFFFF", anchor="center"
        ).pack(pady=(10, 5))

        # إطار الأزرار
        buttons_frame = ctk.CTkFrame(social_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", padx=20, pady=(5, 15))

        # إنشاء ثلاثة أقسام متساوية للأزرار
        left_section = ctk.CTkFrame(buttons_frame, fg_color="transparent")
        left_section.pack(side="left", fill="both", expand=True, padx=5)

        middle_section = ctk.CTkFrame(buttons_frame, fg_color="transparent")
        middle_section.pack(side="left", fill="both", expand=True, padx=5)

        right_section = ctk.CTkFrame(buttons_frame, fg_color="transparent")
        right_section.pack(side="left", fill="both", expand=True, padx=5)

        # زر واتساب
        self.whatsapp_button = ctk.CTkButton(
            left_section, text="واتساب",
            command=lambda: self.open_social_link("https://wa.me/+201062680608"),
            font=ctk.CTkFont(size=16, weight="bold"), fg_color="#25D366", hover_color="#128C7E",
            text_color="#FFFFFF", corner_radius=8, height=40, border_width=1,
            border_color="#128C7E"
        )
        self.whatsapp_button.pack(fill="both", expand=True, pady=5)

        # زر فيسبوك
        self.facebook_button = ctk.CTkButton(
            middle_section, text="فيسبوك",
            command=lambda: self.open_social_link("https://www.facebook.com/profile.php?id=61555408544326"),
            font=ctk.CTkFont(size=16, weight="bold"), fg_color="#1877F2", hover_color="#0E5AA7",
            text_color="#FFFFFF", corner_radius=8, height=40, border_width=1,
            border_color="#0E5AA7"
        )
        self.facebook_button.pack(fill="both", expand=True, pady=5)

        # زر انستجرام
        self.instagram_button = ctk.CTkButton(
            right_section, text="انستجرام",
            command=lambda: self.open_social_link("https://www.instagram.com/yousef_.ghanam/"),
            font=ctk.CTkFont(size=16, weight="bold"), fg_color="#E1306C", hover_color="#C13584",
            text_color="#FFFFFF", corner_radius=8, height=40, border_width=1,
            border_color="#C13584"
        )
        self.instagram_button.pack(fill="both", expand=True, pady=5)

        # متغير لتخزين حالة الموافقة
        self.approval_status = None

    def authenticate(self):
        username = self.username_entry.get()
        password = self.password_entry.get()
        if username == "jo" and password == "jo":
            self.status_label.configure(text="جارٍ إرسال طلب الإذن...")

            # إرسال طلب الإذن إلى البوت
            threading.Thread(target=self.request_approval, args=(username,)).start()
        else:
            self.status_label.configure(text="صحيحة غير المرور كلمة  او المستخدم اسم ")

    def request_approval(self, username):
        from telegram import Bot

        bot_token = "**********************************************"  # Updated bot token
        chat_id = "1486393122"  # Updated Telegram chat ID
        bot = Bot(token=bot_token)

        # إرسال رسالة طلب الإذن
        try:
            bot.send_message(chat_id=chat_id, text=f"طلب تسجيل دخول من المستخدم: {username}. هل توافق؟")
            print("تم إرسال الرسالة بنجاح.")
        except Exception as e:
            print(f"خطأ أثناء إرسال الرسالة: {e}")

        # الانتظار للحصول على الموافقة
        for _ in range(30):  # الانتظار لمدة 30 ثانية
            if self.approval_status is not None:
                break
            time.sleep(1)

        if self.approval_status:
            self.status_label.configure(text="تمت الموافقة. جارٍ فتح التطبيق...")
            time.sleep(1)
            self.destroy()
            app = PDFBuilderApp()
            app.mainloop()
        else:
            self.status_label.configure(text="تم رفض الدخول أو لم يتم الرد.")

    def set_approval_status(self, status):
        self.approval_status = status

    def open_social_link(self, url):
        """فتح رابط التواصل الاجتماعي في المتصفح"""
        try:
            import webbrowser
            webbrowser.open_new(url)
            # عرض رسالة نجاح
            if hasattr(self, 'status_label'):
                self.status_label.configure(text=f"تم فتح الرابط بنجاح")

            # إظهار رسالة تأكيد
            try:
                from customtkinter import CTkMessagebox
                CTkMessagebox(
                    title="فتح الرابط",
                    message="تم فتح الرابط في المتصفح",
                    icon="info",
                    option_1="موافق"
                )
            except:
                pass
        except Exception as e:
            # عرض رسالة الخطأ
            error_message = f"حدث خطأ أثناء فتح الرابط: {e}"
            if hasattr(self, 'status_label'):
                self.status_label.configure(text=error_message)

            # إظهار رسالة خطأ
            try:
                from customtkinter import CTkMessagebox
                CTkMessagebox(
                    title="خطأ",
                    message=error_message,
                    icon="cancel",
                    option_1="موافق"
                )
            except:
                pass

# تعديل في TelegramBotHandler للتعامل مع الموافقة
class TelegramBotHandler:
    def __init__(self, token, login_app):
        self.application = Application.builder().token(token).build()
        self.login_app = login_app

        # Add command handlers
        self.application.add_handler(CommandHandler("start", self.start))
        self.application.add_handler(CommandHandler("approve", self.approve))
        self.application.add_handler(CommandHandler("reject", self.reject))

    def start(self, update: Update, context):
        update.message.reply_text("مرحبًا! أنا بوت امتحانك بوك. استخدم /approve أو /reject للرد على طلبات الإذن.")

    def approve(self, update: Update, context):
        self.login_app.set_approval_status(True)
        update.message.reply_text("تمت الموافقة على الطلب.")

    def reject(self, update: Update, context):
        self.login_app.set_approval_status(False)
        update.message.reply_text("تم رفض الطلب.")

    def run(self):
        self.application.run_polling()

# Example usage
if __name__ == "__main__":
    # تشغيل واجهة تسجيل الدخول أولاً
    login_app = LoginApp()

    # تشغيل بوت Telegram في خيط منفصل
    bot_handler = TelegramBotHandler("**********************************************", login_app)  # Updated bot token
    threading.Thread(target=bot_handler.run, daemon=True).start()

    login_app.mainloop()
