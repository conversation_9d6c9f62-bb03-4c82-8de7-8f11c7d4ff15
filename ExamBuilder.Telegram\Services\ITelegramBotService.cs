using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Telegram.Bot;
using Telegram.Bot.Exceptions;
using Telegram.Bot.Polling;
using Telegram.Bot.Types;
using Telegram.Bot.Types.Enums;

namespace ExamBuilder.Telegram.Services
{
    /// <summary>
    /// واجهة خدمة Telegram Bot
    /// </summary>
    public interface ITelegramBotService
    {
        Task StartAsync(CancellationToken cancellationToken = default);
        Task StopAsync(CancellationToken cancellationToken = default);
        Task SendMessageAsync(string message, CancellationToken cancellationToken = default);
        Task SendMessageAsync(long chatId, string message, CancellationToken cancellationToken = default);
        Task<bool> IsConnectedAsync();
        event EventHandler<TelegramCommandEventArgs>? CommandReceived;
    }

    /// <summary>
    /// تنفيذ خدمة Telegram Bot
    /// </summary>
    public class TelegramBotService : ITelegramBotService, IDisposable
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<TelegramBotService> _logger;
        private readonly ITelegramBotClient _botClient;
        private readonly CancellationTokenSource _cancellationTokenSource;
        private readonly string _defaultChatId;
        private bool _isRunning;
        private bool _disposed;

        public event EventHandler<TelegramCommandEventArgs>? CommandReceived;

        public TelegramBotService(IConfiguration configuration, ILogger<TelegramBotService> logger)
        {
            _configuration = configuration;
            _logger = logger;
            _cancellationTokenSource = new CancellationTokenSource();

            var botToken = _configuration["Telegram:BotToken"];
            _defaultChatId = _configuration["Telegram:ChatId"] ?? "";

            if (string.IsNullOrEmpty(botToken))
            {
                throw new InvalidOperationException("Telegram Bot Token غير محدد في الإعدادات");
            }

            _botClient = new TelegramBotClient(botToken);
        }

        public async Task StartAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                if (_isRunning)
                {
                    _logger.LogWarning("Telegram Bot يعمل بالفعل");
                    return;
                }

                // التحقق من صحة البوت
                var me = await _botClient.GetMeAsync(cancellationToken);
                _logger.LogInformation($"تم بدء تشغيل Telegram Bot: {me.Username}");

                // إعداد معالج الرسائل
                var receiverOptions = new ReceiverOptions
                {
                    AllowedUpdates = Array.Empty<UpdateType>() // استقبال جميع أنواع التحديثات
                };

                _botClient.StartReceiving(
                    updateHandler: HandleUpdateAsync,
                    pollingErrorHandler: HandlePollingErrorAsync,
                    receiverOptions: receiverOptions,
                    cancellationToken: _cancellationTokenSource.Token
                );

                _isRunning = true;

                // إرسال رسالة بدء التشغيل
                await SendMessageAsync("🟢 تم بدء تشغيل برنامج إنشاء الامتحانات بنجاح", cancellationToken);

                _logger.LogInformation("تم بدء تشغيل Telegram Bot بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في بدء تشغيل Telegram Bot");
                throw;
            }
        }

        public async Task StopAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                if (!_isRunning)
                {
                    return;
                }

                _logger.LogInformation("جارٍ إيقاف Telegram Bot...");

                // إرسال رسالة إيقاف التشغيل
                await SendMessageAsync("🔴 تم إيقاف تشغيل برنامج إنشاء الامتحانات", cancellationToken);

                _cancellationTokenSource.Cancel();
                _isRunning = false;

                _logger.LogInformation("تم إيقاف Telegram Bot بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إيقاف Telegram Bot");
            }
        }

        public async Task SendMessageAsync(string message, CancellationToken cancellationToken = default)
        {
            if (long.TryParse(_defaultChatId, out var chatId))
            {
                await SendMessageAsync(chatId, message, cancellationToken);
            }
            else
            {
                _logger.LogWarning("Chat ID الافتراضي غير صحيح");
            }
        }

        public async Task SendMessageAsync(long chatId, string message, CancellationToken cancellationToken = default)
        {
            try
            {
                if (string.IsNullOrEmpty(message))
                {
                    return;
                }

                await _botClient.SendTextMessageAsync(
                    chatId: chatId,
                    text: message,
                    parseMode: ParseMode.Html,
                    cancellationToken: cancellationToken
                );

                _logger.LogDebug($"تم إرسال رسالة Telegram: {message[..Math.Min(50, message.Length)]}...");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"خطأ في إرسال رسالة Telegram إلى {chatId}");
            }
        }

        public async Task<bool> IsConnectedAsync()
        {
            try
            {
                await _botClient.GetMeAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        private async Task HandleUpdateAsync(ITelegramBotClient botClient, Update update, CancellationToken cancellationToken)
        {
            try
            {
                if (update.Message is not { } message)
                    return;

                if (message.Text is not { } messageText)
                    return;

                var chatId = message.Chat.Id;
                var userId = message.From?.Id ?? 0;
                var username = message.From?.Username ?? "Unknown";

                _logger.LogInformation($"تم استقبال رسالة من {username} ({userId}): {messageText}");

                // التحقق من صلاحية المستخدم
                if (!IsAuthorizedUser(chatId))
                {
                    await botClient.SendTextMessageAsync(
                        chatId: chatId,
                        text: "❌ غير مصرح لك باستخدام هذا البوت",
                        cancellationToken: cancellationToken
                    );
                    return;
                }

                // معالجة الأوامر
                await ProcessCommandAsync(botClient, message, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في معالجة تحديث Telegram");
            }
        }

        private async Task ProcessCommandAsync(ITelegramBotClient botClient, Message message, CancellationToken cancellationToken)
        {
            var messageText = message.Text?.Trim() ?? "";
            var chatId = message.Chat.Id;

            try
            {
                var commandArgs = new TelegramCommandEventArgs
                {
                    Command = messageText,
                    ChatId = chatId,
                    UserId = message.From?.Id ?? 0,
                    Username = message.From?.Username ?? "Unknown",
                    Message = message
                };

                // إثارة حدث استقبال الأمر
                CommandReceived?.Invoke(this, commandArgs);

                // معالجة الأوامر الأساسية
                switch (messageText.ToLower())
                {
                    case "/start":
                        await SendWelcomeMessage(botClient, chatId, cancellationToken);
                        break;

                    case "/help":
                    case "مساعدة":
                        await SendHelpMessage(botClient, chatId, cancellationToken);
                        break;

                    case "/status":
                    case "حالة":
                        await SendStatusMessage(botClient, chatId, cancellationToken);
                        break;

                    case "موافق":
                    case "approve":
                        commandArgs.Command = "approve_device";
                        CommandReceived?.Invoke(this, commandArgs);
                        break;

                    case "مرفوض":
                    case "reject":
                        commandArgs.Command = "reject_device";
                        CommandReceived?.Invoke(this, commandArgs);
                        break;

                    default:
                        if (messageText.StartsWith("/"))
                        {
                            await botClient.SendTextMessageAsync(
                                chatId: chatId,
                                text: "❌ أمر غير معروف. اكتب /help للحصول على قائمة الأوامر",
                                cancellationToken: cancellationToken
                            );
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"خطأ في معالجة الأمر: {messageText}");
                
                await botClient.SendTextMessageAsync(
                    chatId: chatId,
                    text: "❌ حدث خطأ في معالجة الأمر",
                    cancellationToken: cancellationToken
                );
            }
        }

        private async Task SendWelcomeMessage(ITelegramBotClient botClient, long chatId, CancellationToken cancellationToken)
        {
            var welcomeMessage = @"
🎓 <b>مرحباً بك في برنامج إنشاء الامتحانات</b>

👨‍💻 <b>المطور:</b> المهندس يوسف غنام

📋 <b>الأوامر المتاحة:</b>
• /status - عرض حالة النظام
• /help - عرض المساعدة
• موافق - الموافقة على جهاز جديد
• مرفوض - رفض جهاز جديد

🔧 يمكنك التحكم في البرنامج من خلال هذا البوت
";

            await botClient.SendTextMessageAsync(
                chatId: chatId,
                text: welcomeMessage,
                parseMode: ParseMode.Html,
                cancellationToken: cancellationToken
            );
        }

        private async Task SendHelpMessage(ITelegramBotClient botClient, long chatId, CancellationToken cancellationToken)
        {
            var helpMessage = @"
📖 <b>دليل الاستخدام</b>

🔹 <b>الأوامر الأساسية:</b>
• /start - رسالة الترحيب
• /status - عرض حالة النظام
• /help - عرض هذه المساعدة

🔹 <b>إدارة الأجهزة:</b>
• موافق - الموافقة على جهاز جديد
• مرفوض - رفض جهاز جديد

🔹 <b>معلومات إضافية:</b>
• سيتم إرسال إشعارات تلقائية عند محاولة أجهزة جديدة الوصول
• يمكنك الرد بـ 'موافق' أو 'مرفوض' على طلبات الأجهزة الجديدة

👨‍💻 <b>المطور:</b> المهندس يوسف غنام
";

            await botClient.SendTextMessageAsync(
                chatId: chatId,
                text: helpMessage,
                parseMode: ParseMode.Html,
                cancellationToken: cancellationToken
            );
        }

        private async Task SendStatusMessage(ITelegramBotClient botClient, long chatId, CancellationToken cancellationToken)
        {
            var statusMessage = $@"
📊 <b>حالة النظام</b>

🟢 <b>البرنامج:</b> يعمل بشكل طبيعي
🤖 <b>البوت:</b> متصل
⏰ <b>الوقت:</b> {DateTime.Now:yyyy-MM-dd HH:mm:ss}
💻 <b>الجهاز:</b> {Environment.MachineName}
👤 <b>المستخدم:</b> {Environment.UserName}

📈 <b>الذاكرة المستخدمة:</b> {GC.GetTotalMemory(false) / 1024 / 1024:F1} MB
";

            await botClient.SendTextMessageAsync(
                chatId: chatId,
                text: statusMessage,
                parseMode: ParseMode.Html,
                cancellationToken: cancellationToken
            );
        }

        private bool IsAuthorizedUser(long chatId)
        {
            // التحقق من أن المستخدم مصرح له
            return chatId.ToString() == _defaultChatId;
        }

        private Task HandlePollingErrorAsync(ITelegramBotClient botClient, Exception exception, CancellationToken cancellationToken)
        {
            var errorMessage = exception switch
            {
                ApiRequestException apiRequestException
                    => $"Telegram API Error:\n[{apiRequestException.ErrorCode}]\n{apiRequestException.Message}",
                _ => exception.ToString()
            };

            _logger.LogError(exception, "خطأ في Telegram Bot Polling: {ErrorMessage}", errorMessage);
            return Task.CompletedTask;
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _cancellationTokenSource?.Cancel();
                _cancellationTokenSource?.Dispose();
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// معطيات حدث أمر Telegram
    /// </summary>
    public class TelegramCommandEventArgs : EventArgs
    {
        public string Command { get; set; } = string.Empty;
        public long ChatId { get; set; }
        public long UserId { get; set; }
        public string Username { get; set; } = string.Empty;
        public Message? Message { get; set; }
    }
}
