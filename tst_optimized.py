import hashlib  # For hashing the MAC Address
import hmac  # For HMAC-based encryption
import os
import uuid as uuid_lib
import tkinter.filedialog as fd
import threading
import time
import requests
import json
import webbrowser
import tempfile
import random
from datetime import datetime
from tkinter import filedialog
from io import BytesIO
from reportlab.lib.utils import ImageReader
from reportlab.lib.pagesizes import letter
from reportlab.lib.colors import Color

import customtkinter as ctk
from CTkMessagebox import CTkMessagebox
from PIL import Image, ImageDraw, ImageFont
from tkinter import Canvas

# ==========================
# إعدادات الثيم واللون
# ==========================
ctk.set_appearance_mode("dark")  # Default theme for red theme
ctk.set_default_color_theme("dark-blue")

# إعدادات الألوان
COLORS = {
    # الثيم الأحمر - Red Color Palette
    "red_bg": "#0F0A1A",          # خلفية داكنة مائلة للأرجواني
    "red_card": "#622872",        # إطارات بلون أرجواني داكن
    "red_card_alt": "#4A1D59",    # إطارات بديلة بلون أرجواني أغمق
    "red_accent": "#caa5cb",      # لون مميز (أرجواني فاتح)
    "red_text": "#e8ddea",        # نص فاتح
    "red_text_muted": "#a58fa8",  # نص مخفف
    "red_button1": "#9C27B0",     # أزرار بلون أرجواني أكثر تباينًا
    "red_button2": "#BA68C8",     # أزرار بلون أرجواني فاتح أكثر تباينًا
    "red_button3": "#F50057",     # أزرار بلون وردي أكثر تباينًا للإجراءات المهمة
    "red_border": "#caa5cb",      # لون الحدود للإطارات الفاصلة

    # الثيم الأخضر - Green Color Palette
    "green_bg": "#f0f7e9",        # خلفية فاتحة مائلة للأخضر
    "green_card": "#e0ecd5",      # إطارات بلون أخضر فاتح
    "green_card_alt": "#c5e0b4",  # إطارات بديلة بلون أخضر فاتح
    "green_accent": "#4caf50",    # لون مميز (أخضر)
    "green_text": "#2e7d32",      # نص داكن (أخضر غامق)
    "green_text_muted": "#558b2f",# نص مخفف (أخضر متوسط)
    "green_button1": "#4caf50",   # أزرار بلون أخضر
    "green_button2": "#388e3c",   # أزرار بلون أخضر غامق
    "green_button3": "#1b5e20",   # أزرار بلون أخضر داكن للإجراءات المهمة
    "green_border": "#81c784",    # لون الحدود للإطارات الفاصلة

    # الوضع الداكن - ألوان متناسقة بدرجات الأزرق والأرجواني (للتوافق مع الكود القديم)
    "dark_bg": "#0A1931",         # خلفية داكنة مائلة للأزرق
    "dark_card": "#185ADB",       # إطارات بلون أزرق متوسط
    "dark_card_alt": "#0F52BA",   # إطارات بديلة بلون أزرق أغمق قليلاً
    "dark_accent": "#FFC947",     # لون مميز (أصفر ذهبي)
    "dark_text": "#FFFFFF",       # نص أبيض
    "dark_text_muted": "#DDDDDD", # نص أبيض مخفف
    "dark_button1": "#1E56A0",    # أزرار بلون أزرق
    "dark_button2": "#4F80E1",    # أزرار بلون أزرق فاتح
    "dark_button3": "#D72323",    # أزرار بلون أحمر للإجراءات المهمة
    "dark_border": "#FFC947",     # لون الحدود للإطارات الفاصلة

    # الوضع الفاتح - ألوان متناسقة بدرجات الأزرق والرمادي (للتوافق مع الكود القديم)
    "light_bg": "#F6F8FA",        # خلفية فاتحة مائلة للرمادي
    "light_card": "#FFFFFF",      # إطارات بيضاء
    "light_card_alt": "#F0F0F0",  # إطارات بديلة بلون رمادي فاتح
    "light_accent": "#1E56A0",    # لون مميز (أزرق)
    "light_text": "#333333",      # نص أسود مخفف
    "light_text_muted": "#666666",# نص رمادي
    "light_button1": "#4F80E1",   # أزرار بلون أزرق فاتح
    "light_button2": "#1E56A0",   # أزرار بلون أزرق غامق
    "light_button3": "#D72323",   # أزرار بلون أحمر للإجراءات المهمة
    "light_border": "#1E56A0"     # لون الحدود للإطارات الفاصلة
}

# Function to toggle between light and dark themes
def toggle_theme():
    current_theme = ctk.get_appearance_mode()
    if current_theme == "dark":
        ctk.set_appearance_mode("light")
    else:
        ctk.set_appearance_mode("dark")

# ==========================
# تطبيق بناء PDF  صلي
# ==========================

# Security keys
SECRET_KEY = "my_super_secret_key"  # المفتاح السري (يجب أن يكون محفوظًا لديك فقط)
PASSWORD_SALT = "custom_salt_for_password_hashing"  # ملح لتشفير كلمات المرور

# Telegram Bot settings
BOT_TOKEN = "**********************************************"  # توكن بوت التلجرام
CHAT_ID = "1486393122"  # معرف المحادثة في التلجرام

# نظام تسجيل الأجهزة النشطة
ACTIVE_DEVICES_FILE = "active_devices.json"  # ملف لتخزين معلومات الأجهزة النشطة
COMMAND_POLLING_INTERVAL = 5  # الفاصل الزمني بالثواني للتحقق من الأوامر الجديدة
LAST_UPDATE_ID = None  # آخر معرف تحديث تم معالجته

# Telegram Bot functions
def send_telegram_message(message):
    """إرسال رسالة إلى بوت التلجرام"""
    url = f"https://api.telegram.org/bot{BOT_TOKEN}/sendMessage"
    payload = {"chat_id": CHAT_ID, "text": message}
    try:
        response = requests.post(url, json=payload)
        return response.ok
    except Exception as e:
        print(f"خطأ في إرسال الرسالة: {e}")
        return False

def get_telegram_updates(offset=None):
    """الحصول على التحديثات من بوت التلجرام"""
    url = f"https://api.telegram.org/bot{BOT_TOKEN}/getUpdates"
    params = {"timeout": 30}
    if offset:
        params["offset"] = offset

    try:
        response = requests.get(url, params=params)
        if response.ok:
            return response.json().get("result", [])
        return []
    except Exception as e:
        print(f"خطأ في الحصول على التحديثات: {e}")
        return []

def wait_for_telegram_approval(timeout=120):
    """انتظار الموافقة من بوت التلجرام"""
    global LAST_UPDATE_ID
    url = f"https://api.telegram.org/bot{BOT_TOKEN}/getUpdates"
    start_time = time.time()

    # إرسال رسالة للتأكد من أن البوت يعمل
    test_message = f"طلب تحقق جديد: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\nللموافقة اكتب 'موافق'، للرفض اكتب 'مرفوض'"
    if not send_telegram_message(test_message):
        return False

    # الحصول على آخر معرف تحديث قبل إرسال الطلب
    try:
        response = requests.get(url)
        if response.ok:
            updates = response.json().get("result", [])
            if updates:
                LAST_UPDATE_ID = updates[-1]["update_id"]
    except Exception as e:
        print(f"خطأ في الحصول على آخر معرف تحديث: {e}")

    # انتظار الرد
    while time.time() - start_time < timeout:
        try:
            params = {}
            if LAST_UPDATE_ID:
                params["offset"] = LAST_UPDATE_ID + 1

            response = requests.get(url, params=params)
            if response.ok:
                updates = response.json().get("result", [])
                for update in updates:
                    update_id = update["update_id"]
                    LAST_UPDATE_ID = update_id
                    message = update.get("message", {}).get("text", "")
                    if message and message.strip().lower() == "موافق":
                        return True
                    elif message and message.strip().lower() == "مرفوض":
                        return False
        except Exception as e:
            print(f"خطأ في الاتصال ببوت التلجرام: {e}")
        time.sleep(2)  # انتظار قبل المحاولة مرة أخرى

    # لم يتم الرد خلال المهلة المحددة - نعتبر أنه لم يتم الرفض
    return None  # انتهت المهلة دون رد

def register_active_device():
    """تسجيل الجهاز الحالي كجهاز نشط"""
    device_id = get_device_id()
    device_info = {
        "device_id": device_id,
        "last_active": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "hostname": os.environ.get("COMPUTERNAME", "Unknown"),
        "status": "active"
    }

    # تحميل قائمة الأجهزة النشطة
    active_devices = {}
    if os.path.exists(ACTIVE_DEVICES_FILE):
        try:
            with open(ACTIVE_DEVICES_FILE, "r") as file:
                active_devices = json.loads(file.read())
        except:
            active_devices = {}

    # تحديث معلومات الجهاز الحالي
    active_devices[device_id] = device_info

    # حفظ قائمة الأجهزة النشطة
    try:
        with open(ACTIVE_DEVICES_FILE, "w") as file:
            file.write(json.dumps(active_devices, indent=4))
    except Exception as e:
        print(f"خطأ في حفظ معلومات الجهاز النشط: {e}")

    # إرسال إشعار بتسجيل الجهاز
    message = f"تم تسجيل جهاز نشط جديد:\nID: {device_id}\nHostname: {device_info['hostname']}\nTime: {device_info['last_active']}"
    threading.Thread(target=send_telegram_message, args=(message,), daemon=True).start()

    return device_info

def update_device_status(status="active"):
    """تحديث حالة الجهاز الحالي"""
    device_id = get_device_id()

    # تحميل قائمة الأجهزة النشطة
    if os.path.exists(ACTIVE_DEVICES_FILE):
        try:
            with open(ACTIVE_DEVICES_FILE, "r") as file:
                active_devices = json.loads(file.read())

            if device_id in active_devices:
                active_devices[device_id]["last_active"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                active_devices[device_id]["status"] = status

                # حفظ قائمة الأجهزة النشطة
                with open(ACTIVE_DEVICES_FILE, "w") as file:
                    file.write(json.dumps(active_devices, indent=4))
        except Exception as e:
            print(f"خطأ في تحديث حالة الجهاز: {e}")

def get_active_devices():
    """الحصول على قائمة الأجهزة النشطة"""
    if os.path.exists(ACTIVE_DEVICES_FILE):
        try:
            with open(ACTIVE_DEVICES_FILE, "r") as file:
                return json.loads(file.read())
        except:
            return {}
    return {}

# نظام الاستماع للأوامر من بوت التلجرام
class TelegramCommandListener:
    """فئة للاستماع للأوامر من بوت التلجرام"""
    def __init__(self, app_instance=None):
        self.app_instance = app_instance
        self.running = False
        self.listener_thread = None
        self.last_update_id = None

    def start(self):
        """بدء الاستماع للأوامر"""
        if not self.running:
            self.running = True
            self.listener_thread = threading.Thread(target=self._listen_for_commands, daemon=True)
            self.listener_thread.start()
            print("تم بدء الاستماع للأوامر من بوت التلجرام")

    def stop(self):
        """إيقاف الاستماع للأوامر"""
        self.running = False
        if self.listener_thread and self.listener_thread.is_alive():
            self.listener_thread.join(timeout=1)
            print("تم إيقاف الاستماع للأوامر من بوت التلجرام")

    def _listen_for_commands(self):
        """الاستماع للأوامر من بوت التلجرام"""
        global LAST_UPDATE_ID

        while self.running:
            try:
                # الحصول على التحديثات الجديدة
                updates = get_telegram_updates(offset=LAST_UPDATE_ID)

                for update in updates:
                    # تحديث آخر معرف تحديث تم معالجته
                    update_id = update["update_id"]
                    LAST_UPDATE_ID = update_id + 1

                    # التحقق من وجود رسالة
                    if "message" in update and "text" in update["message"]:
                        message_text = update["message"]["text"]
                        self._process_command(message_text)

            except Exception as e:
                print(f"خطأ في الاستماع للأوامر: {e}")

            # انتظار قبل التحقق من الأوامر الجديدة
            time.sleep(COMMAND_POLLING_INTERVAL)

    def _process_command(self, command_text):
        """معالجة الأوامر الواردة"""
        # أمر إغلاق التطبيق
        if command_text.startswith("/close"):
            parts = command_text.split()
            if len(parts) > 1:
                target_device = parts[1]
                self._close_app(target_device)
            else:
                self._close_all_apps()

        # أمر حظر جهاز
        elif command_text.startswith("/block"):
            parts = command_text.split()
            if len(parts) > 1:
                target_device = parts[1]
                self._block_device(target_device)

        # أمر إلغاء حظر جهاز
        elif command_text.startswith("/unblock"):
            parts = command_text.split()
            if len(parts) > 1:
                target_device = parts[1]
                self._unblock_device(target_device)

        # أمر عرض الأجهزة النشطة
        elif command_text == "/devices":
            self._list_active_devices()

        # أمر المساعدة
        elif command_text == "/help":
            self._send_help_message()

    def _close_app(self, target_device):
        """إغلاق التطبيق على جهاز محدد"""
        current_device = get_device_id()

        # إذا كان الجهاز المستهدف هو الجهاز الحالي
        if target_device == current_device or target_device == "current":
            send_telegram_message(f"جاري إغلاق التطبيق على الجهاز الحالي: {current_device}")
            if self.app_instance:
                # تحديث حالة الجهاز قبل الإغلاق
                update_device_status("closed")

                # إغلاق التطبيق بشكل فوري
                try:
                    # عرض رسالة للمستخدم
                    try:
                        ctk.CTkMessagebox(
                            title="إغلاق التطبيق",
                            message="تم طلب إغلاق التطبيق عن بُعد.",
                            icon="info",
                            option_1="موافق"
                        )
                    except:
                        pass

                    # إغلاق التطبيق
                    self.app_instance.quit()
                    self.app_instance.destroy()
                except:
                    import sys
                    sys.exit(0)
            return True
        else:
            # تحديث حالة الجهاز المستهدف
            active_devices = get_active_devices()
            if target_device in active_devices:
                active_devices[target_device]["status"] = "close_requested"
                with open(ACTIVE_DEVICES_FILE, "w") as file:
                    file.write(json.dumps(active_devices, indent=4))
                send_telegram_message(f"تم إرسال طلب إغلاق التطبيق إلى الجهاز: {target_device}")
                return True
            else:
                # إذا كان الجهاز غير نشط حاليًا، نضيفه إلى قائمة الأجهزة النشطة مع حالة close_requested
                device_info = {
                    "device_id": target_device,
                    "last_active": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "hostname": "Unknown",
                    "status": "close_requested"
                }
                active_devices[target_device] = device_info
                with open(ACTIVE_DEVICES_FILE, "w") as file:
                    file.write(json.dumps(active_devices, indent=4))
                send_telegram_message(f"تم تحديث حالة الجهاز: {target_device} إلى close_requested")
                return True

    def _close_all_apps(self):
        """إغلاق التطبيق على جميع الأجهزة"""
        # تحديث حالة جميع الأجهزة
        active_devices = get_active_devices()
        for device_id in active_devices:
            active_devices[device_id]["status"] = "close_requested"

        # حفظ التغييرات
        with open(ACTIVE_DEVICES_FILE, "w") as file:
            file.write(json.dumps(active_devices, indent=4))

        send_telegram_message("تم إرسال طلب إغلاق التطبيق إلى جميع الأجهزة")

        # إغلاق التطبيق على الجهاز الحالي
        if self.app_instance:
            # تحديث حالة الجهاز قبل الإغلاق
            update_device_status("closed")

            # إغلاق التطبيق بشكل فوري
            try:
                # عرض رسالة للمستخدم
                try:
                    ctk.CTkMessagebox(
                        title="إغلاق التطبيق",
                        message="تم طلب إغلاق التطبيق عن بُعد.",
                        icon="info",
                        option_1="موافق"
                    )
                except:
                    pass

                # إغلاق التطبيق
                self.app_instance.quit()
                self.app_instance.destroy()
            except:
                import sys
                sys.exit(0)

    def _block_device(self, target_device):
        """حظر جهاز محدد"""
        # تحديث ملف الأجهزة المصرح بها
        auth_file = "authorized_devices.txt"
        blocked_file = "blocked_devices.txt"
        approval_file = "approval.txt"

        # التحقق من وجود الجهاز في قائمة الأجهزة المصرح بها
        if os.path.exists(auth_file):
            with open(auth_file, "r") as file:
                authorized_ids = file.read().splitlines()

            # إزالة الجهاز من قائمة الأجهزة المصرح بها
            if target_device in authorized_ids:
                authorized_ids.remove(target_device)
                with open(auth_file, "w") as file:
                    file.write("\n".join(authorized_ids))

        # إضافة الجهاز إلى قائمة الأجهزة المحظورة
        if os.path.exists(blocked_file):
            with open(blocked_file, "r") as file:
                blocked_ids = file.read().splitlines()
        else:
            blocked_ids = []

        if target_device not in blocked_ids:
            blocked_ids.append(target_device)
            with open(blocked_file, "w") as file:
                file.write("\n".join(blocked_ids))

        # حذف ملف الموافقة إذا كان موجودًا
        if os.path.exists(approval_file):
            try:
                os.remove(approval_file)
            except:
                pass

        # تحديث حالة الجهاز في ملف الأجهزة النشطة
        active_devices = get_active_devices()
        if target_device in active_devices:
            active_devices[target_device]["status"] = "blocked"
            with open(ACTIVE_DEVICES_FILE, "w") as file:
                file.write(json.dumps(active_devices, indent=4))
        else:
            # إذا كان الجهاز غير نشط حاليًا، نضيفه إلى قائمة الأجهزة النشطة مع حالة blocked
            device_info = {
                "device_id": target_device,
                "last_active": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "hostname": "Unknown",
                "status": "blocked"
            }
            active_devices[target_device] = device_info
            with open(ACTIVE_DEVICES_FILE, "w") as file:
                file.write(json.dumps(active_devices, indent=4))

        send_telegram_message(f"تم حظر الجهاز: {target_device}")

        # إغلاق التطبيق على الجهاز المحظور بشكل فوري
        self._close_app(target_device)

    def _unblock_device(self, target_device):
        """إلغاء حظر جهاز محدد"""
        # تحديث ملف الأجهزة المحظورة
        blocked_file = "blocked_devices.txt"
        auth_file = "authorized_devices.txt"
        approval_file = "approval.txt"

        # التحقق من وجود الجهاز في قائمة الأجهزة المحظورة
        device_was_blocked = False
        if os.path.exists(blocked_file):
            with open(blocked_file, "r") as file:
                blocked_ids = file.read().splitlines()

            # إزالة الجهاز من قائمة الأجهزة المحظورة
            if target_device in blocked_ids:
                device_was_blocked = True
                blocked_ids.remove(target_device)
                with open(blocked_file, "w") as file:
                    file.write("\n".join(blocked_ids))

        # إضافة الجهاز إلى قائمة الأجهزة المصرح بها
        if os.path.exists(auth_file):
            with open(auth_file, "r") as file:
                authorized_ids = file.read().splitlines()

            if target_device not in authorized_ids:
                authorized_ids.append(target_device)
                with open(auth_file, "w") as file:
                    file.write("\n".join(authorized_ids))
        else:
            with open(auth_file, "w") as file:
                file.write(target_device)

        # إنشاء ملف الموافقة للجهاز
        with open(approval_file, "w") as file:
            file.write("approved")

        # تحديث حالة الجهاز في ملف الأجهزة النشطة
        active_devices = get_active_devices()
        if target_device in active_devices:
            active_devices[target_device]["status"] = "active"
            with open(ACTIVE_DEVICES_FILE, "w") as file:
                file.write(json.dumps(active_devices, indent=4))
        else:
            # إذا كان الجهاز غير نشط حاليًا، نضيفه إلى قائمة الأجهزة النشطة مع حالة active
            device_info = {
                "device_id": target_device,
                "last_active": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "hostname": "Unknown",
                "status": "active"
            }
            active_devices[target_device] = device_info
            with open(ACTIVE_DEVICES_FILE, "w") as file:
                file.write(json.dumps(active_devices, indent=4))

        if device_was_blocked:
            send_telegram_message(f"تم إلغاء حظر الجهاز: {target_device}")
        else:
            send_telegram_message(f"تم تفعيل الجهاز: {target_device}")

    def _list_active_devices(self):
        """عرض قائمة الأجهزة النشطة"""
        active_devices = get_active_devices()

        if not active_devices:
            send_telegram_message("لا توجد أجهزة نشطة حاليًا.")
            return

        message = "الأجهزة النشطة:\n\n"
        for device_id, info in active_devices.items():
            message += f"ID: {device_id}\n"
            message += f"Hostname: {info.get('hostname', 'Unknown')}\n"
            message += f"Last Active: {info.get('last_active', 'Unknown')}\n"
            message += f"Status: {info.get('status', 'Unknown')}\n"
            message += "-" * 30 + "\n"

        send_telegram_message(message)

    def _send_help_message(self):
        """إرسال رسالة المساعدة"""
        help_message = """أوامر التحكم المتاحة:

/close [device_id] - إغلاق التطبيق على جهاز محدد
/close - إغلاق التطبيق على جميع الأجهزة
/block [device_id] - حظر جهاز محدد
/unblock [device_id] - إلغاء حظر جهاز محدد
/devices - عرض قائمة الأجهزة النشطة
/help - عرض هذه الرسالة
"""
        send_telegram_message(help_message)

    def check_device_status(self):
        """التحقق من حالة الجهاز الحالي"""
        device_id = get_device_id()
        active_devices = get_active_devices()

        # التحقق من وجود الجهاز في قائمة الأجهزة المحظورة
        blocked_file = "blocked_devices.txt"
        if os.path.exists(blocked_file):
            with open(blocked_file, "r") as file:
                blocked_ids = file.read().splitlines()
                if device_id in blocked_ids:
                    # إذا كان الجهاز محظورًا، أغلق التطبيق فورًا
                    if self.app_instance:
                        # تحديث حالة الجهاز قبل الإغلاق
                        update_device_status("blocked")
                        # عرض رسالة للمستخدم
                        try:
                            ctk.CTkMessagebox(
                                title="جهاز محظور",
                                message="هذا الجهاز محظور من استخدام التطبيق.\nيرجى التواصل مع المسؤول للحصول على مساعدة.",
                                icon="cancel",
                                option_1="إغلاق"
                            )
                        except:
                            pass

                        # إغلاق التطبيق بشكل فوري
                        try:
                            self.app_instance.quit()
                            self.app_instance.destroy()
                        except:
                            import sys
                            sys.exit(0)

                        return False

        if device_id in active_devices:
            status = active_devices[device_id].get("status", "active")

            # إذا تم طلب إغلاق التطبيق
            if status == "close_requested" and self.app_instance:
                # تحديث حالة الجهاز قبل الإغلاق
                update_device_status("closed")
                # عرض رسالة للمستخدم
                try:
                    ctk.CTkMessagebox(
                        title="إغلاق التطبيق",
                        message="تم طلب إغلاق التطبيق عن بُعد.",
                        icon="info",
                        option_1="موافق"
                    )
                except:
                    pass

                # إغلاق التطبيق بشكل فوري
                try:
                    self.app_instance.quit()
                    self.app_instance.destroy()
                except:
                    import sys
                    sys.exit(0)

                return False

            # إذا كان الجهاز محظورًا
            elif status == "blocked" and self.app_instance:
                # عرض رسالة للمستخدم
                try:
                    ctk.CTkMessagebox(
                        title="جهاز محظور",
                        message="هذا الجهاز محظور من استخدام التطبيق.\nيرجى التواصل مع المسؤول للحصول على مساعدة.",
                        icon="cancel",
                        option_1="إغلاق"
                    )
                except:
                    pass

                # إغلاق التطبيق بشكل فوري
                try:
                    self.app_instance.quit()
                    self.app_instance.destroy()
                except:
                    import sys
                    sys.exit(0)

                return False

        # تحديث حالة الجهاز
        update_device_status("active")
        return True


class PDFBuilderApp(ctk.CTk):
    def __init__(self):
        super().__init__()
        self.title(" ghannam_exam")
        self.geometry("1300x900")
        self.minsize(800, 600)
        # تعيين الثيم الأحمر كثيم افتراضي
        self.configure(fg_color=COLORS["red_bg"])

        self.question_images = []
        self.frame_image_path = None
        self.logo_image_path = None
        self.last_pdf_path = None

        # مسار ملف الإعدادات
        self.settings_file = os.path.join(os.path.expanduser("~"), "pdf_builder_settings.json")

        # تسجيل الجهاز كجهاز نشط
        self.device_info = register_active_device()

        # إنشاء مستمع الأوامر من بوت التلجرام
        self.command_listener = TelegramCommandListener(app_instance=self)

        # تفعيل دعم السحب والإفلات
        try:
            # محاولة تفعيل دعم السحب والإفلات
            self.drop_target_register("*")
            self.dnd_bind("<<Drop>>", self.handle_drop)
        except:
            # إذا لم يكن دعم السحب والإفلات متاحًا
            print("دعم السحب والإفلات غير متاح في هذه النسخة من Python")

        self.create_widgets()
        self.grid_config()

        # استعادة الإعدادات المحفوظة (إن وجدت)
        self.load_settings()

        # تطبيق الثيم الأحمر عند بدء التشغيل (إذا لم يتم تحميل ثيم من الإعدادات)
        if not hasattr(self, 'current_theme'):
            self._apply_theme("red")

        # تعيين وظيفة الإغلاق عند الضغط على زر X
        self.protocol("WM_DELETE_WINDOW", self.on_closing)

        # تعطيل زر إزالة اللوجو عند بدء التشغيل
        if hasattr(self, 'remove_logo_button'):
            self.remove_logo_button.configure(state="disabled")

        # اختبار الاتصال ببوت التلجرام عند بدء التشغيل (بعد إنشاء الواجهة)
        self.after(1000, self.check_telegram_connection)

        # بدء الاستماع للأوامر من بوت التلجرام
        self.command_listener.start()

        # التحقق من حالة الجهاز بشكل دوري
        self.after(5000, self.check_device_status)

        # تسجيل وظيفة لتنفيذها عند إغلاق التطبيق
        self.protocol("WM_DELETE_WINDOW", self.on_closing)

    def create_widgets(self):
        # Header with theme toggle
        header_frame = ctk.CTkFrame(self, fg_color=COLORS["dark_card"], corner_radius=15)
        header_frame.grid(row=0, column=0, columnspan=2, padx=20, pady=(20, 10), sticky="nsew")

        ctk.CTkLabel(
            header_frame, text="النبي علي صلي ", font=ctk.CTkFont(size=32, weight="bold"),
            text_color=COLORS["dark_accent"], anchor="center"
        ).pack(side="left", expand=True, fill="both", padx=(20, 0), pady=10)

        # Theme toggle button
        self.theme_button = ctk.CTkButton(
            header_frame, text="(red) تغيير الثيم", command=self.toggle_app_theme,
            font=ctk.CTkFont(size=14), fg_color=COLORS["red_button1"], hover_color=COLORS["red_button2"],
            text_color=COLORS["red_text"], corner_radius=10, width=150, height=35
        )
        self.theme_button.pack(side="right", padx=(0, 20), pady=10)

        # إنشاء إطار التبويب الرئيسي
        self.tabview = ctk.CTkTabview(self, fg_color=COLORS["dark_card"], corner_radius=15)
        self.tabview.grid(row=1, column=0, columnspan=2, padx=20, pady=10, sticky="nsew")

        # إضافة التبويبات
        self.tab_main = self.tabview.add("الرئيسية")
        self.tab_cover = self.tabview.add("غلاف الامتحان")
        self.tab_customize = self.tabview.add("تخصيص الأسئلة")
        self.tab_settings = self.tabview.add("الإعدادات")

        # تكوين التبويبات
        for tab in [self.tab_main, self.tab_cover, self.tab_customize, self.tab_settings]:
            tab.grid_columnconfigure(0, weight=1)
            tab.grid_columnconfigure(1, weight=1)
            tab.grid_rowconfigure(0, weight=1)
            tab.grid_rowconfigure(1, weight=1)

        # تعيين التبويب الافتراضي
        self.tabview.set("الرئيسية")

        # إضافة محتوى التبويبات
        self.create_main_tab()
        self.create_cover_tab()
        self.create_customize_tab()
        self.create_settings_tab()

        # Status and Progress Bar
        status_frame = ctk.CTkFrame(self, fg_color=COLORS["dark_card"], corner_radius=15,
                                   border_width=2, border_color=COLORS["dark_border"])
        status_frame.grid(row=2, column=0, columnspan=2, padx=20, pady=(10, 20), sticky="nsew")

        # Status label
        self.status = ctk.CTkLabel(
            status_frame, text="جاهز", font=ctk.CTkFont(size=14),
            text_color=COLORS["dark_text"], anchor="w"
        )
        self.status.pack(side="top", anchor="w", padx=20, pady=(10, 5), fill="x")

        # Progress bar
        self.progress_bar = ctk.CTkProgressBar(status_frame, height=15)
        self.progress_bar.pack(side="bottom", fill="x", padx=20, pady=(5, 10))
        self.progress_bar.set(0)

    def create_main_tab(self):
        """إنشاء محتوى تبويب الرئيسية"""
        # إطار اختيار الصور
        images_frame = ctk.CTkFrame(self.tab_main, fg_color=COLORS["dark_card"], corner_radius=15,
                                   border_width=2, border_color=COLORS["dark_border"])
        images_frame.grid(row=0, column=0, padx=10, pady=10, sticky="nsew")

        # عنوان القسم
        title_frame = ctk.CTkFrame(images_frame, fg_color=COLORS["dark_card_alt"], corner_radius=8)
        title_frame.pack(fill="x", padx=10, pady=10)

        ctk.CTkLabel(
            title_frame, text="اختيار الصور",
            font=ctk.CTkFont(size=18, weight="bold"), text_color=COLORS["dark_text"]
        ).pack(pady=8)

        # إطار الأزرار
        buttons_frame = ctk.CTkFrame(images_frame, fg_color="transparent")
        buttons_frame.pack(fill="both", expand=True, padx=15, pady=10)

        # إطار السحب والإفلات
        drop_frame = ctk.CTkFrame(buttons_frame, fg_color=COLORS["dark_card_alt"], corner_radius=8)
        drop_frame.pack(fill="x", padx=10, pady=10)

        # تكوين إطار السحب والإفلات
        drop_label = ctk.CTkLabel(
            drop_frame, text="اسحب وأفلت صور الأسئلة هنا",
            font=ctk.CTkFont(size=16), text_color=COLORS["dark_text"]
        )
        drop_label.pack(pady=20)

        # تكوين وظائف السحب والإفلات
        drop_frame.bind("<Enter>", lambda e: drop_frame.configure(fg_color=COLORS["dark_accent"]))
        drop_frame.bind("<Leave>", lambda e: drop_frame.configure(fg_color=COLORS["dark_card_alt"]))
        drop_frame.bind("<ButtonRelease-1>", lambda e: self.select_questions())

        # تكوين وظائف السحب والإفلات للنص
        drop_label.bind("<Enter>", lambda e: drop_frame.configure(fg_color=COLORS["dark_accent"]))
        drop_label.bind("<Leave>", lambda e: drop_frame.configure(fg_color=COLORS["dark_card_alt"]))
        drop_label.bind("<ButtonRelease-1>", lambda e: self.select_questions())

        # زر اختيار صور الأسئلة
        ctk.CTkButton(
            buttons_frame, text="اختيار صور الأسئلة", command=self.select_questions,
            font=ctk.CTkFont(size=14), fg_color=COLORS["dark_button1"], hover_color=COLORS["dark_button2"],
            text_color="#FFFFFF", corner_radius=8, height=40, border_width=1,
            border_color=COLORS["dark_button2"]
        ).pack(fill="x", padx=10, pady=10)

        # زر اختيار صورة الفريم
        ctk.CTkButton(
            buttons_frame, text="اختيار صورة الفريم", command=self.select_frame,
            font=ctk.CTkFont(size=14), fg_color=COLORS["dark_button1"], hover_color=COLORS["dark_button2"],
            text_color="#FFFFFF", corner_radius=8, height=40, border_width=1,
            border_color=COLORS["dark_button2"]
        ).pack(fill="x", padx=10, pady=10)

        # إطار اللوجو
        logo_frame = ctk.CTkFrame(buttons_frame, fg_color=COLORS["dark_card_alt"], corner_radius=5)
        logo_frame.pack(fill="x", pady=10)

        ctk.CTkLabel(
            logo_frame, text=":إضافة لوجو",
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"]
        ).pack(side="right", padx=(0, 10), pady=8)

        logo_buttons_frame = ctk.CTkFrame(logo_frame, fg_color="transparent")
        logo_buttons_frame.pack(side="left", fill="x", expand=True, padx=10, pady=8)

        ctk.CTkButton(
            logo_buttons_frame, text="اختيار اللوجو", command=self.select_logo,
            font=ctk.CTkFont(size=14), fg_color=COLORS["dark_button1"], hover_color=COLORS["dark_button2"],
            text_color="#FFFFFF", corner_radius=8, height=30, border_width=1,
            border_color=COLORS["dark_button2"]
        ).pack(side="right", padx=5, pady=0, fill="x", expand=True)

        self.remove_logo_button = ctk.CTkButton(
            logo_buttons_frame, text="إزالة اللوجو", command=self.remove_logo,
            font=ctk.CTkFont(size=14), fg_color=COLORS["dark_button3"], hover_color="#FF2E63",
            text_color="#FFFFFF", corner_radius=8, height=30, border_width=1,
            border_color="#FF2E63", state="disabled"
        )
        self.remove_logo_button.pack(side="left", padx=5, pady=0, fill="x", expand=True)

        # إطار الإعدادات
        settings_frame = ctk.CTkFrame(self.tab_main, fg_color=COLORS["dark_card"], corner_radius=15,
                                     border_width=2, border_color=COLORS["dark_border"])
        settings_frame.grid(row=0, column=1, padx=10, pady=10, sticky="nsew")

        # عنوان القسم
        title_frame = ctk.CTkFrame(settings_frame, fg_color=COLORS["dark_card_alt"], corner_radius=8)
        title_frame.pack(fill="x", padx=10, pady=10)

        ctk.CTkLabel(
            title_frame, text="الإعدادات",
            font=ctk.CTkFont(size=18, weight="bold"), text_color=COLORS["dark_text"]
        ).pack(pady=8)

        # إطار الإعدادات
        options_frame = ctk.CTkFrame(settings_frame, fg_color="transparent")
        options_frame.pack(fill="both", expand=True, padx=15, pady=10)

        # عدد الأسئلة في الصفحة
        per_page_frame = ctk.CTkFrame(options_frame, fg_color=COLORS["dark_card_alt"], corner_radius=5)
        per_page_frame.pack(fill="x", pady=10)

        ctk.CTkLabel(
            per_page_frame, text=":عدد الأسئلة في الصفحة",
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"]
        ).pack(side="right", padx=(0, 10), pady=8)

        self.num_per_page_entry = ctk.CTkEntry(
            per_page_frame, width=80, justify="center",
            font=ctk.CTkFont(size=14), fg_color=COLORS["dark_bg"],
            text_color=COLORS["dark_text"], border_color=COLORS["dark_accent"], border_width=1
        )
        self.num_per_page_entry.insert(0, "5")
        self.num_per_page_entry.pack(side="left", padx=10, pady=8)

        # ترقيم الأسئلة
        numbering_frame = ctk.CTkFrame(options_frame, fg_color=COLORS["dark_card_alt"], corner_radius=5)
        numbering_frame.pack(fill="x", pady=10)

        ctk.CTkLabel(
            numbering_frame, text=":ترقيم الأسئلة",
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"]
        ).pack(side="right", padx=(0, 10), pady=8)

        self.numbering_var = ctk.BooleanVar(value=True)
        ctk.CTkCheckBox(
            numbering_frame, text="تفعيل", variable=self.numbering_var,
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"],
            fg_color=COLORS["dark_button3"], hover_color="#FF2E63",
            checkbox_height=22, checkbox_width=22
        ).pack(side="left", padx=10, pady=8)

        # أزرار المعاينة والإنشاء
        buttons_frame = ctk.CTkFrame(options_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", pady=10)

        ctk.CTkButton(
            buttons_frame, text="معاينة PDF", command=self.preview_pdf,
            font=ctk.CTkFont(size=14), fg_color=COLORS["dark_button1"], hover_color=COLORS["dark_button2"],
            text_color="#FFFFFF", corner_radius=8, height=30, border_width=1,
            border_color=COLORS["dark_button2"]
        ).pack(side="right", padx=5, pady=0, fill="x", expand=True)

        ctk.CTkButton(
            buttons_frame, text="إنشاء PDF", command=self.generate_pdf,
            font=ctk.CTkFont(size=14), fg_color=COLORS["dark_button3"], hover_color="#FF2E63",
            text_color="#FFFFFF", corner_radius=8, height=30, border_width=1,
            border_color="#FF2E63"
        ).pack(side="left", padx=5, pady=0, fill="x", expand=True)

    def create_cover_tab(self):
        """إنشاء محتوى تبويب غلاف الامتحان"""
        # إطار غلاف الامتحان
        cover_frame = ctk.CTkFrame(self.tab_cover, fg_color=COLORS["dark_card"], corner_radius=15,
                                  border_width=2, border_color=COLORS["dark_border"])
        cover_frame.grid(row=0, column=0, columnspan=2, padx=10, pady=10, sticky="nsew")

        # عنوان القسم
        title_frame = ctk.CTkFrame(cover_frame, fg_color=COLORS["dark_card_alt"], corner_radius=8)
        title_frame.pack(fill="x", padx=10, pady=10)

        ctk.CTkLabel(
            title_frame, text="غلاف الامتحان (البابل شيت)",
            font=ctk.CTkFont(size=18, weight="bold"), text_color=COLORS["dark_text"]
        ).pack(pady=8)

        # إطار الإعدادات
        bubble_settings_frame = ctk.CTkFrame(cover_frame, fg_color="transparent")
        bubble_settings_frame.pack(fill="both", expand=True, padx=15, pady=10)

        # تفعيل غلاف الامتحان
        enable_cover_frame = ctk.CTkFrame(bubble_settings_frame, fg_color=COLORS["dark_card_alt"], corner_radius=5)
        enable_cover_frame.pack(fill="x", pady=10)

        ctk.CTkLabel(
            enable_cover_frame, text=":تفعيل غلاف الامتحان",
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"]
        ).pack(side="right", padx=(0, 10), pady=8)

        self.enable_cover_var = ctk.BooleanVar(value=False)
        ctk.CTkCheckBox(
            enable_cover_frame, text="تفعيل", variable=self.enable_cover_var,
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"],
            fg_color=COLORS["dark_button3"], hover_color="#FF2E63",
            checkbox_height=22, checkbox_width=22,
            command=self.toggle_cover_settings
        ).pack(side="left", padx=10, pady=8)

        # نوع غلاف الامتحان
        cover_type_frame = ctk.CTkFrame(bubble_settings_frame, fg_color=COLORS["dark_card_alt"], corner_radius=5)
        cover_type_frame.pack(fill="x", pady=10)

        ctk.CTkLabel(
            cover_type_frame, text=":نوع غلاف الامتحان",
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"]
        ).pack(side="right", padx=(0, 10), pady=8)

        # إطار أزرار الاختيار
        cover_type_options_frame = ctk.CTkFrame(cover_type_frame, fg_color="transparent")
        cover_type_options_frame.pack(side="left", fill="x", expand=True, padx=10, pady=8)

        # متغير لتخزين نوع الغلاف
        self.cover_type_var = ctk.StringVar(value="bubble")

        # زر اختيار البابل شيت
        ctk.CTkRadioButton(
            cover_type_options_frame, text="بابل شيت", variable=self.cover_type_var, value="bubble",
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"],
            fg_color=COLORS["dark_button3"], hover_color="#FF2E63",
            radiobutton_height=16, radiobutton_width=16,
            command=self.toggle_cover_type
        ).pack(side="right", padx=10)

        # زر اختيار الغلاف العادي
        ctk.CTkRadioButton(
            cover_type_options_frame, text="غلاف عادي", variable=self.cover_type_var, value="regular",
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"],
            fg_color=COLORS["dark_button3"], hover_color="#FF2E63",
            radiobutton_height=16, radiobutton_width=16,
            command=self.toggle_cover_type
        ).pack(side="right", padx=10)

        # إطار عنوان الامتحان المخصص
        custom_title_frame = ctk.CTkFrame(bubble_settings_frame, fg_color=COLORS["dark_card_alt"], corner_radius=5)
        custom_title_frame.pack(fill="x", pady=10)

        ctk.CTkLabel(
            custom_title_frame, text=":عنوان الامتحان",
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"]
        ).pack(side="right", padx=(0, 10), pady=8)

        self.custom_title_var = ctk.StringVar(value="ورقة الإجابة")
        self.custom_title_entry = ctk.CTkEntry(
            custom_title_frame, width=300, justify="right", textvariable=self.custom_title_var,
            font=ctk.CTkFont(size=14), fg_color=COLORS["dark_bg"],
            text_color=COLORS["dark_text"], border_color=COLORS["dark_accent"], border_width=1
        )
        self.custom_title_entry.pack(side="left", padx=10, pady=8, fill="x", expand=True)

        # إطار إعدادات البابل شيت
        self.bubble_options_frame = ctk.CTkFrame(bubble_settings_frame, fg_color=COLORS["dark_card_alt"], corner_radius=5)
        self.bubble_options_frame.pack(fill="x", pady=10)

        # إطار إعدادات الغلاف العادي
        self.regular_options_frame = ctk.CTkFrame(bubble_settings_frame, fg_color=COLORS["dark_card_alt"], corner_radius=5)
        self.regular_options_frame.pack(fill="x", pady=10)

        # عنوان إعدادات الغلاف العادي
        regular_cover_header = ctk.CTkFrame(self.regular_options_frame, fg_color=COLORS["dark_card"], corner_radius=5)
        regular_cover_header.pack(fill="x", padx=10, pady=10)

        ctk.CTkLabel(
            regular_cover_header, text="إعدادات الغلاف العادي",
            font=ctk.CTkFont(size=16, weight="bold"), text_color=COLORS["dark_text"]
        ).pack(pady=5)

        # معلومات المادة
        regular_course_info_frame = ctk.CTkFrame(regular_cover_header, fg_color="transparent")
        regular_course_info_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(
            regular_course_info_frame, text=":اسم المادة",
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"]
        ).pack(side="right", padx=(0, 10), pady=8)

        self.regular_course_name_entry = ctk.CTkEntry(
            regular_course_info_frame, width=200, justify="right",
            font=ctk.CTkFont(size=14), fg_color=COLORS["dark_bg"],
            text_color=COLORS["dark_text"], border_color=COLORS["dark_accent"], border_width=1
        )
        self.regular_course_name_entry.pack(side="left", padx=10, pady=8, fill="x", expand=True)

        # معلومات المدرس
        regular_teacher_info_frame = ctk.CTkFrame(regular_cover_header, fg_color="transparent")
        regular_teacher_info_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(
            regular_teacher_info_frame, text=":اسم المدرس",
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"]
        ).pack(side="right", padx=(0, 10), pady=8)

        self.regular_teacher_name_entry = ctk.CTkEntry(
            regular_teacher_info_frame, width=200, justify="right",
            font=ctk.CTkFont(size=14), fg_color=COLORS["dark_bg"],
            text_color=COLORS["dark_text"], border_color=COLORS["dark_accent"], border_width=1
        )
        self.regular_teacher_name_entry.pack(side="left", padx=10, pady=8, fill="x", expand=True)

        # معلومات الامتحان
        regular_exam_details_frame = ctk.CTkFrame(regular_cover_header, fg_color="transparent")
        regular_exam_details_frame.pack(fill="x", pady=5)

        # إطار مدة الامتحان
        regular_duration_frame = ctk.CTkFrame(regular_exam_details_frame, fg_color="transparent")
        regular_duration_frame.pack(side="right", fill="x", expand=True)

        ctk.CTkLabel(
            regular_duration_frame, text=":مدة الامتحان (دقيقة)",
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"]
        ).pack(side="right", padx=(0, 10), pady=8)

        self.regular_exam_duration_entry = ctk.CTkEntry(
            regular_duration_frame, width=60, justify="center",
            font=ctk.CTkFont(size=14), fg_color=COLORS["dark_bg"],
            text_color=COLORS["dark_text"], border_color=COLORS["dark_accent"], border_width=1
        )
        self.regular_exam_duration_entry.insert(0, "60")
        self.regular_exam_duration_entry.pack(side="left", padx=10, pady=8)

        # إطار الدرجة الكلية
        regular_total_score_frame = ctk.CTkFrame(regular_exam_details_frame, fg_color="transparent")
        regular_total_score_frame.pack(side="left", fill="x", expand=True)

        ctk.CTkLabel(
            regular_total_score_frame, text=":الدرجة الكلية",
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"]
        ).pack(side="right", padx=(0, 10), pady=8)

        self.regular_total_score_entry = ctk.CTkEntry(
            regular_total_score_frame, width=60, justify="center",
            font=ctk.CTkFont(size=14), fg_color=COLORS["dark_bg"],
            text_color=COLORS["dark_text"], border_color=COLORS["dark_accent"], border_width=1
        )
        self.regular_total_score_entry.insert(0, "100")
        self.regular_total_score_entry.pack(side="left", padx=10, pady=8)

        # تعليمات الامتحان
        regular_instructions_frame = ctk.CTkFrame(regular_cover_header, fg_color="transparent")
        regular_instructions_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(
            regular_instructions_frame, text=":تعليمات الامتحان",
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"]
        ).pack(side="right", padx=(0, 10), pady=8)

        self.regular_exam_instructions_var = ctk.StringVar(value="يرجى الإجابة على جميع الأسئلة")
        self.regular_exam_instructions_entry = ctk.CTkEntry(
            regular_instructions_frame, width=300, justify="right", textvariable=self.regular_exam_instructions_var,
            font=ctk.CTkFont(size=14), fg_color=COLORS["dark_bg"],
            text_color=COLORS["dark_text"], border_color=COLORS["dark_accent"], border_width=1
        )
        self.regular_exam_instructions_entry.pack(side="left", padx=10, pady=8, fill="x", expand=True)

        # إطار معلومات الامتحان (مشترك بين النوعين)
        exam_info_header = ctk.CTkFrame(self.bubble_options_frame, fg_color=COLORS["dark_card"], corner_radius=5)
        exam_info_header.pack(fill="x", padx=10, pady=10)

        ctk.CTkLabel(
            exam_info_header, text="معلومات الامتحان",
            font=ctk.CTkFont(size=16, weight="bold"), text_color=COLORS["dark_text"]
        ).pack(pady=5)

        # معلومات المادة
        course_info_frame = ctk.CTkFrame(exam_info_header, fg_color="transparent")
        course_info_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(
            course_info_frame, text=":اسم المادة",
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"]
        ).pack(side="right", padx=(0, 10), pady=8)

        self.course_name_entry = ctk.CTkEntry(
            course_info_frame, width=200, justify="right",
            font=ctk.CTkFont(size=14), fg_color=COLORS["dark_bg"],
            text_color=COLORS["dark_text"], border_color=COLORS["dark_accent"], border_width=1
        )
        self.course_name_entry.pack(side="left", padx=10, pady=8, fill="x", expand=True)

        # معلومات المدرس
        teacher_info_frame = ctk.CTkFrame(exam_info_header, fg_color="transparent")
        teacher_info_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(
            teacher_info_frame, text=":اسم المدرس",
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"]
        ).pack(side="right", padx=(0, 10), pady=8)

        self.teacher_name_entry = ctk.CTkEntry(
            teacher_info_frame, width=200, justify="right",
            font=ctk.CTkFont(size=14), fg_color=COLORS["dark_bg"],
            text_color=COLORS["dark_text"], border_color=COLORS["dark_accent"], border_width=1
        )
        self.teacher_name_entry.pack(side="left", padx=10, pady=8, fill="x", expand=True)

        # معلومات الامتحان
        exam_details_frame = ctk.CTkFrame(exam_info_header, fg_color="transparent")
        exam_details_frame.pack(fill="x", pady=5)

        # إطار مدة الامتحان
        duration_frame = ctk.CTkFrame(exam_details_frame, fg_color="transparent")
        duration_frame.pack(side="right", fill="x", expand=True)

        ctk.CTkLabel(
            duration_frame, text=":مدة الامتحان (دقيقة)",
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"]
        ).pack(side="right", padx=(0, 10), pady=8)

        self.exam_duration_entry = ctk.CTkEntry(
            duration_frame, width=60, justify="center",
            font=ctk.CTkFont(size=14), fg_color=COLORS["dark_bg"],
            text_color=COLORS["dark_text"], border_color=COLORS["dark_accent"], border_width=1
        )
        self.exam_duration_entry.insert(0, "60")
        self.exam_duration_entry.pack(side="left", padx=10, pady=8)

        # إطار الدرجة الكلية
        total_score_frame = ctk.CTkFrame(exam_details_frame, fg_color="transparent")
        total_score_frame.pack(side="left", fill="x", expand=True)

        ctk.CTkLabel(
            total_score_frame, text=":الدرجة الكلية",
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"]
        ).pack(side="right", padx=(0, 10), pady=8)

        self.total_score_entry = ctk.CTkEntry(
            total_score_frame, width=60, justify="center",
            font=ctk.CTkFont(size=14), fg_color=COLORS["dark_bg"],
            text_color=COLORS["dark_text"], border_color=COLORS["dark_accent"], border_width=1
        )
        self.total_score_entry.insert(0, "100")
        self.total_score_entry.pack(side="left", padx=10, pady=8)

        # إطار إعدادات البابل شيت (دوائر الأسئلة)
        bubble_sheet_settings = ctk.CTkFrame(self.bubble_options_frame, fg_color=COLORS["dark_card"], corner_radius=5)
        bubble_sheet_settings.pack(fill="x", padx=10, pady=10)

        ctk.CTkLabel(
            bubble_sheet_settings, text="إعدادات البابل شيت",
            font=ctk.CTkFont(size=16, weight="bold"), text_color=COLORS["dark_text"]
        ).pack(pady=5)

        # عدد الأسئلة
        questions_count_frame = ctk.CTkFrame(bubble_sheet_settings, fg_color="transparent")
        questions_count_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(
            questions_count_frame, text=":عدد الأسئلة",
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"]
        ).pack(side="right", padx=(0, 10), pady=8)

        self.questions_count_entry = ctk.CTkEntry(
            questions_count_frame, width=80, justify="center",
            font=ctk.CTkFont(size=14), fg_color=COLORS["dark_bg"],
            text_color=COLORS["dark_text"], border_color=COLORS["dark_accent"], border_width=1
        )
        self.questions_count_entry.insert(0, "20")
        self.questions_count_entry.pack(side="left", padx=10, pady=8)

        # عدد الخيارات لكل سؤال
        options_count_frame = ctk.CTkFrame(bubble_sheet_settings, fg_color="transparent")
        options_count_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(
            options_count_frame, text=":عدد الخيارات لكل سؤال",
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"]
        ).pack(side="right", padx=(0, 10), pady=8)

        self.options_var = ctk.StringVar(value="4")
        options_dropdown = ctk.CTkOptionMenu(
            options_count_frame, values=["2", "3", "4", "5"], variable=self.options_var,
            font=ctk.CTkFont(size=14), fg_color=COLORS["dark_bg"],
            text_color=COLORS["dark_text"], button_color=COLORS["dark_button1"],
            button_hover_color=COLORS["dark_button2"], width=80
        )
        options_dropdown.pack(side="left", padx=10, pady=8)

        # تعليمات الامتحان
        instructions_frame = ctk.CTkFrame(bubble_sheet_settings, fg_color="transparent")
        instructions_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(
            instructions_frame, text=":تعليمات الامتحان",
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"]
        ).pack(side="right", padx=(0, 10), pady=8)

        self.exam_instructions_var = ctk.StringVar(value="قم بتظليل الدائرة المقابلة للإجابة الصحيحة لكل سؤال")
        self.exam_instructions_entry = ctk.CTkEntry(
            instructions_frame, width=300, justify="right", textvariable=self.exam_instructions_var,
            font=ctk.CTkFont(size=14), fg_color=COLORS["dark_bg"],
            text_color=COLORS["dark_text"], border_color=COLORS["dark_accent"], border_width=1
        )
        self.exam_instructions_entry.pack(side="left", padx=10, pady=8, fill="x", expand=True)

        # موضع غلاف الامتحان
        cover_position_frame = ctk.CTkFrame(self.bubble_options_frame, fg_color="transparent")
        cover_position_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(
            cover_position_frame, text=":موضع غلاف الامتحان",
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"]
        ).pack(side="right", padx=(0, 10), pady=8)

        self.cover_position_var = ctk.StringVar(value="first")
        position_frame = ctk.CTkFrame(cover_position_frame, fg_color="transparent")
        position_frame.pack(side="left", fill="x", expand=True, padx=10, pady=8)

        ctk.CTkRadioButton(
            position_frame, text="الصفحة الأولى", variable=self.cover_position_var, value="first",
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"],
            fg_color=COLORS["dark_button3"], hover_color="#FF2E63",
            radiobutton_height=16, radiobutton_width=16
        ).pack(side="right", padx=10)

        ctk.CTkRadioButton(
            position_frame, text="الصفحة الأخيرة", variable=self.cover_position_var, value="last",
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"],
            fg_color=COLORS["dark_button3"], hover_color="#FF2E63",
            radiobutton_height=16, radiobutton_width=16
        ).pack(side="right", padx=10)

        # زر معاينة غلاف الامتحان
        preview_cover_frame = ctk.CTkFrame(self.bubble_options_frame, fg_color="transparent")
        preview_cover_frame.pack(fill="x", pady=10)

        ctk.CTkButton(
            preview_cover_frame, text="معاينة غلاف الامتحان", command=self.preview_exam_cover,
            font=ctk.CTkFont(size=14), fg_color=COLORS["dark_button1"], hover_color=COLORS["dark_button2"],
            text_color="#FFFFFF", corner_radius=8, height=30, border_width=1,
            border_color=COLORS["dark_button2"]
        ).pack(fill="x", padx=10, pady=5)

        # تعطيل إطار إعدادات البابل شيت في البداية
        self.toggle_cover_settings()

        # تعطيل إطارات الإعدادات الجديدة في البداية
        if hasattr(self, 'watermark_options_frame'):
            self._set_frame_state(self.watermark_options_frame, "disabled")
        if hasattr(self, 'encryption_options_frame'):
            self._set_frame_state(self.encryption_options_frame, "disabled")
        if hasattr(self, 'random_models_options_frame'):
            self._set_frame_state(self.random_models_options_frame, "disabled")

    def create_customize_tab(self):
        """إنشاء محتوى تبويب تخصيص الأسئلة"""
        # إطار تخصيص الأسئلة
        customize_frame = ctk.CTkFrame(self.tab_customize, fg_color=COLORS["dark_card"], corner_radius=15,
                                     border_width=2, border_color=COLORS["dark_border"])
        customize_frame.grid(row=0, column=0, columnspan=2, padx=10, pady=10, sticky="nsew")

        # عنوان القسم
        title_frame = ctk.CTkFrame(customize_frame, fg_color=COLORS["dark_card_alt"], corner_radius=8)
        title_frame.pack(fill="x", padx=10, pady=10)

        ctk.CTkLabel(
            title_frame, text="تخصيص الأسئلة",
            font=ctk.CTkFont(size=18, weight="bold"), text_color=COLORS["dark_text"]
        ).pack(pady=8)

        # إطار الأسئلة
        questions_frame = ctk.CTkFrame(customize_frame, fg_color="transparent")
        questions_frame.pack(fill="both", expand=True, padx=15, pady=10)

        # رسالة عند عدم وجود أسئلة
        self.no_questions_label = ctk.CTkLabel(
            questions_frame, text="لم يتم اختيار أي صور للأسئلة بعد.\nيرجى اختيار صور الأسئلة من تبويب الرئيسية أولاً.",
            font=ctk.CTkFont(size=16), text_color=COLORS["dark_text"]
        )
        self.no_questions_label.pack(pady=50)

        # إطار عرض الأسئلة (بدون شريط تمرير)
        self.questions_list_frame = ctk.CTkFrame(
            questions_frame, fg_color=COLORS["dark_card_alt"], corner_radius=8,
            width=800, height=500
        )

        # إطار أزرار التحكم
        control_buttons_frame = ctk.CTkFrame(customize_frame, fg_color=COLORS["dark_card_alt"], corner_radius=8)
        control_buttons_frame.pack(fill="x", padx=10, pady=10)

        # زر تحديث قائمة الأسئلة
        ctk.CTkButton(
            control_buttons_frame, text="تحديث قائمة الأسئلة", command=self.refresh_questions_list,
            font=ctk.CTkFont(size=14), fg_color=COLORS["dark_button1"], hover_color=COLORS["dark_button2"],
            text_color="#FFFFFF", corner_radius=8, height=40, border_width=1,
            border_color=COLORS["dark_button2"]
        ).pack(side="right", padx=10, pady=10, fill="x", expand=True)

        # زر إضافة سؤال جديد
        ctk.CTkButton(
            control_buttons_frame, text="إضافة سؤال جديد", command=self.add_new_question,
            font=ctk.CTkFont(size=14), fg_color=COLORS["dark_button3"], hover_color="#FF2E63",
            text_color="#FFFFFF", corner_radius=8, height=40, border_width=1,
            border_color="#FF2E63"
        ).pack(side="left", padx=10, pady=10, fill="x", expand=True)

    def create_settings_tab(self):
        """إنشاء محتوى تبويب الإعدادات"""
        # إطار الإعدادات العامة
        general_settings_frame = ctk.CTkFrame(self.tab_settings, fg_color=COLORS["dark_card"], corner_radius=15,
                                            border_width=2, border_color=COLORS["dark_border"])
        general_settings_frame.grid(row=0, column=0, columnspan=2, padx=10, pady=10, sticky="nsew")

        # عنوان القسم
        title_frame = ctk.CTkFrame(general_settings_frame, fg_color=COLORS["dark_card_alt"], corner_radius=8)
        title_frame.pack(fill="x", padx=10, pady=10)

        ctk.CTkLabel(
            title_frame, text="الإعدادات العامة",
            font=ctk.CTkFont(size=18, weight="bold"), text_color=COLORS["dark_text"]
        ).pack(pady=8)

        # إطار الإعدادات
        settings_content_frame = ctk.CTkFrame(general_settings_frame, fg_color="transparent")
        settings_content_frame.pack(fill="both", expand=True, padx=15, pady=10)

        # إعدادات العلامة المائية
        watermark_frame = ctk.CTkFrame(settings_content_frame, fg_color=COLORS["dark_card_alt"], corner_radius=5)
        watermark_frame.pack(fill="x", pady=10)

        ctk.CTkLabel(
            watermark_frame, text=":العلامة المائية",
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"]
        ).pack(side="right", padx=(0, 10), pady=8)

        # تفعيل العلامة المائية
        self.enable_watermark_var = ctk.BooleanVar(value=False)
        ctk.CTkCheckBox(
            watermark_frame, text="تفعيل", variable=self.enable_watermark_var,
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"],
            fg_color=COLORS["dark_button3"], hover_color="#FF2E63",
            checkbox_height=22, checkbox_width=22,
            command=self.toggle_watermark_settings
        ).pack(side="left", padx=10, pady=8)

        # إطار إعدادات العلامة المائية
        self.watermark_options_frame = ctk.CTkFrame(settings_content_frame, fg_color=COLORS["dark_card_alt"], corner_radius=5)
        self.watermark_options_frame.pack(fill="x", pady=10)

        # نص العلامة المائية
        watermark_text_frame = ctk.CTkFrame(self.watermark_options_frame, fg_color="transparent")
        watermark_text_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(
            watermark_text_frame, text=":نص العلامة المائية",
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"]
        ).pack(side="right", padx=(0, 10), pady=8)

        self.watermark_text_var = ctk.StringVar(value="سري")
        self.watermark_text_entry = ctk.CTkEntry(
            watermark_text_frame, width=200, justify="right", textvariable=self.watermark_text_var,
            font=ctk.CTkFont(size=14), fg_color=COLORS["dark_bg"],
            text_color=COLORS["dark_text"], border_color=COLORS["dark_accent"], border_width=1
        )
        self.watermark_text_entry.pack(side="left", padx=10, pady=8, fill="x", expand=True)

        # لون العلامة المائية
        watermark_color_frame = ctk.CTkFrame(self.watermark_options_frame, fg_color="transparent")
        watermark_color_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(
            watermark_color_frame, text=":لون العلامة المائية",
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"]
        ).pack(side="right", padx=(0, 10), pady=8)

        self.watermark_color_var = ctk.StringVar(value="رمادي")
        watermark_color_options = ["رمادي", "أحمر", "أزرق", "أخضر"]
        watermark_color_dropdown = ctk.CTkOptionMenu(
            watermark_color_frame, values=watermark_color_options, variable=self.watermark_color_var,
            font=ctk.CTkFont(size=14), fg_color=COLORS["dark_bg"],
            text_color=COLORS["dark_text"], button_color=COLORS["dark_button1"],
            button_hover_color=COLORS["dark_button2"], width=120
        )
        watermark_color_dropdown.pack(side="left", padx=10, pady=8)

        # شفافية العلامة المائية
        watermark_opacity_frame = ctk.CTkFrame(self.watermark_options_frame, fg_color="transparent")
        watermark_opacity_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(
            watermark_opacity_frame, text=":شفافية العلامة المائية",
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"]
        ).pack(side="right", padx=(0, 10), pady=8)

        self.watermark_opacity_var = ctk.DoubleVar(value=0.3)
        opacity_slider = ctk.CTkSlider(
            watermark_opacity_frame, from_=0.1, to=0.9, variable=self.watermark_opacity_var,
            width=200, progress_color=COLORS["dark_button3"], button_color=COLORS["dark_button1"],
            button_hover_color=COLORS["dark_button2"]
        )
        opacity_slider.pack(side="left", padx=10, pady=8)

        # إعدادات تشفير PDF
        pdf_security_frame = ctk.CTkFrame(settings_content_frame, fg_color=COLORS["dark_card_alt"], corner_radius=5)
        pdf_security_frame.pack(fill="x", pady=10)

        ctk.CTkLabel(
            pdf_security_frame, text=":تشفير ملف PDF",
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"]
        ).pack(side="right", padx=(0, 10), pady=8)

        # تفعيل تشفير PDF
        self.enable_encryption_var = ctk.BooleanVar(value=False)
        ctk.CTkCheckBox(
            pdf_security_frame, text="تفعيل", variable=self.enable_encryption_var,
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"],
            fg_color=COLORS["dark_button3"], hover_color="#FF2E63",
            checkbox_height=22, checkbox_width=22,
            command=self.toggle_encryption_settings
        ).pack(side="left", padx=10, pady=8)

        # إطار إعدادات تشفير PDF
        self.encryption_options_frame = ctk.CTkFrame(settings_content_frame, fg_color=COLORS["dark_card_alt"], corner_radius=5)
        self.encryption_options_frame.pack(fill="x", pady=10)

        # كلمة مرور PDF
        password_frame = ctk.CTkFrame(self.encryption_options_frame, fg_color="transparent")
        password_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(
            password_frame, text=":كلمة المرور",
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"]
        ).pack(side="right", padx=(0, 10), pady=8)

        self.pdf_password_var = ctk.StringVar()
        self.pdf_password_entry = ctk.CTkEntry(
            password_frame, width=200, justify="right", textvariable=self.pdf_password_var,
            font=ctk.CTkFont(size=14), fg_color=COLORS["dark_bg"], show="*",
            text_color=COLORS["dark_text"], border_color=COLORS["dark_accent"], border_width=1
        )
        self.pdf_password_entry.pack(side="left", padx=10, pady=8, fill="x", expand=True)

        # إعدادات توليد نماذج عشوائية
        random_models_frame = ctk.CTkFrame(settings_content_frame, fg_color=COLORS["dark_card_alt"], corner_radius=5)
        random_models_frame.pack(fill="x", pady=10)

        ctk.CTkLabel(
            random_models_frame, text=":توليد نماذج عشوائية",
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"]
        ).pack(side="right", padx=(0, 10), pady=8)

        # تفعيل توليد نماذج عشوائية
        self.enable_random_models_var = ctk.BooleanVar(value=False)
        ctk.CTkCheckBox(
            random_models_frame, text="تفعيل", variable=self.enable_random_models_var,
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"],
            fg_color=COLORS["dark_button3"], hover_color="#FF2E63",
            checkbox_height=22, checkbox_width=22,
            command=self.toggle_random_models_settings
        ).pack(side="left", padx=10, pady=8)

        # إطار إعدادات توليد نماذج عشوائية
        self.random_models_options_frame = ctk.CTkFrame(settings_content_frame, fg_color=COLORS["dark_card_alt"], corner_radius=5)
        self.random_models_options_frame.pack(fill="x", pady=10)

        # عدد النماذج
        models_count_frame = ctk.CTkFrame(self.random_models_options_frame, fg_color="transparent")
        models_count_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(
            models_count_frame, text=":عدد النماذج",
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"]
        ).pack(side="right", padx=(0, 10), pady=8)

        self.models_count_var = ctk.StringVar(value="2")
        models_count_dropdown = ctk.CTkOptionMenu(
            models_count_frame, values=["2", "3", "4", "5"], variable=self.models_count_var,
            font=ctk.CTkFont(size=14), fg_color=COLORS["dark_bg"],
            text_color=COLORS["dark_text"], button_color=COLORS["dark_button1"],
            button_hover_color=COLORS["dark_button2"], width=80
        )
        models_count_dropdown.pack(side="left", padx=10, pady=8)

        # إعدادات الطباعة
        print_settings_frame = ctk.CTkFrame(settings_content_frame, fg_color=COLORS["dark_card_alt"], corner_radius=5)
        print_settings_frame.pack(fill="x", pady=10)

        ctk.CTkLabel(
            print_settings_frame, text=":إعدادات الطباعة",
            font=ctk.CTkFont(size=14, weight="bold"), text_color=COLORS["dark_text"]
        ).pack(side="right", padx=(0, 10), pady=8)

        # إطار إعدادات الطباعة
        print_options_frame = ctk.CTkFrame(settings_content_frame, fg_color=COLORS["dark_card_alt"], corner_radius=5)
        print_options_frame.pack(fill="x", pady=10)

        # حجم الورق
        paper_size_frame = ctk.CTkFrame(print_options_frame, fg_color="transparent")
        paper_size_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(
            paper_size_frame, text=":حجم الورق",
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"]
        ).pack(side="right", padx=(0, 10), pady=8)

        self.paper_size_var = ctk.StringVar(value="A4")
        paper_size_options = ["A4", "A3", "Letter"]
        paper_size_dropdown = ctk.CTkOptionMenu(
            paper_size_frame, values=paper_size_options, variable=self.paper_size_var,
            font=ctk.CTkFont(size=14), fg_color=COLORS["dark_bg"],
            text_color=COLORS["dark_text"], button_color=COLORS["dark_button1"],
            button_hover_color=COLORS["dark_button2"], width=120
        )
        paper_size_dropdown.pack(side="left", padx=10, pady=8)

        # اتجاه الصفحة
        orientation_frame = ctk.CTkFrame(print_options_frame, fg_color="transparent")
        orientation_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(
            orientation_frame, text=":اتجاه الصفحة",
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"]
        ).pack(side="right", padx=(0, 10), pady=8)

        self.orientation_var = ctk.StringVar(value="عمودي")
        orientation_options = ["عمودي", "أفقي"]
        orientation_dropdown = ctk.CTkOptionMenu(
            orientation_frame, values=orientation_options, variable=self.orientation_var,
            font=ctk.CTkFont(size=14), fg_color=COLORS["dark_bg"],
            text_color=COLORS["dark_text"], button_color=COLORS["dark_button1"],
            button_hover_color=COLORS["dark_button2"], width=120
        )
        orientation_dropdown.pack(side="left", padx=10, pady=8)

        # الهوامش
        margins_frame = ctk.CTkFrame(print_options_frame, fg_color="transparent")
        margins_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(
            margins_frame, text=":الهوامش (مم)",
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"]
        ).pack(side="right", padx=(0, 10), pady=8)

        self.margins_var = ctk.StringVar(value="10")
        margins_entry = ctk.CTkEntry(
            margins_frame, width=80, justify="center", textvariable=self.margins_var,
            font=ctk.CTkFont(size=14), fg_color=COLORS["dark_bg"],
            text_color=COLORS["dark_text"], border_color=COLORS["dark_accent"], border_width=1
        )
        margins_entry.pack(side="left", padx=10, pady=8)

        # إعدادات التصدير
        export_settings_frame = ctk.CTkFrame(settings_content_frame, fg_color=COLORS["dark_card_alt"], corner_radius=5)
        export_settings_frame.pack(fill="x", pady=10)

        ctk.CTkLabel(
            export_settings_frame, text=":إعدادات التصدير",
            font=ctk.CTkFont(size=14, weight="bold"), text_color=COLORS["dark_text"]
        ).pack(side="right", padx=(0, 10), pady=8)

        # إطار إعدادات التصدير
        export_options_frame = ctk.CTkFrame(settings_content_frame, fg_color=COLORS["dark_card_alt"], corner_radius=5)
        export_options_frame.pack(fill="x", pady=10)

        # جودة الصور
        image_quality_frame = ctk.CTkFrame(export_options_frame, fg_color="transparent")
        image_quality_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(
            image_quality_frame, text=":جودة الصور",
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"]
        ).pack(side="right", padx=(0, 10), pady=8)

        self.image_quality_var = ctk.IntVar(value=90)
        image_quality_slider = ctk.CTkSlider(
            image_quality_frame, from_=50, to=100, variable=self.image_quality_var,
            width=200, progress_color=COLORS["dark_button3"], button_color=COLORS["dark_button1"],
            button_hover_color=COLORS["dark_button2"]
        )
        image_quality_slider.pack(side="left", padx=10, pady=8)

        image_quality_label = ctk.CTkLabel(
            image_quality_frame, textvariable=self.image_quality_var,
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"]
        )
        image_quality_label.pack(side="left", padx=10, pady=8)

        # ضغط ملف PDF
        compress_pdf_frame = ctk.CTkFrame(export_options_frame, fg_color="transparent")
        compress_pdf_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(
            compress_pdf_frame, text=":ضغط ملف PDF",
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"]
        ).pack(side="right", padx=(0, 10), pady=8)

        self.compress_pdf_var = ctk.BooleanVar(value=False)
        ctk.CTkCheckBox(
            compress_pdf_frame, text="تفعيل", variable=self.compress_pdf_var,
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"],
            fg_color=COLORS["dark_button3"], hover_color="#FF2E63",
            checkbox_height=22, checkbox_width=22
        ).pack(side="left", padx=10, pady=8)

        # إضافة فهرس للصفحات
        add_index_frame = ctk.CTkFrame(export_options_frame, fg_color="transparent")
        add_index_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(
            add_index_frame, text=":إضافة فهرس للصفحات",
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"]
        ).pack(side="right", padx=(0, 10), pady=8)

        self.add_index_var = ctk.BooleanVar(value=False)
        ctk.CTkCheckBox(
            add_index_frame, text="تفعيل", variable=self.add_index_var,
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"],
            fg_color=COLORS["dark_button3"], hover_color="#FF2E63",
            checkbox_height=22, checkbox_width=22
        ).pack(side="left", padx=10, pady=8)

        # إعدادات المظهر
        appearance_settings_frame = ctk.CTkFrame(settings_content_frame, fg_color=COLORS["dark_card_alt"], corner_radius=5)
        appearance_settings_frame.pack(fill="x", pady=10)

        ctk.CTkLabel(
            appearance_settings_frame, text=":إعدادات المظهر",
            font=ctk.CTkFont(size=14, weight="bold"), text_color=COLORS["dark_text"]
        ).pack(side="right", padx=(0, 10), pady=8)

        # إطار إعدادات المظهر
        appearance_options_frame = ctk.CTkFrame(settings_content_frame, fg_color=COLORS["dark_card_alt"], corner_radius=5)
        appearance_options_frame.pack(fill="x", pady=10)

        # نوع الخط
        font_type_frame = ctk.CTkFrame(appearance_options_frame, fg_color="transparent")
        font_type_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(
            font_type_frame, text=":نوع الخط",
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"]
        ).pack(side="right", padx=(0, 10), pady=8)

        self.font_type_var = ctk.StringVar(value="Arial")
        font_type_options = ["Arial", "Times New Roman", "Calibri", "Tahoma"]
        font_type_dropdown = ctk.CTkOptionMenu(
            font_type_frame, values=font_type_options, variable=self.font_type_var,
            font=ctk.CTkFont(size=14), fg_color=COLORS["dark_bg"],
            text_color=COLORS["dark_text"], button_color=COLORS["dark_button1"],
            button_hover_color=COLORS["dark_button2"], width=150
        )
        font_type_dropdown.pack(side="left", padx=10, pady=8)

        # حجم الخط
        font_size_frame = ctk.CTkFrame(appearance_options_frame, fg_color="transparent")
        font_size_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(
            font_size_frame, text=":حجم الخط",
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"]
        ).pack(side="right", padx=(0, 10), pady=8)

        self.font_size_var = ctk.StringVar(value="12")
        font_size_options = ["10", "12", "14", "16", "18", "20"]
        font_size_dropdown = ctk.CTkOptionMenu(
            font_size_frame, values=font_size_options, variable=self.font_size_var,
            font=ctk.CTkFont(size=14), fg_color=COLORS["dark_bg"],
            text_color=COLORS["dark_text"], button_color=COLORS["dark_button1"],
            button_hover_color=COLORS["dark_button2"], width=80
        )
        font_size_dropdown.pack(side="left", padx=10, pady=8)

        # لون الخط
        font_color_frame = ctk.CTkFrame(appearance_options_frame, fg_color="transparent")
        font_color_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(
            font_color_frame, text=":لون الخط",
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"]
        ).pack(side="right", padx=(0, 10), pady=8)

        self.font_color_var = ctk.StringVar(value="أسود")
        font_color_options = ["أسود", "أزرق", "أحمر", "أخضر"]
        font_color_dropdown = ctk.CTkOptionMenu(
            font_color_frame, values=font_color_options, variable=self.font_color_var,
            font=ctk.CTkFont(size=14), fg_color=COLORS["dark_bg"],
            text_color=COLORS["dark_text"], button_color=COLORS["dark_button1"],
            button_hover_color=COLORS["dark_button2"], width=120
        )
        font_color_dropdown.pack(side="left", padx=10, pady=8)

        # إعدادات متقدمة
        advanced_settings_frame = ctk.CTkFrame(settings_content_frame, fg_color=COLORS["dark_card_alt"], corner_radius=5)
        advanced_settings_frame.pack(fill="x", pady=10)

        ctk.CTkLabel(
            advanced_settings_frame, text=":إعدادات متقدمة",
            font=ctk.CTkFont(size=14, weight="bold"), text_color=COLORS["dark_text"]
        ).pack(side="right", padx=(0, 10), pady=8)

        # إطار الإعدادات المتقدمة
        advanced_options_frame = ctk.CTkFrame(settings_content_frame, fg_color=COLORS["dark_card_alt"], corner_radius=5)
        advanced_options_frame.pack(fill="x", pady=10)

        # النسخ الاحتياطي التلقائي
        auto_backup_frame = ctk.CTkFrame(advanced_options_frame, fg_color="transparent")
        auto_backup_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(
            auto_backup_frame, text=":النسخ الاحتياطي التلقائي",
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"]
        ).pack(side="right", padx=(0, 10), pady=8)

        self.auto_backup_var = ctk.BooleanVar(value=False)
        ctk.CTkCheckBox(
            auto_backup_frame, text="تفعيل", variable=self.auto_backup_var,
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"],
            fg_color=COLORS["dark_button3"], hover_color="#FF2E63",
            checkbox_height=22, checkbox_width=22,
            command=self.toggle_backup_settings
        ).pack(side="left", padx=10, pady=8)

        # مجلد النسخ الاحتياطي
        backup_folder_frame = ctk.CTkFrame(advanced_options_frame, fg_color="transparent")
        backup_folder_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(
            backup_folder_frame, text=":مجلد النسخ الاحتياطي",
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"]
        ).pack(side="right", padx=(0, 10), pady=8)

        self.backup_folder_var = ctk.StringVar(value=os.path.join(os.path.expanduser("~"), "PDF_Builder_Backup"))
        backup_folder_entry = ctk.CTkEntry(
            backup_folder_frame, width=250, justify="right", textvariable=self.backup_folder_var,
            font=ctk.CTkFont(size=14), fg_color=COLORS["dark_bg"],
            text_color=COLORS["dark_text"], border_color=COLORS["dark_accent"], border_width=1
        )
        backup_folder_entry.pack(side="left", padx=10, pady=8, fill="x", expand=True)

        backup_folder_button = ctk.CTkButton(
            backup_folder_frame, text="تصفح", command=self.select_backup_folder,
            font=ctk.CTkFont(size=14), fg_color=COLORS["dark_button1"], hover_color=COLORS["dark_button2"],
            text_color="#FFFFFF", corner_radius=8, width=80, height=30, border_width=1,
            border_color=COLORS["dark_button2"]
        )
        backup_folder_button.pack(side="left", padx=10, pady=8)

        # فترة النسخ الاحتياطي
        backup_interval_frame = ctk.CTkFrame(advanced_options_frame, fg_color="transparent")
        backup_interval_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(
            backup_interval_frame, text=":فترة النسخ الاحتياطي (دقيقة)",
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"]
        ).pack(side="right", padx=(0, 10), pady=8)

        self.backup_interval_var = ctk.StringVar(value="30")
        backup_interval_entry = ctk.CTkEntry(
            backup_interval_frame, width=80, justify="center", textvariable=self.backup_interval_var,
            font=ctk.CTkFont(size=14), fg_color=COLORS["dark_bg"],
            text_color=COLORS["dark_text"], border_color=COLORS["dark_accent"], border_width=1
        )
        backup_interval_entry.pack(side="left", padx=10, pady=8)

        # زر حفظ الإعدادات
        save_settings_button = ctk.CTkButton(
            settings_content_frame, text="حفظ الإعدادات", command=self.save_settings,
            font=ctk.CTkFont(size=14), fg_color=COLORS["dark_button3"], hover_color="#FF2E63",
            text_color="#FFFFFF", corner_radius=8, height=40, border_width=1,
            border_color="#FF2E63"
        )
        save_settings_button.pack(fill="x", padx=10, pady=10)

        # معلومات المطور
        developer_frame = ctk.CTkFrame(settings_content_frame, fg_color=COLORS["dark_card_alt"], corner_radius=5)
        developer_frame.pack(fill="x", pady=10)

        ctk.CTkLabel(
            developer_frame, text="المهندس يوسف غنام",
            font=ctk.CTkFont(size=16, weight="bold"), text_color=COLORS["dark_text"]
        ).pack(pady=10)

        # تعطيل إطارات الإعدادات المتقدمة في البداية
        self.backup_options_frame = advanced_options_frame
        self._set_frame_state(backup_folder_frame, "disabled")
        self._set_frame_state(backup_interval_frame, "disabled")

    def select_questions(self):
        """اختيار صور الأسئلة"""
        file_paths = filedialog.askopenfilenames(
            title="اختيار صور الأسئلة",
            filetypes=[("Image files", "*.jpg *.jpeg *.png *.bmp *.gif")]
        )

        if file_paths:
            self.question_images = list(file_paths)
            self.status.configure(text=f"تم اختيار {len(self.question_images)} صورة للأسئلة.")
            # حفظ الإعدادات بعد اختيار الصور
            self.save_settings()

            # تحديث قائمة الأسئلة في تبويب التخصيص
            if hasattr(self, 'questions_list_frame'):
                self.refresh_questions_list()
        else:
            self.status.configure(text="لم يتم اختيار أي صور للأسئلة.")

    def select_frame(self):
        """اختيار صورة الفريم"""
        file_path = filedialog.askopenfilename(
            title="اختيار صورة الفريم",
            filetypes=[("Image files", "*.jpg *.jpeg *.png *.bmp *.gif")]
        )

        if file_path:
            self.frame_image_path = file_path
            self.status.configure(text="تم اختيار صورة الفريم بنجاح.")
            # حفظ الإعدادات بعد اختيار صورة الفريم
            self.save_settings()
        else:
            self.status.configure(text="لم يتم اختيار صورة الفريم.")

    def select_logo(self):
        """اختيار صورة اللوجو"""
        file_path = filedialog.askopenfilename(
            title="اختيار صورة اللوجو",
            filetypes=[("Image files", "*.jpg *.jpeg *.png *.bmp *.gif")]
        )

        if file_path:
            self.logo_image_path = file_path
            self.remove_logo_button.configure(state="normal")
            self.status.configure(text="تم اختيار صورة اللوجو بنجاح.")
            # حفظ الإعدادات بعد اختيار صورة اللوجو
            self.save_settings()
        else:
            self.status.configure(text="لم يتم اختيار صورة اللوجو.")

    def remove_logo(self):
        """إزالة صورة اللوجو"""
        if hasattr(self, 'logo_image_path'):
            self.logo_image_path = None
            self.remove_logo_button.configure(state="disabled")
            self.status.configure(text="تم إزالة صورة اللوجو.")
            # حفظ الإعدادات بعد إزالة صورة اللوجو
            self.save_settings()
        else:
            self.status.configure(text="لا توجد صورة لوجو لإزالتها.")

    def on_closing(self):
        """وظيفة تنفذ عند إغلاق البرنامج"""
        # حفظ الإعدادات قبل الإغلاق
        try:
            self.save_settings()
        except:
            pass

        # إيقاف مستمع الأوامر من بوت التلجرام
        if hasattr(self, 'command_listener'):
            try:
                self.command_listener.stop()
            except:
                pass

        # إغلاق البرنامج
        self.destroy()

    def refresh_questions_list(self):
        """تحديث قائمة الأسئلة في تبويب التخصيص"""
        if not hasattr(self, 'questions_list_frame') or not hasattr(self, 'no_questions_label'):
            return

        # التحقق من وجود أسئلة
        if not hasattr(self, 'question_images') or not self.question_images:
            self.no_questions_label.pack(pady=50)
            self.questions_list_frame.pack_forget()
            return

        # إخفاء رسالة عدم وجود أسئلة
        self.no_questions_label.pack_forget()

        # إظهار إطار قائمة الأسئلة
        self.questions_list_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # حذف جميع العناصر السابقة
        for widget in self.questions_list_frame.winfo_children():
            widget.destroy()

        # إضافة الأسئلة إلى القائمة
        for i, question_path in enumerate(self.question_images):
            # إطار السؤال
            question_frame = ctk.CTkFrame(self.questions_list_frame, fg_color=COLORS["dark_card"], corner_radius=8)
            question_frame.pack(fill="x", padx=10, pady=5, ipadx=5, ipady=5)

            # رقم السؤال
            question_number_label = ctk.CTkLabel(
                question_frame, text=f"سؤال {i+1}",
                font=ctk.CTkFont(size=16, weight="bold"), text_color=COLORS["dark_text"]
            )
            question_number_label.grid(row=0, column=3, padx=10, pady=5, sticky="e")

            # مسار السؤال
            question_path_label = ctk.CTkLabel(
                question_frame, text=os.path.basename(question_path),
                font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"]
            )
            question_path_label.grid(row=0, column=2, padx=10, pady=5, sticky="e")

            # زر معاينة السؤال
            preview_button = ctk.CTkButton(
                question_frame, text="معاينة",
                command=lambda path=question_path: self.preview_question(path),
                font=ctk.CTkFont(size=14), fg_color=COLORS["dark_button1"], hover_color=COLORS["dark_button2"],
                text_color="#FFFFFF", corner_radius=8, width=80, height=30, border_width=1,
                border_color=COLORS["dark_button2"]
            )
            preview_button.grid(row=0, column=1, padx=5, pady=5, sticky="w")

            # زر تعديل السؤال
            edit_button = ctk.CTkButton(
                question_frame, text="تعديل",
                command=lambda idx=i, path=question_path: self.edit_question(idx, path),
                font=ctk.CTkFont(size=14), fg_color=COLORS["dark_button3"], hover_color="#FF2E63",
                text_color="#FFFFFF", corner_radius=8, width=80, height=30, border_width=1,
                border_color="#FF2E63"
            )
            edit_button.grid(row=0, column=0, padx=5, pady=5, sticky="w")

            # تكوين الشبكة
            question_frame.grid_columnconfigure(2, weight=1)

        self.status.configure(text=f"تم تحديث قائمة الأسئلة: {len(self.question_images)} سؤال")

    def preview_question(self, question_path):
        """معاينة صورة السؤال"""
        try:
            # فتح الصورة باستخدام البرنامج الافتراضي
            os.startfile(question_path)
            self.status.configure(text=f"تم فتح صورة السؤال: {os.path.basename(question_path)}")
        except Exception as e:
            self.status.configure(text=f"خطأ في فتح صورة السؤال: {e}")

    def edit_question(self, question_index, question_path):
        """تعديل صورة السؤال"""
        # إنشاء نافذة تعديل السؤال
        edit_window = ctk.CTkToplevel(self)
        edit_window.title(f"تعديل السؤال {question_index+1}")
        edit_window.geometry("800x600")
        edit_window.grab_set()  # جعل النافذة مركزة

        # تعيين الثيم
        edit_window.configure(fg_color=COLORS[f"{self.current_theme}_bg"])

        # إطار الصورة
        image_frame = ctk.CTkFrame(edit_window, fg_color=COLORS[f"{self.current_theme}_card"], corner_radius=15)
        image_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # عنوان الإطار
        ctk.CTkLabel(
            image_frame, text=f"تعديل السؤال {question_index+1}",
            font=ctk.CTkFont(size=18, weight="bold"), text_color=COLORS[f"{self.current_theme}_text"]
        ).pack(pady=10)

        # محاولة فتح الصورة وعرضها
        try:
            # فتح الصورة
            img = Image.open(question_path)

            # تحجيم الصورة للعرض
            max_width, max_height = 700, 400
            img_width, img_height = img.size

            # حساب نسبة التحجيم
            scale = min(max_width / img_width, max_height / img_height)
            new_width, new_height = int(img_width * scale), int(img_height * scale)

            # تحجيم الصورة
            img = img.resize((new_width, new_height), Image.LANCZOS)

            # تحويل الصورة إلى صورة CTk
            ctk_img = ctk.CTkImage(light_image=img, dark_image=img, size=(new_width, new_height))

            # عرض الصورة
            img_label = ctk.CTkLabel(image_frame, image=ctk_img, text="")
            img_label.pack(pady=10)

            # حفظ الصورة في المتغير لمنع حذفها بواسطة جامع النفايات
            img_label.image = ctk_img

            # إطار الأزرار
            buttons_frame = ctk.CTkFrame(image_frame, fg_color="transparent")
            buttons_frame.pack(fill="x", padx=20, pady=10)

            # زر استبدال الصورة
            ctk.CTkButton(
                buttons_frame, text="استبدال الصورة",
                command=lambda: self.replace_question(question_index, edit_window),
                font=ctk.CTkFont(size=14), fg_color=COLORS[f"{self.current_theme}_button1"],
                hover_color=COLORS[f"{self.current_theme}_button2"],
                text_color="#FFFFFF", corner_radius=8, height=40, border_width=1,
                border_color=COLORS[f"{self.current_theme}_button2"]
            ).pack(side="right", padx=5, pady=5, fill="x", expand=True)

            # زر حذف السؤال
            ctk.CTkButton(
                buttons_frame, text="حذف السؤال",
                command=lambda: self.delete_question(question_index, edit_window),
                font=ctk.CTkFont(size=14), fg_color=COLORS[f"{self.current_theme}_button3"],
                hover_color="#FF2E63",
                text_color="#FFFFFF", corner_radius=8, height=40, border_width=1,
                border_color="#FF2E63"
            ).pack(side="left", padx=5, pady=5, fill="x", expand=True)

        except Exception as e:
            # عرض رسالة خطأ
            ctk.CTkLabel(
                image_frame, text=f"خطأ في فتح الصورة: {e}",
                font=ctk.CTkFont(size=16), text_color=COLORS[f"{self.current_theme}_text"]
            ).pack(pady=50)

    def replace_question(self, question_index, edit_window):
        """استبدال صورة السؤال"""
        # اختيار صورة جديدة
        file_path = filedialog.askopenfilename(
            title="اختيار صورة السؤال الجديدة",
            filetypes=[("Image files", "*.jpg *.jpeg *.png *.bmp *.gif")]
        )

        if file_path:
            # استبدال الصورة في القائمة
            self.question_images[question_index] = file_path

            # حفظ الإعدادات
            self.save_settings()

            # تحديث قائمة الأسئلة
            self.refresh_questions_list()

            # إغلاق نافذة التعديل
            edit_window.destroy()

            # تحديث حالة التطبيق
            self.status.configure(text=f"تم استبدال صورة السؤال {question_index+1} بنجاح")

    def delete_question(self, question_index, edit_window):
        """حذف السؤال من القائمة"""
        # التأكيد قبل الحذف
        dialog = CTkMessagebox(
            title="تأكيد الحذف",
            message=f"هل أنت متأكد من حذف السؤال {question_index+1}؟",
            icon="question",
            option_1="نعم",
            option_2="لا"
        )
        response = dialog.get()

        if response == "نعم":
            # حذف السؤال من القائمة
            del self.question_images[question_index]

            # حفظ الإعدادات
            self.save_settings()

            # تحديث قائمة الأسئلة
            self.refresh_questions_list()

            # إغلاق نافذة التعديل
            edit_window.destroy()

            # تحديث حالة التطبيق
            self.status.configure(text=f"تم حذف السؤال {question_index+1} بنجاح")

    def add_new_question(self):
        """إضافة سؤال جديد"""
        # اختيار صورة جديدة
        file_paths = filedialog.askopenfilenames(
            title="اختيار صور الأسئلة الجديدة",
            filetypes=[("Image files", "*.jpg *.jpeg *.png *.bmp *.gif")]
        )

        if file_paths:
            # إنشاء قائمة الأسئلة إذا لم تكن موجودة
            if not hasattr(self, 'question_images') or self.question_images is None:
                self.question_images = []

            # إضافة الأسئلة الجديدة
            self.question_images.extend(list(file_paths))

            # حفظ الإعدادات
            self.save_settings()

            # تحديث قائمة الأسئلة
            self.refresh_questions_list()

            # تحديث حالة التطبيق
            self.status.configure(text=f"تم إضافة {len(file_paths)} سؤال جديد بنجاح")

    def handle_drop(self, event):
        """معالجة السحب والإفلات للملفات"""
        # الحصول على مسارات الملفات المسحوبة
        file_paths = self.tk.splitlist(event.data)

        # التحقق من أن الملفات هي صور
        valid_extensions = (".jpg", ".jpeg", ".png", ".bmp", ".gif")
        image_files = [f for f in file_paths if os.path.isfile(f) and f.lower().endswith(valid_extensions)]

        if not image_files:
            self.status.configure(text="لم يتم اختيار أي صور صالحة. يرجى سحب ملفات صور فقط.")
            return

        # تحديد نوع الملفات المسحوبة (أسئلة، فريم، لوجو)
        if len(image_files) == 1:
            # إذا كان ملف واحد، اسأل المستخدم عن نوع الملف
            dialog = CTkMessagebox(
                title="نوع الملف",
                message="ما هو نوع الصورة المسحوبة؟",
                icon="question",
                option_1="صورة سؤال",
                option_2="صورة فريم",
                option_3="صورة لوجو"
            )
            response = dialog.get()

            if response == "صورة سؤال":
                self.question_images = image_files
                self.status.configure(text="تم إضافة صورة السؤال بنجاح.")
                # تحديث قائمة الأسئلة في تبويب التخصيص
                if hasattr(self, 'questions_list_frame'):
                    self.refresh_questions_list()
            elif response == "صورة فريم":
                self.frame_image_path = image_files[0]
                self.status.configure(text="تم إضافة صورة الفريم بنجاح.")
            elif response == "صورة لوجو":
                self.logo_image_path = image_files[0]
                if hasattr(self, 'remove_logo_button'):
                    self.remove_logo_button.configure(state="normal")
                self.status.configure(text="تم إضافة صورة اللوجو بنجاح.")
        else:
            # إذا كان أكثر من ملف، فهي صور أسئلة
            self.question_images = image_files
            self.status.configure(text=f"تم إضافة {len(image_files)} صورة للأسئلة بنجاح.")
            # تحديث قائمة الأسئلة في تبويب التخصيص
            if hasattr(self, 'questions_list_frame'):
                self.refresh_questions_list()

        # حفظ الإعدادات بعد إضافة الصور
        self.save_settings()

    def preview_pdf(self):
        """معاينة ملف PDF"""
        if hasattr(self, 'question_images') and hasattr(self, 'frame_image_path'):
            try:
                # إنشاء ملف PDF مؤقت للمعاينة
                temp_file = "temp_preview.pdf"
                self.generate_pdf_file(temp_file)

                # فتح ملف PDF في المعاين الافتراضي
                os.startfile(temp_file)

                # تحديث حالة التطبيق
                self.status.configure(text="تم إنشاء معاينة PDF بنجاح.")
            except Exception as e:
                self.status.configure(text=f"حدث خطأ أثناء إنشاء معاينة PDF: {str(e)}")
        else:
            self.status.configure(text="يرجى اختيار صور الأسئلة وصورة الفريم أولاً.")

    def generate_pdf(self):
        """إنشاء ملف PDF نهائي"""
        if hasattr(self, 'question_images') and hasattr(self, 'frame_image_path'):
            try:
                # اختيار مسار حفظ ملف PDF
                file_path = filedialog.asksaveasfilename(
                    title="حفظ ملف PDF",
                    defaultextension=".pdf",
                    filetypes=[("PDF files", "*.pdf")]
                )

                if file_path:
                    # إنشاء ملف PDF
                    self.generate_pdf_file(file_path)

                    # تحديث حالة التطبيق
                    self.status.configure(text=f"تم إنشاء ملف PDF بنجاح وحفظه في: {file_path}")
                else:
                    self.status.configure(text="تم إلغاء عملية حفظ ملف PDF.")
            except Exception as e:
                self.status.configure(text=f"حدث خطأ أثناء إنشاء ملف PDF: {str(e)}")
        else:
            self.status.configure(text="يرجى اختيار صور الأسئلة وصورة الفريم أولاً.")

    def generate_pdf_file(self, output_path):
        """إنشاء ملف PDF بالمسار المحدد بأصغر حجم ممكن مع الحفاظ على الجودة"""
        # التحقق من وجود الصور المطلوبة
        if not hasattr(self, 'question_images') or not self.question_images:
            raise ValueError("لم يتم اختيار صور الأسئلة.")

        if not hasattr(self, 'frame_image_path') or not self.frame_image_path:
            raise ValueError("لم يتم اختيار صورة الفريم.")

        # الحصول على عدد الأسئلة في الصفحة
        try:
            per_page = int(self.num_per_page_entry.get())
        except:
            per_page = 5  # القيمة الافتراضية

        # تحديد دقة الصور (DPI) - قيمة أقل تعني حجم ملف أصغر
        image_dpi = 150  # قيمة متوازنة بين الجودة وحجم الملف

        # إنشاء ملف PDF مع خيارات ضغط
        from reportlab.pdfgen.canvas import Canvas
        c = Canvas(output_path, pagesize=letter, pageCompression=1)

        # إضافة غلاف الامتحان إذا كان مفعلاً
        if hasattr(self, 'enable_cover_var') and self.enable_cover_var.get():
            try:
                # تحديد نوع الغلاف
                cover_type = self.cover_type_var.get() if hasattr(self, 'cover_type_var') else "bubble"

                if cover_type == "bubble":
                    # التحقق من وجود إعدادات البابل شيت
                    if hasattr(self, 'questions_count_entry') and hasattr(self, 'options_var'):
                        questions_count = int(self.questions_count_entry.get())
                        options_count = int(self.options_var.get())
                    else:
                        questions_count = 0
                        options_count = 0
                else:
                    # الغلاف العادي لا يحتاج إلى عدد أسئلة أو خيارات
                    questions_count = 0
                    options_count = 0

                # إنشاء صورة غلاف الامتحان
                cover_image = self.create_exam_cover(questions_count, options_count)

                # ضغط صورة الغلاف وتحويلها إلى JPEG بجودة متوسطة
                cover_buffer = BytesIO()
                cover_image = cover_image.convert('RGB')  # تحويل إلى RGB لدعم تنسيق JPEG

                # تقليل حجم الصورة قبل الحفظ
                cover_width, cover_height = cover_image.size
                if cover_width > 1000:  # إذا كان عرض الصورة أكبر من 1000 بكسل
                    scale_factor = 1000 / cover_width
                    new_width = int(cover_width * scale_factor)
                    new_height = int(cover_height * scale_factor)
                    cover_image = cover_image.resize((new_width, new_height), Image.LANCZOS)

                # حفظ الصورة بجودة متوسطة مع تفعيل خيارات الضغط
                cover_image.save(cover_buffer, format='JPEG', quality=80, optimize=True, progressive=True)
                cover_buffer.seek(0)

                # إضافة غلاف الامتحان كصفحة أولى إذا كان الموضع هو "first"
                if hasattr(self, 'cover_position_var') and self.cover_position_var.get() == "first":
                    c.drawImage(ImageReader(cover_buffer), 0, 0, width=letter[0], height=letter[1])
                    c.showPage()

                # حفظ الغلاف للاستخدام لاحقًا إذا كان الموضع هو "last"
                if hasattr(self, 'cover_position_var') and self.cover_position_var.get() == "last":
                    self._cover_buffer = cover_buffer
            except Exception as e:
                print(f"حدث خطأ أثناء إنشاء غلاف الامتحان: {str(e)}")

        # معالجة صور الأسئلة
        for i in range(0, len(self.question_images), per_page):
            # الحصول على مجموعة الصور للصفحة الحالية
            page_images = self.question_images[i:i+per_page]

            # تحميل صورة الفريم
            frame = Image.open(self.frame_image_path).convert("RGB")

            # ضغط صورة الفريم مسبقًا إذا كانت كبيرة جدًا
            fw, fh = frame.size
            if fw > 1500:  # إذا كان عرض الفريم أكبر من 1500 بكسل
                scale_factor = 1500 / fw
                new_fw = int(fw * scale_factor)
                new_fh = int(fh * scale_factor)
                frame = frame.resize((new_fw, new_fh), Image.LANCZOS)
                fw, fh = frame.size  # تحديث الأبعاد بعد تغيير الحجم

            # حساب المسافات
            mx, my = int(fw*0.05), int(fh*0.07)  # المسافة الأفقية 5%، المسافة الرأسية 7%
            qh = (fh-2*my)//per_page  # ارتفاع منطقة السؤال

            # تحميل الخط
            try:
                font_q = ImageFont.truetype("arial.ttf", int(qh*0.15))
            except:
                font_q = ImageFont.load_default()

            # إنشاء نسخة من الفريم للرسم عليها
            frame_copy = frame.copy()
            draw = ImageDraw.Draw(frame_copy)

            # إضافة اللوجو إذا كان موجوداً
            if hasattr(self, 'logo_image_path') and self.logo_image_path:
                try:
                    logo = Image.open(self.logo_image_path).convert("RGBA")

                    # ضغط صورة اللوجو مسبقًا إذا كانت كبيرة جدًا
                    logo_orig_width, logo_orig_height = logo.size
                    if logo_orig_width > 500:  # إذا كان عرض اللوجو أكبر من 500 بكسل
                        scale_factor = 500 / logo_orig_width
                        new_logo_width = int(logo_orig_width * scale_factor)
                        new_logo_height = int(logo_orig_height * scale_factor)
                        logo = logo.resize((new_logo_width, new_logo_height), Image.LANCZOS)

                    # تغيير حجم اللوجو ليناسب الفريم
                    logo_width = int(fw * 0.15)  # 15% من عرض الفريم (تقليل من 20% إلى 15%)
                    logo_height = int(logo.height * (logo_width / logo.width))
                    logo = logo.resize((logo_width, logo_height), Image.LANCZOS)

                    # وضع اللوجو في الزاوية العلوية اليمنى
                    frame_copy.paste(logo, (fw - logo_width - mx, my), logo)
                except Exception as e:
                    print(f"حدث خطأ أثناء إضافة اللوجو: {str(e)}")

            # إضافة صور الأسئلة
            for j, img_path in enumerate(page_images):
                try:
                    # تحميل صورة السؤال
                    q_img = Image.open(img_path).convert("RGB")

                    # ضغط صورة السؤال مسبقًا إذا كانت كبيرة جدًا
                    q_width_orig, q_height_orig = q_img.size
                    if q_width_orig > 1000:  # إذا كان عرض الصورة أكبر من 1000 بكسل
                        scale_factor = 1000 / q_width_orig
                        new_width_orig = int(q_width_orig * scale_factor)
                        new_height_orig = int(q_height_orig * scale_factor)
                        q_img = q_img.resize((new_width_orig, new_height_orig), Image.LANCZOS)

                    # حساب موضع السؤال
                    y_pos = my + j * qh

                    # تغيير حجم صورة السؤال لتناسب المساحة المخصصة
                    q_width = fw - 2 * mx
                    q_height = qh - 10  # ترك مساحة صغيرة بين الأسئلة

                    # الحفاظ على نسبة العرض إلى الارتفاع
                    img_ratio = q_img.width / q_img.height
                    if img_ratio > (q_width / q_height):
                        # الصورة أعرض من المساحة المتاحة
                        new_width = q_width
                        new_height = int(new_width / img_ratio)
                    else:
                        # الصورة أطول من المساحة المتاحة
                        new_height = q_height
                        new_width = int(new_height * img_ratio)

                    # تغيير حجم الصورة مع تحسين الجودة
                    q_img = q_img.resize((new_width, new_height), Image.LANCZOS)

                    # حساب موضع الصورة لتكون في وسط المساحة المخصصة
                    x_offset = mx + (q_width - new_width) // 2
                    y_offset = y_pos + (q_height - new_height) // 2

                    # لصق صورة السؤال على الفريم
                    frame_copy.paste(q_img, (x_offset, y_offset))

                    # إضافة رقم السؤال إذا كان ترقيم الأسئلة مفعلاً
                    if hasattr(self, 'numbering_var') and self.numbering_var.get():
                        question_num = i + j + 1
                        draw.text((mx, y_pos), f"{question_num}.", fill="black", font=font_q)
                except Exception as e:
                    print(f"حدث خطأ أثناء معالجة صورة السؤال {i+j+1}: {str(e)}")

            # ضغط الصفحة وتحويلها إلى JPEG بجودة متوسطة
            page_buffer = BytesIO()
            frame_copy = frame_copy.convert('RGB')  # تحويل إلى RGB لدعم تنسيق JPEG

            # تقليل حجم الصورة قبل الحفظ إذا كانت كبيرة جدًا
            frame_width, frame_height = frame_copy.size
            if frame_width > 1200:  # إذا كان عرض الصورة أكبر من 1200 بكسل
                scale_factor = 1200 / frame_width
                new_width = int(frame_width * scale_factor)
                new_height = int(frame_height * scale_factor)
                frame_copy = frame_copy.resize((new_width, new_height), Image.LANCZOS)

            # حفظ الصورة بجودة أقل مع تفعيل خيارات الضغط
            frame_copy.save(page_buffer, format='JPEG', quality=75, optimize=True, progressive=True, dpi=(image_dpi, image_dpi))
            page_buffer.seek(0)

            # إضافة الصفحة إلى ملف PDF
            c.drawImage(ImageReader(page_buffer), 0, 0, width=letter[0], height=letter[1])
            c.showPage()

        # إضافة غلاف الامتحان كصفحة أخيرة إذا كان الموضع هو "last"
        if hasattr(self, 'enable_cover_var') and self.enable_cover_var.get():
            if hasattr(self, 'cover_position_var') and self.cover_position_var.get() == "last":
                try:
                    if hasattr(self, '_cover_buffer'):
                        self._cover_buffer.seek(0)
                        c.drawImage(ImageReader(self._cover_buffer), 0, 0, width=letter[0], height=letter[1])
                        c.showPage()
                        # حذف المخزن المؤقت بعد الاستخدام
                        delattr(self, '_cover_buffer')
                except Exception as e:
                    print(f"حدث خطأ أثناء إضافة غلاف الامتحان كصفحة أخيرة: {str(e)}")

        # حفظ ملف PDF مع ضغط إضافي
        c.setPageCompression(1)  # تفعيل ضغط الصفحات
        c.save()

    def toggle_cover_settings(self):
        """تفعيل أو تعطيل إعدادات غلاف الامتحان بناءً على حالة الاختيار"""
        if hasattr(self, 'enable_cover_var'):
            if self.enable_cover_var.get():
                # تفعيل إعدادات الغلاف
                self.toggle_cover_type()  # تحديث نوع الغلاف المفعل
            else:
                # تعطيل جميع إعدادات الغلاف
                if hasattr(self, 'bubble_options_frame'):
                    self._set_frame_state(self.bubble_options_frame, "disabled")
                if hasattr(self, 'regular_options_frame'):
                    self._set_frame_state(self.regular_options_frame, "disabled")

    def toggle_cover_type(self):
        """تبديل بين نوعي الغلاف (بابل شيت أو غلاف عادي)"""
        if not hasattr(self, 'cover_type_var') or not hasattr(self, 'enable_cover_var'):
            return

        # التحقق من تفعيل الغلاف
        if not self.enable_cover_var.get():
            return

        # تحديد نوع الغلاف المفعل
        cover_type = self.cover_type_var.get()

        if cover_type == "bubble":
            # تفعيل إعدادات البابل شيت وتعطيل إعدادات الغلاف العادي
            if hasattr(self, 'bubble_options_frame'):
                self._set_frame_state(self.bubble_options_frame, "normal")
            if hasattr(self, 'regular_options_frame'):
                self._set_frame_state(self.regular_options_frame, "disabled")
        else:  # regular
            # تفعيل إعدادات الغلاف العادي وتعطيل إعدادات البابل شيت
            if hasattr(self, 'bubble_options_frame'):
                self._set_frame_state(self.bubble_options_frame, "disabled")
            if hasattr(self, 'regular_options_frame'):
                self._set_frame_state(self.regular_options_frame, "normal")

    def toggle_watermark_settings(self):
        """تفعيل أو تعطيل إعدادات العلامة المائية"""
        if hasattr(self, 'enable_watermark_var') and hasattr(self, 'watermark_options_frame'):
            if self.enable_watermark_var.get():
                # تفعيل إعدادات العلامة المائية
                self._set_frame_state(self.watermark_options_frame, "normal")
            else:
                # تعطيل إعدادات العلامة المائية
                self._set_frame_state(self.watermark_options_frame, "disabled")

    def toggle_encryption_settings(self):
        """تفعيل أو تعطيل إعدادات تشفير PDF"""
        if hasattr(self, 'enable_encryption_var') and hasattr(self, 'encryption_options_frame'):
            if self.enable_encryption_var.get():
                # تفعيل إعدادات تشفير PDF
                self._set_frame_state(self.encryption_options_frame, "normal")
            else:
                # تعطيل إعدادات تشفير PDF
                self._set_frame_state(self.encryption_options_frame, "disabled")

    def toggle_random_models_settings(self):
        """تفعيل أو تعطيل إعدادات توليد النماذج العشوائية"""
        if hasattr(self, 'enable_random_models_var') and hasattr(self, 'random_models_options_frame'):
            if self.enable_random_models_var.get():
                # تفعيل إعدادات توليد النماذج العشوائية
                self._set_frame_state(self.random_models_options_frame, "normal")
            else:
                # تعطيل إعدادات توليد النماذج العشوائية
                self._set_frame_state(self.random_models_options_frame, "disabled")

    def toggle_backup_settings(self):
        """تفعيل أو تعطيل إعدادات النسخ الاحتياطي"""
        if hasattr(self, 'auto_backup_var') and hasattr(self, 'backup_options_frame'):
            # الحصول على جميع الإطارات الفرعية
            backup_frames = []
            for widget in self.backup_options_frame.winfo_children():
                if isinstance(widget, ctk.CTkFrame) and widget != self.auto_backup_var:
                    backup_frames.append(widget)

            if self.auto_backup_var.get():
                # تفعيل إعدادات النسخ الاحتياطي
                for frame in backup_frames:
                    self._set_frame_state(frame, "normal")
            else:
                # تعطيل إعدادات النسخ الاحتياطي
                for frame in backup_frames:
                    self._set_frame_state(frame, "disabled")

    def select_backup_folder(self):
        """اختيار مجلد النسخ الاحتياطي"""
        folder_path = filedialog.askdirectory(
            title="اختيار مجلد النسخ الاحتياطي"
        )

        if folder_path:
            self.backup_folder_var.set(folder_path)
            self.status.configure(text=f"تم اختيار مجلد النسخ الاحتياطي: {folder_path}")
            # حفظ الإعدادات بعد اختيار المجلد
            self.save_settings()

    def _set_frame_state(self, frame, state):
        """تعيين حالة الإطار وجميع العناصر الفرعية"""
        try:
            frame.configure(state=state)
        except:
            pass

        for widget in frame.winfo_children():
            try:
                if "state" in widget.configure():
                    widget.configure(state=state)
            except:
                pass

            # تعيين حالة العناصر الفرعية
            if hasattr(widget, "winfo_children"):
                for child in widget.winfo_children():
                    try:
                        if "state" in child.configure():
                            child.configure(state=state)
                    except:
                        pass

    def preview_exam_cover(self):
        """معاينة غلاف الامتحان"""
        if not self.enable_cover_var.get():
            return self.status.configure(text="يجب تفعيل غلاف الامتحان أولاً.")

        # تحديد نوع الغلاف
        cover_type = self.cover_type_var.get() if hasattr(self, 'cover_type_var') else "bubble"

        try:
            # الحصول على عدد الأسئلة وعدد الخيارات
            if cover_type == "bubble":
                # التحقق من وجود إعدادات البابل شيت
                if not hasattr(self, 'questions_count_entry') or not hasattr(self, 'options_var'):
                    return self.status.configure(text="لم يتم تهيئة إعدادات البابل شيت بشكل صحيح.")

                questions_count = int(self.questions_count_entry.get())
                options_count = int(self.options_var.get())

                if not (1 <= questions_count <= 100):
                    raise ValueError("عدد الأسئلة يجب أن يكون بين 1 و 100")
            else:
                # الغلاف العادي لا يحتاج إلى عدد أسئلة أو خيارات
                questions_count = 0
                options_count = 0

            # تحديث حالة التطبيق
            self.status.configure(text="جارٍ إنشاء غلاف الامتحان...")
            self.progress_bar.set(0.1)
            self.update_idletasks()

            # إنشاء صورة غلاف الامتحان
            cover_image = self.create_exam_cover(questions_count, options_count)

            # ضغط صورة الغلاف وحفظها مؤقتًا
            temp_dir = tempfile.gettempdir()
            preview_path = os.path.join(temp_dir, "exam_cover_preview.jpg")

            # تقليل حجم الصورة قبل الحفظ
            cover_width, cover_height = cover_image.size
            if cover_width > 1000:  # إذا كان عرض الصورة أكبر من 1000 بكسل
                scale_factor = 1000 / cover_width
                new_width = int(cover_width * scale_factor)
                new_height = int(cover_height * scale_factor)
                cover_image = cover_image.resize((new_width, new_height), Image.LANCZOS)

            # حفظ الصورة بتنسيق JPEG بجودة متوسطة
            cover_image = cover_image.convert('RGB')  # تحويل إلى RGB لدعم تنسيق JPEG
            cover_image.save(preview_path, format='JPEG', quality=85, optimize=True, progressive=True)

            # فتح الصورة في المعاين الافتراضي
            os.startfile(preview_path)

            # تحديث حالة التطبيق
            self.status.configure(text="تم إنشاء معاينة غلاف الامتحان بنجاح.")
            self.progress_bar.set(1.0)

        except ValueError as e:
            self.progress_bar.set(0)
            self.status.configure(text=f"خطأ: {str(e)}")
        except Exception as e:
            self.progress_bar.set(0)
            self.status.configure(text=f"خطأ أثناء إنشاء معاينة غلاف الامتحان: {str(e)}")

    def create_exam_cover(self, questions_count, options_count):
        """إنشاء صورة غلاف الامتحان (البابل شيت أو الغلاف العادي)"""
        # إنشاء صورة بيضاء بحجم A4 بدقة أقل لتقليل حجم الملف
        width, height = 1654, 2339  # حجم A4 بدقة 200 DPI (بدلاً من 300 DPI)
        image = Image.new('RGB', (width, height), 'white')
        draw = ImageDraw.Draw(image)

        # تحميل الخطوط
        try:
            title_font = ImageFont.truetype("arial.ttf", 60)
            header_font = ImageFont.truetype("arial.ttf", 40)
            label_font = ImageFont.truetype("arial.ttf", 30)
            option_font = ImageFont.truetype("arial.ttf", 25)
        except:
            # استخدام الخط الافتراضي إذا لم يتم العثور على الخط
            title_font = ImageFont.load_default()
            header_font = ImageFont.load_default()
            label_font = ImageFont.load_default()
            option_font = ImageFont.load_default()

        # تحديد نوع الغلاف
        cover_type = "bubble"  # القيمة الافتراضية
        if hasattr(self, 'cover_type_var'):
            cover_type = self.cover_type_var.get()

        # الحصول على العنوان المخصص
        custom_title = "ورقة الإجابة"  # العنوان الافتراضي
        if hasattr(self, 'custom_title_var'):
            custom_title = self.custom_title_var.get()

        # رسم العنوان
        title_bbox = draw.textbbox((0, 0), custom_title, font=title_font)
        title_width = title_bbox[2] - title_bbox[0]
        draw.text(((width - title_width) // 2, 100), custom_title, fill="black", font=title_font)

        # الحصول على معلومات الامتحان حسب نوع الغلاف
        if cover_type == "bubble":
            # معلومات البابل شيت
            course_name = self.course_name_entry.get() if hasattr(self, 'course_name_entry') else ""
            teacher_name = self.teacher_name_entry.get() if hasattr(self, 'teacher_name_entry') else ""
            exam_duration = self.exam_duration_entry.get() if hasattr(self, 'exam_duration_entry') else "60"
            total_score = self.total_score_entry.get() if hasattr(self, 'total_score_entry') else "100"
            instructions = self.exam_instructions_var.get() if hasattr(self, 'exam_instructions_var') else "قم بتظليل الدائرة المقابلة للإجابة الصحيحة لكل سؤال"
        else:
            # معلومات الغلاف العادي
            course_name = self.regular_course_name_entry.get() if hasattr(self, 'regular_course_name_entry') else ""
            teacher_name = self.regular_teacher_name_entry.get() if hasattr(self, 'regular_teacher_name_entry') else ""
            exam_duration = self.regular_exam_duration_entry.get() if hasattr(self, 'regular_exam_duration_entry') else "60"
            total_score = self.regular_total_score_entry.get() if hasattr(self, 'regular_total_score_entry') else "100"
            instructions = self.regular_exam_instructions_var.get() if hasattr(self, 'regular_exam_instructions_var') else "يرجى الإجابة على جميع الأسئلة"

        # رسم معلومات المادة
        if course_name:
            course_text = f"المادة: {course_name}"
            course_bbox = draw.textbbox((0, 0), course_text, font=header_font)
            course_width = course_bbox[2] - course_bbox[0]
            draw.text(((width - course_width) // 2, 180), course_text, fill="black", font=header_font)

        # رسم مستطيل معلومات الطالب في الأعلى
        draw.rectangle([(200, 250), (width - 200, 450)], outline="black", width=2)
        draw.text((width - 350, 300), "اسم الطالب:", fill="black", font=header_font)
        draw.line([(200, 350), (width - 200, 350)], fill="black", width=2)
        draw.text((width - 350, 380), "التاريخ:", fill="black", font=header_font)

        # إضافة معلومات المدرس ومدة الامتحان والدرجة الكلية
        if teacher_name:
            teacher_text = f"المدرس: {teacher_name}"
            draw.text((200, 480), teacher_text, fill="black", font=header_font)

        duration_text = f"مدة الامتحان: {exam_duration} دقيقة"
        draw.text((200, 530), duration_text, fill="black", font=header_font)

        score_text = f"الدرجة الكلية: {total_score}"
        draw.text((width - 500, 530), score_text, fill="black", font=header_font)

        # إذا كان نوع الغلاف هو البابل شيت، قم برسم الفقاعات
        if cover_type == "bubble":
            # تحديد عدد الأسئلة في الصفحة الواحدة
            max_questions_per_page = 30

            # حساب عدد الصفحات المطلوبة
            pages_count = (questions_count + max_questions_per_page - 1) // max_questions_per_page

            # إذا كان عدد الأسئلة أكبر من 30، سنقوم بإنشاء صفحات متعددة
            if pages_count > 1:
                # إنشاء قائمة لتخزين جميع الصفحات
                all_pages = []

                # إنشاء الصفحة الأولى
                all_pages.append(image)

                # إنشاء الصفحات الإضافية
                for page in range(1, pages_count):
                    # إنشاء صفحة جديدة
                    page_image = Image.new('RGB', (width, height), 'white')
                    page_draw = ImageDraw.Draw(page_image)

                    # إضافة عنوان الصفحة
                    page_title = f"{custom_title} - صفحة {page + 1}"
                    page_title_bbox = page_draw.textbbox((0, 0), page_title, font=title_font)
                    page_title_width = page_title_bbox[2] - page_title_bbox[0]
                    page_draw.text(((width - page_title_width) // 2, 100), page_title, fill="black", font=title_font)

                    all_pages.append(page_image)

                # رسم الفقاعات على كل صفحة
                for page in range(pages_count):
                    # تحديد نطاق الأسئلة لهذه الصفحة
                    start_question = page * max_questions_per_page
                    end_question = min((page + 1) * max_questions_per_page, questions_count)
                    page_questions_count = end_question - start_question

                    # الحصول على الصفحة الحالية والرسام
                    current_image = all_pages[page]
                    current_draw = ImageDraw.Draw(current_image)

                    # رسم الفقاعات لهذه الصفحة
                    self._draw_bubbles(current_draw, width, start_question, page_questions_count, options_count, label_font, option_font)

                    # إضافة تعليمات في أسفل الصفحة
                    instructions_bbox = current_draw.textbbox((0, 0), instructions, font=header_font)
                    instructions_width = instructions_bbox[2] - instructions_bbox[0]
                    current_draw.text(((width - instructions_width) // 2, height - 200), instructions, fill="black", font=header_font)

                    # إضافة معلومات المطور
                    developer_info = "المهندس يوسف غنام"
                    developer_bbox = current_draw.textbbox((0, 0), developer_info, font=label_font)
                    developer_width = developer_bbox[2] - developer_bbox[0]
                    current_draw.text(((width - developer_width) // 2, height - 100), developer_info, fill="black", font=label_font)

                # دمج جميع الصفحات في ملف PDF
                temp_dir = tempfile.gettempdir()
                pdf_path = os.path.join(temp_dir, "exam_cover_multi_page.pdf")

                # حفظ الصفحة الأولى
                all_pages[0].save(pdf_path, save_all=True, append_images=all_pages[1:])

                # إعادة الصفحة الأولى فقط (سيتم استخدام ملف PDF للمعاينة)
                return all_pages[0]
            else:
                # رسم الفقاعات على صفحة واحدة
                self._draw_bubbles(draw, width, 0, questions_count, options_count, label_font, option_font)

        # إضافة تعليمات في أسفل الصفحة
        instructions_bbox = draw.textbbox((0, 0), instructions, font=header_font)
        instructions_width = instructions_bbox[2] - instructions_bbox[0]
        draw.text(((width - instructions_width) // 2, height - 200), instructions, fill="black", font=header_font)

        # إضافة معلومات المطور
        developer_info = "المهندس يوسف غنام"
        developer_bbox = draw.textbbox((0, 0), developer_info, font=label_font)
        developer_width = developer_bbox[2] - developer_bbox[0]
        draw.text(((width - developer_width) // 2, height - 100), developer_info, fill="black", font=label_font)

        return image

    def _draw_bubbles(self, draw, width, start_question, questions_count, options_count, label_font, option_font):
        """رسم فقاعات الأسئلة على الصفحة"""
        # حساب المسافات لرسم الفقاعات
        margin_top = 600
        margin_left = 200
        bubble_size = 35  # تكبير حجم الفقاعات
        bubble_spacing = 25  # زيادة المسافة بين الفقاعات

        # تحديد عدد الأسئلة في العمود الواحد (بحد أقصى 15 سؤال)
        questions_per_column = min(15, questions_count)

        # تحديد عدد الأعمدة المطلوبة
        columns_count = (questions_count + questions_per_column - 1) // questions_per_column

        # حساب عرض العمود الواحد
        column_width = (width - 2 * margin_left) // columns_count

        # رسم الفقاعات لكل سؤال
        for q in range(questions_count):
            # تحديد العمود والصف
            col = q // questions_per_column
            row = q % questions_per_column

            # حساب موضع السؤال
            question_num = start_question + q + 1
            y_pos = margin_top + row * (bubble_size + bubble_spacing * 2)
            x_pos = margin_left + col * column_width

            # رسم رقم السؤال
            question_text = f"{question_num}."
            draw.text((x_pos, y_pos + bubble_size // 2 - 10), question_text, fill="black", font=label_font)

            # رسم الخيارات (أ، ب، ج، د، هـ) بدلاً من (A, B, C, D, E)
            option_chars = "أبجدهـ"[:options_count]
            for i, option in enumerate(option_chars):
                option_x = x_pos + 50 + i * (bubble_size + bubble_spacing)

                # رسم الفقاعة
                draw.ellipse([(option_x, y_pos), (option_x + bubble_size, y_pos + bubble_size)], outline="black", width=2)

                # رسم حرف الخيار
                draw.text((option_x + bubble_size + 5, y_pos), option, fill="black", font=option_font)

    def grid_config(self):
        for i in range(5):
            self.grid_rowconfigure(i, weight=1)
        for j in range(2):
            self.grid_columnconfigure(j, weight=1)

    def select_questions(self):
        self.question_images = list(fd.askopenfilenames(
            title="اختر صور الأسئلة", filetypes=[("Images","*.png *.jpg *.jpeg")]))
        self.status.configure(text=f"تم اختيار {len(self.question_images)} صورة.")

    def select_frame(self):
        self.frame_image_path = fd.askopenfilename(
            title="اختر صورة الفريم", filetypes=[("Images","*.png *.jpg *.jpeg")])
        self.status.configure(text="تم اختيار صورة الفريم.")

    def select_logo(self):
        self.logo_image_path = fd.askopenfilename(
            title="اختر صورة اللوجو", filetypes=[("Images","*.png *.jpg *.jpeg")])
        if self.logo_image_path:
            self.status.configure(text="تم اختيار صورة اللوجو.")
            # تفعيل زر إزالة اللوجو
            self.remove_logo_button.configure(state="normal")
        else:
            self.status.configure(text="لم يتم اختيار صورة اللوجو.")

    def remove_logo(self):
        """إزالة اللوجو المختار"""
        if hasattr(self, 'logo_image_path') and self.logo_image_path:
            self.logo_image_path = None
            self.status.configure(text="تم إزالة اللوجو بنجاح.")
            # تعطيل زر إزالة اللوجو
            self.remove_logo_button.configure(state="disabled")

            # عرض رسالة تأكيد
            try:
                ctk.CTkMessagebox(
                    title="إزالة اللوجو",
                    message="تم إزالة اللوجو بنجاح",
                    icon="info",
                    option_1="موافق"
                )
            except:
                pass
        else:
            self.status.configure(text="لا يوجد لوجو لإزالته.")

    def toggle_cover_settings(self):
        """تفعيل أو تعطيل إعدادات غلاف الامتحان"""
        if hasattr(self, 'bubble_options_frame') and hasattr(self, 'enable_cover_var'):
            if self.enable_cover_var.get():
                # تفعيل إعدادات غلاف الامتحان
                for widget in self.bubble_options_frame.winfo_children():
                    if isinstance(widget, ctk.CTkFrame):
                        for child in widget.winfo_children():
                            try:
                                # التحقق مما إذا كان العنصر يدعم خاصية state
                                if hasattr(child, 'configure') and not isinstance(child, ctk.CTkFrame) and not isinstance(child, ctk.CTkLabel):
                                    child.configure(state="normal")
                            except Exception:
                                # تجاهل الأخطاء إذا كان العنصر لا يدعم خاصية state
                                pass
            else:
                # تعطيل إعدادات غلاف الامتحان
                for widget in self.bubble_options_frame.winfo_children():
                    if isinstance(widget, ctk.CTkFrame):
                        for child in widget.winfo_children():
                            try:
                                # التحقق مما إذا كان العنصر يدعم خاصية state
                                if hasattr(child, 'configure') and not isinstance(child, ctk.CTkFrame) and not isinstance(child, ctk.CTkLabel):
                                    child.configure(state="disabled")
                            except Exception:
                                # تجاهل الأخطاء إذا كان العنصر لا يدعم خاصية state
                                pass

    def preview_exam_cover(self):
        """معاينة غلاف الامتحان (البابل شيت)"""
        if not self.enable_cover_var.get():
            return self.status.configure(text="يجب تفعيل غلاف الامتحان أولاً.")

        try:
            # الحصول على عدد الأسئلة وعدد الخيارات
            questions_count = int(self.questions_count_entry.get())
            options_count = int(self.options_var.get())

            if not (1 <= questions_count <= 100):
                raise ValueError("عدد الأسئلة يجب أن يكون بين 1 و 100")

            # إنشاء صورة غلاف الامتحان
            self.status.configure(text="جارٍ إنشاء غلاف الامتحان...")
            self.progress_bar.set(0.1)
            self.update_idletasks()

            # إنشاء صورة غلاف الامتحان
            cover_image = self.create_exam_cover(questions_count, options_count)
            self.exam_cover_image = cover_image

            # حفظ الصورة مؤقتًا وعرضها
            import tempfile
            import os

            temp_dir = tempfile.gettempdir()
            preview_path = os.path.join(temp_dir, "exam_cover_preview.pdf")

            # حفظ الصورة كملف PDF
            cover_image.save(preview_path)

            # فتح الملف للمعاينة
            self.status.configure(text="تم فتح معاينة غلاف الامتحان.")
            self.progress_bar.set(1.0)

            # فتح ملف PDF باستخدام البرنامج الافتراضي
            os.startfile(preview_path)

        except ValueError as e:
            self.progress_bar.set(0)
            self.status.configure(text=f"خطأ: {str(e)}")
        except Exception as e:
            self.progress_bar.set(0)
            self.status.configure(text=f"خطأ أثناء إنشاء غلاف الامتحان: {e}")

    def generate_pdf(self):
        if not self.question_images or not self.frame_image_path:
            return self.status.configure(text="اختر صور الأسئلة وصورة الفريم أولاً.")
        try:
            per_page = int(self.num_per_page_entry.get())
            if not (1 <= per_page <= 20):
                raise ValueError
        except ValueError:
            return self.status.configure(text="أدخل عددًا صحيحًا بين 1 و 20.")

        self.status.configure(text="جارٍ إنشاء PDF...")
        self.progress_bar.set(0.05)  # Start progress
        self.update_idletasks()

        try:
            # التحقق من تفعيل توليد النماذج العشوائية
            if hasattr(self, 'enable_random_models_var') and self.enable_random_models_var.get():
                # الحصول على عدد النماذج
                models_count = int(self.models_count_var.get()) if hasattr(self, 'models_count_var') else 2

                # اختيار مسار حفظ الملف
                save = fd.asksaveasfilename(defaultextension=".pdf", filetypes=[("PDF","*.pdf")])
                if not save:
                    self.progress_bar.set(0)  # Reset progress
                    self.status.configure(text="تم إلغاء حفظ ملف PDF.")
                    return

                # إنشاء نماذج متعددة
                base_path, ext = os.path.splitext(save)

                for i in range(models_count):
                    model_path = f"{base_path}_نموذج_{i+1}{ext}"

                    # تحديث حالة التطبيق
                    self.status.configure(text=f"جارٍ إنشاء النموذج {i+1} من {models_count}...")
                    self.progress_bar.set((i + 0.5) / models_count)
                    self.update_idletasks()

                    # إنشاء نموذج PDF
                    self.generate_pdf_file(model_path, model_number=i+1)

                    # حفظ مسار آخر ملف
                    if i == models_count - 1:
                        self.last_pdf_path = model_path

                # تحديث حالة التطبيق
                self.status.configure(text=f"تم إنشاء {models_count} نماذج PDF بنجاح")
                self.progress_bar.set(1.0)

                # فتح المجلد الذي يحتوي على الملفات
                os.startfile(os.path.dirname(save))
            else:
                # اختيار مسار حفظ الملف
                save = fd.asksaveasfilename(defaultextension=".pdf", filetypes=[("PDF","*.pdf")])
                if not save:
                    self.progress_bar.set(0)  # Reset progress
                    self.status.configure(text="تم إلغاء حفظ ملف PDF.")
                    return

                # إنشاء ملف PDF واحد
                self.generate_pdf_file(save)

                # تحديث حالة التطبيق
                self.status.configure(text=f"تم إنشاء ملف PDF بنجاح: {save}")
                self.progress_bar.set(1.0)
                self.last_pdf_path = save

                # فتح الملف بعد الإنشاء
                os.startfile(save)
        except Exception as e:
            self.progress_bar.set(0)  # Reset progress
            self.status.configure(text=f"حدث خطأ أثناء إنشاء PDF: {str(e)}")

    def generate_pdf_file(self, output_path, model_number=None):
        """إنشاء ملف PDF وحفظه في المسار المحدد"""
        try:
            per_page = int(self.num_per_page_entry.get())

            # Load frame image
            frame = Image.open(self.frame_image_path).convert("RGB")
            fw, fh = frame.size
            mx, my = int(fw*0.05), int(fh*0.07)  # المسافة الأفقية 5%، المسافة الرأسية 7%
            qh = (fh-2*my)//per_page
            font_q = ImageFont.truetype("arial.ttf", int(qh*0.15))
            font_p = ImageFont.truetype("arial.ttf", int(fh*0.035))

            self.progress_bar.set(0.1)  # Update progress
            self.status.configure(text="جارٍ تحضير الصفحات...")
            self.update_idletasks()

            # إذا كان هناك رقم نموذج، قم بترتيب الأسئلة عشوائيًا
            question_images = self.question_images.copy()
            if model_number is not None:
                # تعيين بذرة عشوائية ثابتة لكل نموذج للحصول على نفس الترتيب في كل مرة
                random.seed(model_number * 100)
                # ترتيب الأسئلة عشوائيًا
                random.shuffle(question_images)

            # Create pages
            pages = []
            total_pages = (len(question_images) + per_page - 1) // per_page
            for i in range(0, len(question_images), per_page):
                pg = frame.copy(); draw=ImageDraw.Draw(pg)
                for j, path in enumerate(question_images[i:i+per_page]):
                    qi = Image.open(path).resize((fw-2*mx, qh-10))
                    y= my + j*qh +5; pg.paste(qi,(mx,y))
                    if self.numbering_var.get():
                        draw.text((mx+fw-2*mx+10, y+qh//2-font_q.size//2),
                                  str(i+j+1), fill="black", font=font_q)
                    if j<per_page-1:
                        draw.line([(mx, y+qh-5),(fw-mx, y+qh-5)], fill="black", width=2)
                # رقم الصفحة
                pn=str(i//per_page+1)
                tb=draw.textbbox((0,0), pn, font=font_p)
                tw,th=tb[2]-tb[0], tb[3]-tb[1]
                draw.text(((fw-tw)//2, fh-my//2-th//2), pn, fill="black", font=font_p)

                # إضافة رقم النموذج إذا كان موجودًا
                if model_number is not None:
                    model_text = f"نموذج {model_number}"
                    model_tb = draw.textbbox((0,0), model_text, font=font_p)
                    model_tw, model_th = model_tb[2]-model_tb[0], model_tb[3]-model_tb[1]
                    draw.text((fw-model_tw-20, 20), model_text, fill="black", font=font_p)

                pages.append(pg)

                # Update progress based on page completion
                progress = 0.1 + 0.6 * ((i // per_page + 1) / total_pages)
                self.progress_bar.set(progress)
                self.status.configure(text=f"جارٍ إنشاء الصفحة {i//per_page+1} من {total_pages}...")
                self.update_idletasks()

            # Add logo if available
            if self.logo_image_path:
                self.status.configure(text="جارٍ إضافة اللوجو...")
                self.progress_bar.set(0.8)  # Update progress
                self.update_idletasks()

                lg = Image.open(self.logo_image_path).convert("RGBA")
                lw,lh=lg.size
                ar=min((fw-2*mx)/lw,(fh-2*my)/lh)
                lg=lg.resize((int(lw*ar),int(lh*ar)))
                alpha=lg.split()[3].point(lambda p: p*0.5); lg.putalpha(alpha)
                for pg in pages:
                    xo=my+(fw-2*mx-lg.width)//2; yo=my+(fh-2*my-lg.height)//2
                    pg.paste(lg,(xo,yo),lg)

            # إضافة العلامة المائية إذا كانت مفعلة
            if hasattr(self, 'enable_watermark_var') and self.enable_watermark_var.get():
                self.status.configure(text="جارٍ إضافة العلامة المائية...")
                self.progress_bar.set(0.82)  # Update progress
                self.update_idletasks()

                # الحصول على نص العلامة المائية
                watermark_text = self.watermark_text_var.get() if hasattr(self, 'watermark_text_var') else "سري"

                # الحصول على لون العلامة المائية
                watermark_color = self.watermark_color_var.get() if hasattr(self, 'watermark_color_var') else "رمادي"
                color_map = {
                    "رمادي": (128, 128, 128),
                    "أحمر": (255, 0, 0),
                    "أزرق": (0, 0, 255),
                    "أخضر": (0, 128, 0)
                }
                color = color_map.get(watermark_color, (128, 128, 128))

                # الحصول على شفافية العلامة المائية
                opacity = self.watermark_opacity_var.get() if hasattr(self, 'watermark_opacity_var') else 0.3

                # إضافة العلامة المائية إلى كل صفحة
                watermark_font = ImageFont.truetype("arial.ttf", int(fh*0.1))
                for pg in pages:
                    # إنشاء صورة شفافة للعلامة المائية
                    watermark = Image.new('RGBA', pg.size, (0, 0, 0, 0))
                    wm_draw = ImageDraw.Draw(watermark)

                    # حساب حجم النص
                    wm_bbox = wm_draw.textbbox((0, 0), watermark_text, font=watermark_font)
                    wm_width, wm_height = wm_bbox[2] - wm_bbox[0], wm_bbox[3] - wm_bbox[1]

                    # رسم النص بزاوية 45 درجة
                    watermark = watermark.rotate(45, expand=True)
                    wm_draw = ImageDraw.Draw(watermark)

                    # رسم النص في وسط الصورة
                    wm_draw.text(
                        ((watermark.width - wm_width) // 2, (watermark.height - wm_height) // 2),
                        watermark_text,
                        fill=(*color, int(255 * opacity)),
                        font=watermark_font
                    )

                    # تحجيم العلامة المائية لتناسب الصفحة
                    watermark = watermark.resize(pg.size)

                    # دمج العلامة المائية مع الصفحة
                    pg.paste(watermark, (0, 0), watermark)

            # إضافة غلاف الامتحان إذا كان مفعلاً
            if hasattr(self, 'enable_cover_var') and self.enable_cover_var.get():
                self.status.configure(text="جارٍ إضافة غلاف الامتحان...")
                self.progress_bar.set(0.85)  # Update progress
                self.update_idletasks()

                # إنشاء غلاف الامتحان إذا لم يكن موجودًا
                if not hasattr(self, 'exam_cover_image') or self.exam_cover_image is None:
                    try:
                        questions_count = int(self.questions_count_entry.get())
                        options_count = int(self.options_var.get())
                        self.exam_cover_image = self.create_exam_cover(questions_count, options_count)
                    except Exception as e:
                        self.status.configure(text=f"خطأ أثناء إنشاء غلاف الامتحان: {e}")
                        # استمر بدون غلاف الامتحان
                        pass

                # إضافة غلاف الامتحان إلى الصفحات
                if hasattr(self, 'exam_cover_image') and self.exam_cover_image:
                    # إضافة رقم النموذج إلى غلاف الامتحان إذا كان موجودًا
                    if model_number is not None:
                        cover_copy = self.exam_cover_image.copy()
                        cover_draw = ImageDraw.Draw(cover_copy)
                        model_text = f"نموذج {model_number}"
                        model_font = ImageFont.truetype("arial.ttf", 40)
                        model_tb = cover_draw.textbbox((0,0), model_text, font=model_font)
                        model_tw, model_th = model_tb[2]-model_tb[0], model_tb[3]-model_tb[1]
                        cover_draw.text((fw-model_tw-50, 50), model_text, fill="black", font=model_font)
                        cover_image = cover_copy
                    else:
                        cover_image = self.exam_cover_image

                    cover_position = self.cover_position_var.get()
                    if cover_position == "first":
                        # إضافة غلاف الامتحان كصفحة أولى
                        pages.insert(0, cover_image)
                    else:
                        # إضافة غلاف الامتحان كصفحة أخيرة
                        pages.append(cover_image)

            self.progress_bar.set(0.9)  # Update progress
            self.status.configure(text="جارٍ حفظ الملف...")
            self.update_idletasks()

            # حفظ الملف PDF
            if hasattr(self, 'enable_encryption_var') and self.enable_encryption_var.get():
                # حفظ الملف مع التشفير
                from reportlab.pdfgen import canvas
                from reportlab.lib.pagesizes import letter
                from io import BytesIO

                # الحصول على كلمة المرور
                password = self.pdf_password_var.get() if hasattr(self, 'pdf_password_var') else ""

                # حفظ الصور في ملفات مؤقتة
                temp_files = []
                for i, page in enumerate(pages):
                    temp_file = os.path.join(tempfile.gettempdir(), f"temp_page_{i}.png")
                    page.save(temp_file)
                    temp_files.append(temp_file)

                # إنشاء ملف PDF مشفر
                from PyPDF2 import PdfWriter, PdfReader

                # إنشاء ملف PDF مؤقت
                temp_pdf = os.path.join(tempfile.gettempdir(), "temp_pdf.pdf")
                pages[0].save(temp_pdf, save_all=True, append_images=pages[1:])

                # قراءة الملف المؤقت وإضافة التشفير
                reader = PdfReader(temp_pdf)
                writer = PdfWriter()

                # نسخ جميع الصفحات
                for page in reader.pages:
                    writer.add_page(page)

                # إضافة التشفير
                writer.encrypt(password)

                # حفظ الملف النهائي
                with open(output_path, "wb") as f:
                    writer.write(f)

                # حذف الملفات المؤقتة
                os.remove(temp_pdf)
                for temp_file in temp_files:
                    try:
                        os.remove(temp_file)
                    except:
                        pass
            else:
                # حفظ الملف بدون تشفير
                pages[0].save(output_path, save_all=True, append_images=pages[1:])

            return True
        except Exception as e:
            self.status.configure(text=f"خطأ أثناء إنشاء PDF: {e}")
            return False

    def preview_pdf(self):
        """Preview the PDF before creating it."""
        if not self.question_images or not self.frame_image_path:
            return self.status.configure(text="اختر صور الأسئلة وصورة الفريم أولاً.")
        try:
            per_page = int(self.num_per_page_entry.get())
            if not (1 <= per_page <= 20):
                raise ValueError
        except ValueError:
            return self.status.configure(text="أدخل عددًا صحيحًا بين 1 و 20.")

        self.status.configure(text="جارٍ إنشاء معاينة...")
        self.progress_bar.set(0.05)  # Start progress
        self.update_idletasks()

        try:
            # Load frame image
            frame = Image.open(self.frame_image_path).convert("RGB")
            fw, fh = frame.size
            mx, my = int(fw*0.05), int(fh*0.07)  # المسافة الأفقية 5%، المسافة الرأسية 7%
            qh = (fh-2*my)//per_page
            font_q = ImageFont.truetype("arial.ttf", int(qh*0.15))

            # Create just the first page for preview
            pg = frame.copy(); draw=ImageDraw.Draw(pg)
            for j, path in enumerate(self.question_images[:per_page]):
                qi = Image.open(path).resize((fw-2*mx, qh-10))
                y= my + j*qh +5; pg.paste(qi,(mx,y))
                if self.numbering_var.get():
                    draw.text((mx+fw-2*mx+10, y+qh//2-font_q.size//2),
                              str(j+1), fill="black", font=font_q)
                if j<per_page-1:
                    draw.line([(mx, y+qh-5),(fw-mx, y+qh-5)], fill="black", width=2)

            # Add logo if available
            if self.logo_image_path:
                lg = Image.open(self.logo_image_path).convert("RGBA")
                lw,lh=lg.size
                ar=min((fw-2*mx)/lw,(fh-2*my)/lh)
                lg=lg.resize((int(lw*ar),int(lh*ar)))
                alpha=lg.split()[3].point(lambda p: p*0.5); lg.putalpha(alpha)
                xo=my+(fw-2*mx-lg.width)//2; yo=my+(fh-2*my-lg.height)//2
                pg.paste(lg,(xo,yo),lg)

            # Save temporary file
            import tempfile
            import os

            temp_dir = tempfile.gettempdir()
            preview_path = os.path.join(temp_dir, "preview.pdf")

            # إضافة غلاف الامتحان إذا كان مفعلاً
            if hasattr(self, 'enable_cover_var') and self.enable_cover_var.get():
                self.status.configure(text="جارٍ إضافة غلاف الامتحان للمعاينة...")
                self.progress_bar.set(0.7)
                self.update_idletasks()

                # إنشاء غلاف الامتحان إذا لم يكن موجوداً
                if not hasattr(self, 'exam_cover_image') or self.exam_cover_image is None:
                    try:
                        questions_count = int(self.questions_count_entry.get())
                        options_count = int(self.options_var.get())
                        self.exam_cover_image = self.create_exam_cover(questions_count, options_count)
                    except Exception as e:
                        self.status.configure(text=f"خطأ أثناء إنشاء غلاف الامتحان: {e}")
                        # استمر بدون غلاف الامتحان
                        pass

                # إضافة غلاف الامتحان إلى المعاينة
                if hasattr(self, 'exam_cover_image') and self.exam_cover_image:
                    cover_position = self.cover_position_var.get()
                    if cover_position == "first":
                        # حفظ غلاف الامتحان كصفحة أولى
                        self.exam_cover_image.save(preview_path, save_all=True, append_images=[pg])
                    else:
                        # حفظ صفحة المعاينة أولاً ثم غلاف الامتحان
                        pg.save(preview_path, save_all=True, append_images=[self.exam_cover_image])
                else:
                    # حفظ صفحة المعاينة فقط
                    pg.save(preview_path)
            else:
                # حفظ صفحة المعاينة فقط
                pg.save(preview_path)

            # Open the preview
            self.status.configure(text="تم فتح المعاينة.")
            self.progress_bar.set(1.0)

            # Open the PDF with the default PDF viewer
            os.startfile(preview_path)  # Windows-specific function

        except Exception as e:
            self.progress_bar.set(0)  # Reset progress
            self.status.configure(text=f"خطأ أثناء إنشاء المعاينة: {e}")

    def toggle_app_theme(self):
        """Toggle between themes: Red, Green, Dark, Light."""
        # تحديد الثيم الحالي من خلال لون الخلفية
        current_bg_color = self.cget("fg_color")

        # تحديد الثيم التالي
        if current_bg_color == COLORS["red_bg"]:
            # تغيير من الثيم الأحمر إلى الثيم الأخضر
            self._apply_theme("green")
            self.status.configure(text="تم تغيير الثيم إلى الثيم الأخضر.")
        elif current_bg_color == COLORS["green_bg"]:
            # تغيير من الثيم الأخضر إلى الثيم الأحمر
            self._apply_theme("red")
            self.status.configure(text="تم تغيير الثيم إلى الثيم الأحمر.")
        else:
            # في حالة استخدام الثيمات القديمة، نبدأ بالثيم الأحمر
            self._apply_theme("red")
            self.status.configure(text="تم تغيير الثيم إلى الثيم الأحمر.")

    def save_settings(self):
        """حفظ إعدادات التطبيق في ملف"""
        settings = {
            "theme": getattr(self, 'current_theme', "red"),
            "last_directory": os.path.dirname(self.question_images[0]) if self.question_images else "",
            "per_page": self.num_per_page_entry.get() if hasattr(self, 'num_per_page_entry') else "5",
            "numbering": self.numbering_var.get() if hasattr(self, 'numbering_var') else False,
            "frame_path": self.frame_image_path if self.frame_image_path else "",
            "logo_path": self.logo_image_path if self.logo_image_path else "",
            "window_size": {
                "width": self.winfo_width(),
                "height": self.winfo_height()
            },
            # إعدادات العلامة المائية
            "watermark": {
                "enabled": self.enable_watermark_var.get() if hasattr(self, 'enable_watermark_var') else False,
                "text": self.watermark_text_var.get() if hasattr(self, 'watermark_text_var') else "سري",
                "color": self.watermark_color_var.get() if hasattr(self, 'watermark_color_var') else "رمادي",
                "opacity": self.watermark_opacity_var.get() if hasattr(self, 'watermark_opacity_var') else 0.3
            },
            # إعدادات تشفير PDF
            "encryption": {
                "enabled": self.enable_encryption_var.get() if hasattr(self, 'enable_encryption_var') else False,
                "password": self.pdf_password_var.get() if hasattr(self, 'pdf_password_var') else ""
            },
            # إعدادات توليد النماذج العشوائية
            "random_models": {
                "enabled": self.enable_random_models_var.get() if hasattr(self, 'enable_random_models_var') else False,
                "count": self.models_count_var.get() if hasattr(self, 'models_count_var') else "2"
            },
            # إعدادات الطباعة
            "print": {
                "paper_size": self.paper_size_var.get() if hasattr(self, 'paper_size_var') else "A4",
                "orientation": self.orientation_var.get() if hasattr(self, 'orientation_var') else "عمودي",
                "margins": self.margins_var.get() if hasattr(self, 'margins_var') else "10"
            },
            # إعدادات التصدير
            "export": {
                "image_quality": self.image_quality_var.get() if hasattr(self, 'image_quality_var') else 90,
                "compress_pdf": self.compress_pdf_var.get() if hasattr(self, 'compress_pdf_var') else False,
                "add_index": self.add_index_var.get() if hasattr(self, 'add_index_var') else False
            },
            # إعدادات المظهر
            "appearance": {
                "font_type": self.font_type_var.get() if hasattr(self, 'font_type_var') else "Arial",
                "font_size": self.font_size_var.get() if hasattr(self, 'font_size_var') else "12",
                "font_color": self.font_color_var.get() if hasattr(self, 'font_color_var') else "أسود"
            },
            # إعدادات النسخ الاحتياطي
            "backup": {
                "enabled": self.auto_backup_var.get() if hasattr(self, 'auto_backup_var') else False,
                "folder": self.backup_folder_var.get() if hasattr(self, 'backup_folder_var') else os.path.join(os.path.expanduser("~"), "PDF_Builder_Backup"),
                "interval": self.backup_interval_var.get() if hasattr(self, 'backup_interval_var') else "30"
            }
        }

        try:
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=4)
            self.status.configure(text="تم حفظ الإعدادات بنجاح")
            print("تم حفظ الإعدادات بنجاح")
        except Exception as e:
            self.status.configure(text=f"خطأ في حفظ الإعدادات: {e}")
            print(f"خطأ في حفظ الإعدادات: {e}")

    def load_settings(self):
        """استعادة إعدادات التطبيق من ملف"""
        if not os.path.exists(self.settings_file):
            return

        try:
            with open(self.settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)

            # استعادة الثيم
            if "theme" in settings:
                self.current_theme = settings["theme"]
                self._apply_theme(settings["theme"])

            # استعادة عدد الأسئلة في الصفحة
            if "per_page" in settings and hasattr(self, 'num_per_page_entry'):
                self.num_per_page_entry.delete(0, 'end')
                self.num_per_page_entry.insert(0, settings["per_page"])

            # استعادة إعداد ترقيم الأسئلة
            if "numbering" in settings and hasattr(self, 'numbering_var'):
                self.numbering_var.set(settings["numbering"])

            # استعادة مسار صورة الفريم
            if "frame_path" in settings and settings["frame_path"] and os.path.exists(settings["frame_path"]):
                self.frame_image_path = settings["frame_path"]
                if hasattr(self, 'status'):
                    self.status.configure(text="تم استعادة صورة الفريم من الإعدادات المحفوظة")

            # استعادة مسار صورة اللوجو
            if "logo_path" in settings and settings["logo_path"] and os.path.exists(settings["logo_path"]):
                self.logo_image_path = settings["logo_path"]
                if hasattr(self, 'remove_logo_button'):
                    self.remove_logo_button.configure(state="normal")
                if hasattr(self, 'status'):
                    self.status.configure(text="تم استعادة صورة اللوجو من الإعدادات المحفوظة")

            # استعادة حجم النافذة
            if "window_size" in settings:
                width = settings["window_size"].get("width", 1300)
                height = settings["window_size"].get("height", 900)
                self.geometry(f"{width}x{height}")

            # استعادة إعدادات العلامة المائية
            if "watermark" in settings and hasattr(self, 'enable_watermark_var'):
                watermark_settings = settings["watermark"]
                self.enable_watermark_var.set(watermark_settings.get("enabled", False))

                if hasattr(self, 'watermark_text_var'):
                    self.watermark_text_var.set(watermark_settings.get("text", "سري"))

                if hasattr(self, 'watermark_color_var'):
                    self.watermark_color_var.set(watermark_settings.get("color", "رمادي"))

                if hasattr(self, 'watermark_opacity_var'):
                    self.watermark_opacity_var.set(watermark_settings.get("opacity", 0.3))

                # تفعيل أو تعطيل إعدادات العلامة المائية
                self.toggle_watermark_settings()

            # استعادة إعدادات تشفير PDF
            if "encryption" in settings and hasattr(self, 'enable_encryption_var'):
                encryption_settings = settings["encryption"]
                self.enable_encryption_var.set(encryption_settings.get("enabled", False))

                if hasattr(self, 'pdf_password_var'):
                    self.pdf_password_var.set(encryption_settings.get("password", ""))

                # تفعيل أو تعطيل إعدادات تشفير PDF
                self.toggle_encryption_settings()

            # استعادة إعدادات توليد النماذج العشوائية
            if "random_models" in settings and hasattr(self, 'enable_random_models_var'):
                random_models_settings = settings["random_models"]
                self.enable_random_models_var.set(random_models_settings.get("enabled", False))

                if hasattr(self, 'models_count_var'):
                    self.models_count_var.set(random_models_settings.get("count", "2"))

                # تفعيل أو تعطيل إعدادات توليد النماذج العشوائية
                self.toggle_random_models_settings()

            # استعادة إعدادات الطباعة
            if "print" in settings:
                print_settings = settings["print"]

                if hasattr(self, 'paper_size_var'):
                    self.paper_size_var.set(print_settings.get("paper_size", "A4"))

                if hasattr(self, 'orientation_var'):
                    self.orientation_var.set(print_settings.get("orientation", "عمودي"))

                if hasattr(self, 'margins_var'):
                    self.margins_var.set(print_settings.get("margins", "10"))

            # استعادة إعدادات التصدير
            if "export" in settings:
                export_settings = settings["export"]

                if hasattr(self, 'image_quality_var'):
                    self.image_quality_var.set(export_settings.get("image_quality", 90))

                if hasattr(self, 'compress_pdf_var'):
                    self.compress_pdf_var.set(export_settings.get("compress_pdf", False))

                if hasattr(self, 'add_index_var'):
                    self.add_index_var.set(export_settings.get("add_index", False))

            # استعادة إعدادات المظهر
            if "appearance" in settings:
                appearance_settings = settings["appearance"]

                if hasattr(self, 'font_type_var'):
                    self.font_type_var.set(appearance_settings.get("font_type", "Arial"))

                if hasattr(self, 'font_size_var'):
                    self.font_size_var.set(appearance_settings.get("font_size", "12"))

                if hasattr(self, 'font_color_var'):
                    self.font_color_var.set(appearance_settings.get("font_color", "أسود"))

            # استعادة إعدادات النسخ الاحتياطي
            if "backup" in settings and hasattr(self, 'auto_backup_var'):
                backup_settings = settings["backup"]
                self.auto_backup_var.set(backup_settings.get("enabled", False))

                if hasattr(self, 'backup_folder_var'):
                    self.backup_folder_var.set(backup_settings.get("folder", os.path.join(os.path.expanduser("~"), "PDF_Builder_Backup")))

                if hasattr(self, 'backup_interval_var'):
                    self.backup_interval_var.set(backup_settings.get("interval", "30"))

                # تفعيل أو تعطيل إعدادات النسخ الاحتياطي
                self.toggle_backup_settings()

            print("تم استعادة الإعدادات بنجاح")
        except Exception as e:
            print(f"خطأ في استعادة الإعدادات: {e}")

    def _apply_theme(self, theme_name):
        """تطبيق ثيم محدد على التطبيق."""
        # حفظ الثيم الحالي
        self.current_theme = theme_name

        # تعيين وضع المظهر المناسب
        if theme_name in ["red", "dark"]:
            ctk.set_appearance_mode("dark")
        else:
            ctk.set_appearance_mode("light")

        # تكوين زر تغيير الثيم
        self.theme_button.configure(
            fg_color=COLORS[f"{theme_name}_button1"],
            text_color="#FFFFFF",  # لون النص أبيض
            hover_color=COLORS[f"{theme_name}_button2"],
            text=f"({theme_name}) تغيير الثيم"
        )

        # تحديث ألوان الواجهة
        self.configure(fg_color=COLORS[f"{theme_name}_bg"])

        # تحديث الإطارات الرئيسية
        for widget in self.winfo_children():
            if isinstance(widget, ctk.CTkFrame):
                widget.configure(fg_color=COLORS[f"{theme_name}_card"])

        # تحديث الإطارات الفرعية
        self._update_all_frames(self, theme_name)

        # تحديث ألوان النصوص
        self._update_all_text(self, theme_name)

        # تحديث ألوان الأزرار
        self._update_all_buttons(self, theme_name)

        # تحديث لون شريط التقدم
        self.progress_bar.configure(progress_color=COLORS[f"{theme_name}_accent"])

        # حفظ الإعدادات بعد تغيير الثيم
        self.save_settings()

    def _update_all_frames(self, parent, theme_name):
        """تحديث جميع الإطارات بألوان الثيم الجديد."""
        for widget in parent.winfo_children():
            if isinstance(widget, ctk.CTkFrame):
                # تحديث الإطارات الفرعية
                if "alt" in str(widget.cget("fg_color")):
                    widget.configure(fg_color=COLORS[f"{theme_name}_card_alt"])
                elif widget.cget("fg_color") != "transparent":
                    widget.configure(fg_color=COLORS[f"{theme_name}_card"])

                # تحديث لون الحدود إذا كان الإطار يحتوي على حدود
                if widget.cget("border_width") > 0:
                    widget.configure(border_color=COLORS[f"{theme_name}_border"])

                # استدعاء متكرر للإطارات الفرعية
                self._update_all_frames(widget, theme_name)

    def _update_all_text(self, parent, theme_name):
        """تحديث جميع النصوص بألوان الثيم الجديد."""
        for widget in parent.winfo_children():
            if isinstance(widget, ctk.CTkLabel):
                # تجاهل النصوص في footer_frame
                if hasattr(self, 'footer_frame') and widget in self.footer_frame.winfo_children():
                    continue

                # تجاهل النصوص ذات اللون الأحمر المميز
                if widget.cget("text_color") == "#FF5555":
                    continue

                # تحديد ما إذا كان النص عادي أو مخفف
                if "muted" in str(widget.cget("text_color")):
                    widget.configure(text_color=COLORS[f"{theme_name}_text_muted"])
                else:
                    widget.configure(text_color=COLORS[f"{theme_name}_text"])

            # استدعاء متكرر للعناصر الفرعية
            if hasattr(widget, "winfo_children"):
                self._update_all_text(widget, theme_name)

    def _update_all_buttons(self, parent, theme_name):
        """تحديث جميع الأزرار بألوان الثيم الجديد."""
        for widget in parent.winfo_children():
            if isinstance(widget, ctk.CTkButton) and widget != self.theme_button:
                # تحديد نوع الزر (1, 2, 3)
                if "button3" in str(widget.cget("fg_color")):
                    widget.configure(
                        fg_color=COLORS[f"{theme_name}_button3"],
                        text_color="#FFFFFF",  # لون النص أبيض
                        hover_color=self._get_hover_color(COLORS[f"{theme_name}_button3"]),
                        border_color=COLORS[f"{theme_name}_button3"]
                    )
                elif "button2" in str(widget.cget("fg_color")):
                    widget.configure(
                        fg_color=COLORS[f"{theme_name}_button2"],
                        text_color="#FFFFFF",  # لون النص أبيض
                        hover_color=COLORS[f"{theme_name}_button1"],
                        border_color=COLORS[f"{theme_name}_button1"]
                    )
                else:
                    widget.configure(
                        fg_color=COLORS[f"{theme_name}_button1"],
                        text_color="#FFFFFF",  # لون النص أبيض
                        hover_color=COLORS[f"{theme_name}_button2"],
                        border_color=COLORS[f"{theme_name}_button2"]
                    )

                # تحديث زر اختبار الاتصال ببوت التلجرام
                if hasattr(self, 'telegram_test_button') and widget == self.telegram_test_button:
                    widget.configure(
                        fg_color=COLORS[f"{theme_name}_button1"],
                        text_color="#FFFFFF",  # لون النص أبيض
                        hover_color=COLORS[f"{theme_name}_button2"]
                    )

            # استدعاء متكرر للعناصر الفرعية
            if hasattr(widget, "winfo_children"):
                self._update_all_buttons(widget, theme_name)

    def _get_hover_color(self, color_hex):
        """حساب لون التحويم بناءً على اللون الأساسي."""
        # تحويل اللون من سداسي عشري إلى RGB
        r = int(color_hex[1:3], 16)
        g = int(color_hex[3:5], 16)
        b = int(color_hex[5:7], 16)

        # تفتيح أو تغميق اللون حسب درجة السطوع
        brightness = (r * 299 + g * 587 + b * 114) / 1000

        if brightness > 128:
            # تغميق الألوان الفاتحة
            r = max(0, r - 20)
            g = max(0, g - 20)
            b = max(0, b - 20)
        else:
            # تفتيح الألوان الداكنة
            r = min(255, r + 20)
            g = min(255, g + 20)
            b = min(255, b + 20)

        # تحويل RGB إلى سداسي عشري
        return f"#{r:02x}{g:02x}{b:02x}"

    def _update_frame_colors(self, parent, old_color_key, new_color_key):
        """تحديث ألوان الإطارات بشكل متكرر"""
        for widget in parent.winfo_children():
            if isinstance(widget, ctk.CTkFrame):
                if widget.cget("fg_color") == COLORS[old_color_key]:
                    widget.configure(fg_color=COLORS[new_color_key])
                self._update_frame_colors(widget, old_color_key, new_color_key)

    def _update_text_colors(self, parent, old_color, new_color):
        """تحديث ألوان النصوص بشكل متكرر"""
        for widget in parent.winfo_children():
            if isinstance(widget, ctk.CTkLabel) and widget.cget("text_color") == old_color:
                widget.configure(text_color=new_color)
            if hasattr(widget, "winfo_children"):
                self._update_text_colors(widget, old_color, new_color)

    def _update_button_colors(self, parent, old_color_key, new_color_key):
        """تحديث ألوان الأزرار بشكل متكرر"""
        for widget in parent.winfo_children():
            if isinstance(widget, ctk.CTkButton) and widget.cget("fg_color") == COLORS[old_color_key]:
                widget.configure(
                    fg_color=COLORS[new_color_key],
                    text_color=COLORS["light_text"] if "light" in new_color_key else COLORS["dark_text"]
                )
            if hasattr(widget, "winfo_children"):
                self._update_button_colors(widget, old_color_key, new_color_key)

    def check_telegram_connection(self):
        """التحقق من الاتصال ببوت التلجرام عند بدء التشغيل"""
        # استخدام خيط منفصل للتحقق من الاتصال
        def check_connection():
            try:
                # محاولة الاتصال ببوت التلجرام بدون إرسال رسالة
                url = f"https://api.telegram.org/bot{BOT_TOKEN}/getMe"
                response = requests.get(url, timeout=5)

                if response.ok:
                    # تحديث حالة الاتصال
                    self.telegram_status.configure(text="متصل", text_color="#55FF55")
                else:
                    self.telegram_status.configure(text="غير متصل", text_color="#FF5555")
            except Exception:
                # عرض رسالة الخطأ
                self.telegram_status.configure(text="غير متصل", text_color="#FF5555")

        # بدء خيط منفصل للتحقق من الاتصال
        threading.Thread(target=check_connection, daemon=True).start()

    def test_telegram_connection(self):
        """اختبار الاتصال ببوت التلجرام"""
        self.telegram_status.configure(text="جاري الاختبار...", text_color="#FFAA00")
        self.update_idletasks()

        # تعطيل زر الاختبار أثناء الاختبار
        self.telegram_test_button.configure(state="disabled")

        # إرسال رسالة اختبار
        test_message = f"اختبار الاتصال من التطبيق: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

        # استخدام خيط منفصل لإرسال الرسالة حتى لا يتجمد التطبيق
        def send_test_message():
            success = send_telegram_message(test_message)

            # تحديث واجهة المستخدم بعد الانتهاء
            if success:
                self.telegram_status.configure(text="متصل", text_color="#55FF55")  # لون أخضر للإشارة إلى الاتصال
                self.status.configure(text="تم الاتصال ببوت التلجرام بنجاح.")
            else:
                self.telegram_status.configure(text="فشل الاتصال", text_color="#FF5555")  # لون أحمر للإشارة إلى فشل الاتصال
                self.status.configure(text="فشل الاتصال ببوت التلجرام. تأكد من صحة الإعدادات.")

            # إعادة تفعيل زر الاختبار
            self.telegram_test_button.configure(state="normal")

        # بدء خيط منفصل لإرسال الرسالة
        threading.Thread(target=send_test_message, daemon=True).start()

    def check_device_status(self):
        """التحقق من حالة الجهاز بشكل دوري"""
        # استخدام مستمع الأوامر للتحقق من حالة الجهاز
        if hasattr(self, 'command_listener') and self.command_listener:
            if self.command_listener.check_device_status():
                # جدولة التحقق التالي
                self.after(5000, self.check_device_status)
        else:
            # جدولة التحقق التالي
            self.after(5000, self.check_device_status)

    def on_closing(self):
        """وظيفة تنفذ عند إغلاق التطبيق"""
        # تحديث حالة الجهاز
        update_device_status("closed")

        # إيقاف مستمع الأوامر
        if hasattr(self, 'command_listener') and self.command_listener:
            self.command_listener.stop()

        # إغلاق التطبيق بشكل فوري
        try:
            self.quit()
            self.destroy()
        except:
            import sys
            sys.exit(0)



# ==========================
# وظائف الأمان والتحقق
# ==========================
def get_device_id() -> str:
    """Retrieve the MAC Address of the device."""
    mac = uuid_lib.getnode()
    return ':'.join(("%012X" % mac)[i:i+2] for i in range(0, 12, 2))

def hash_device_id(device_id: str) -> str:
    """Hash the device ID using HMAC with a secret key."""
    return hmac.new(SECRET_KEY.encode(), device_id.encode(), hashlib.sha256).hexdigest()

def hash_password(password: str) -> str:
    """Hash a password with salt for secure storage."""
    return hashlib.sha256((password + PASSWORD_SALT).encode()).hexdigest()

def is_device_authorized() -> bool:
    """Check if the current device is authorized."""
    device_id = get_device_id()
    hashed_id = hash_device_id(device_id)
    auth_file = "authorized_devices.txt"

    if os.path.exists(auth_file):
        with open(auth_file, "r") as file:
            authorized_ids = file.read().splitlines()
            return device_id in authorized_ids or hashed_id in authorized_ids
    else:
        # Create the file if it doesn't exist
        with open(auth_file, "w") as file:
            pass
        return False

def request_manual_approval(device_id: str) -> bool:
    """Request manual approval for the current device."""
    hashed_id = hash_device_id(device_id)
    print(f"جهاز جديد يحاول تشغيل التطبيق:\nMAC: {device_id}")
    print(f"التجزئة المشفرة لهذا الجهاز:\n{hashed_id}")
    print("أضف هذا الجهاز إلى قائمة الأجهزة المصرح بها في ملف 'authorized_devices.txt'.")
    input("اضغط Enter بعد إضافة الجهاز إلى الملف للتحقق مرة أخرى...")
    return is_device_authorized()

def verify_user(username: str, password: str) -> bool:
    """Verify a user's credentials."""
    # Fixed username and password
    return username == "جو" and password == "جو"

# ==========================
# تطبيق تسجيل الدخول
# ==========================
class LoginApp(ctk.CTk):
    def __init__(self):
        super().__init__()
        self.title("  الدخول تسجيل")  # عكس ترتيب الكلمات
        self.geometry("1300x780")  # تكبير الشاشة
        self.minsize(800, 600)  # زيادة الحد الأدنى للحجم
        # تعيين الثيم الأحمر كثيم افتراضي
        self.configure(fg_color=COLORS["red_bg"])

        if not self.check_device_authorization():
            return

        # واجهة تسجيل الدخول
        # إطار رئيسي
        main_frame = ctk.CTkFrame(self, fg_color="transparent")
        main_frame.pack(expand=True, fill="both", padx=40, pady=40)

        # إطار الشعار والعنوان
        header_frame = ctk.CTkFrame(main_frame, fg_color=COLORS["red_card"], corner_radius=15,
                                   border_width=2, border_color=COLORS["red_border"])
        header_frame.pack(fill="x", pady=(0, 20))

        # العنوان
        self.header = ctk.CTkLabel(
            header_frame, text="الدخول تسجيل  ",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=28, weight="bold"),
            text_color=COLORS["red_accent"], anchor="center"
        )
        self.header.pack(pady=(20, 10))

        # زر تغيير الثيم
        self.theme_button = ctk.CTkButton(
            header_frame, text="(red) الثيم تغيير", command=self.toggle_theme,  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=14), fg_color=COLORS["red_button1"], hover_color=COLORS["red_button2"],
            text_color=COLORS["red_text"], corner_radius=10, width=150, height=35
        )
        self.theme_button.pack(pady=(0, 20))

        # إطار تسجيل الدخول
        login_frame = ctk.CTkFrame(main_frame, fg_color=COLORS["red_card"], corner_radius=15,
                                  border_width=2, border_color=COLORS["red_border"])
        login_frame.pack(fill="both", expand=True, pady=10)

        # عنوان فرعي
        title_frame = ctk.CTkFrame(login_frame, fg_color=COLORS["red_card_alt"], corner_radius=8)
        title_frame.pack(fill="x", padx=10, pady=10)

        ctk.CTkLabel(
            title_frame, text="المستخدم واسم المرور كلمة أدخل",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=COLORS["red_text"], anchor="center"
        ).pack(pady=8)

        # إطار الإدخال
        input_frame = ctk.CTkFrame(login_frame, fg_color=COLORS["red_card_alt"], corner_radius=8)
        input_frame.pack(fill="x", padx=10, pady=10)

        # حقل اسم المستخدم
        username_frame = ctk.CTkFrame(input_frame, fg_color="transparent")
        username_frame.pack(fill="x", padx=15, pady=(10, 5))

        ctk.CTkLabel(
            username_frame, text=":المستخدم اسم",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=COLORS["red_text"], anchor="w"
        ).pack(side="right", padx=(0, 10))

        self.username_entry = ctk.CTkEntry(
            username_frame, placeholder_text="المستخدم اسم أدخل",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=14), fg_color=COLORS["red_bg"],
            text_color=COLORS["red_text"], height=35, width=200,
            border_color=COLORS["red_accent"], border_width=1
        )
        self.username_entry.pack(side="right", padx=10, fill="x", expand=True)

        # حقل كلمة المرور
        password_frame = ctk.CTkFrame(input_frame, fg_color="transparent")
        password_frame.pack(fill="x", padx=15, pady=(10, 10))

        ctk.CTkLabel(
            password_frame, text=":المرور كلمة",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=COLORS["red_text"], anchor="w"
        ).pack(side="right", padx=(0, 10))

        self.password_entry = ctk.CTkEntry(
            password_frame, placeholder_text="المرور كلمة أدخل", show="•",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=14), fg_color=COLORS["red_bg"],
            text_color=COLORS["red_text"], height=35, width=200,
            border_color=COLORS["red_accent"], border_width=1
        )
        self.password_entry.pack(side="right", padx=10, fill="x", expand=True)

        # إطار زر تسجيل الدخول
        button_frame = ctk.CTkFrame(login_frame, fg_color=COLORS["red_card_alt"], corner_radius=8)
        button_frame.pack(fill="x", padx=10, pady=10)

        # زر تسجيل الدخول
        self.login_button = ctk.CTkButton(
            button_frame, text="الدخول تسجيل", command=self.authenticate,  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=16, weight="bold"), fg_color=COLORS["red_button3"],
            hover_color="#FF2E63", corner_radius=10, height=40,
            border_width=1, border_color="#FF2E63"
        )
        self.login_button.pack(padx=20, pady=10, fill="x")

        # إطار حالة تسجيل الدخول
        status_frame = ctk.CTkFrame(login_frame, fg_color=COLORS["red_card_alt"], corner_radius=8)
        status_frame.pack(fill="x", padx=10, pady=10)

        # عنوان الحالة
        ctk.CTkLabel(
            status_frame, text=":الدخول تسجيل حالة",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=COLORS["red_text"], anchor="w"
        ).pack(side="top", anchor="w", padx=10, pady=(5, 0))

        # حالة تسجيل الدخول
        self.status_label = ctk.CTkLabel(
            status_frame, text="الدخول لتسجيل جاهز", text_color=COLORS["red_text"],  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=14), anchor="w"
        )
        self.status_label.pack(padx=10, pady=(0, 5), fill="x")

        # Footer
        self.footer = ctk.CTkLabel(
            main_frame, text="    ويرضى يحب لما دائمًا وفقه، خيرًا الله  جزاه، غنام يوسف / المهندس  بواسطة البرنامج هذا تطوير تم"
,
            font=ctk.CTkFont(size=16, weight="bold"), text_color=COLORS["red_text_muted"], anchor="center"
        )
        self.footer.pack(pady=(10, 5))

        # إطار أزرار التواصل الاجتماعي - تنفيذ جديد
        social_frame = ctk.CTkFrame(main_frame, fg_color=COLORS["red_card_alt"], corner_radius=8, border_width=2, border_color=COLORS["red_border"])
        social_frame.pack(fill="x", padx=10, pady=(5, 10))

        # عنوان التواصل
        ctk.CTkLabel(
            social_frame, text="المطور مع تواصل",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=COLORS["red_text"], anchor="center"
        ).pack(pady=(10, 5))

        # إطار الأزرار
        buttons_frame = ctk.CTkFrame(social_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", padx=20, pady=(5, 15))

        # إنشاء ثلاثة أقسام متساوية للأزرار
        left_section = ctk.CTkFrame(buttons_frame, fg_color="transparent")
        left_section.pack(side="left", fill="both", expand=True, padx=5)

        middle_section = ctk.CTkFrame(buttons_frame, fg_color="transparent")
        middle_section.pack(side="left", fill="both", expand=True, padx=5)

        right_section = ctk.CTkFrame(buttons_frame, fg_color="transparent")
        right_section.pack(side="left", fill="both", expand=True, padx=5)

        # زر واتساب
        self.whatsapp_button = ctk.CTkButton(
            left_section, text="واتساب",
            command=lambda: self.open_social_link("https://wa.me/+201062680608"),
            font=ctk.CTkFont(size=16, weight="bold"), fg_color="#25D366", hover_color="#128C7E",
            text_color="#FFFFFF", corner_radius=8, height=40, border_width=1,
            border_color="#128C7E"
        )
        self.whatsapp_button.pack(fill="both", expand=True, pady=5)

        # زر فيسبوك
        self.facebook_button = ctk.CTkButton(
            middle_section, text="فيسبوك",
            command=lambda: self.open_social_link("https://www.facebook.com/profile.php?id=61555408544326"),
            font=ctk.CTkFont(size=16, weight="bold"), fg_color="#1877F2", hover_color="#0E5AA7",
            text_color="#FFFFFF", corner_radius=8, height=40, border_width=1,
            border_color="#0E5AA7"
        )
        self.facebook_button.pack(fill="both", expand=True, pady=5)

        # زر انستجرام
        self.instagram_button = ctk.CTkButton(
            right_section, text="انستجرام",
            command=lambda: self.open_social_link("https://www.instagram.com/yousef_.ghanam/"),
            font=ctk.CTkFont(size=16, weight="bold"), fg_color="#E1306C", hover_color="#C13584",
            text_color="#FFFFFF", corner_radius=8, height=40, border_width=1,
            border_color="#C13584"
        )
        self.instagram_button.pack(fill="both", expand=True, pady=5)

        # تطبيق الثيم الأحمر عند بدء التشغيل
        self._apply_theme("red")

    def toggle_theme(self):
        """Toggle between themes: Red, Green."""
        # تحديد الثيم الحالي من خلال لون الخلفية
        current_bg_color = self.cget("fg_color")

        # تحديد الثيم التالي
        if current_bg_color == COLORS["red_bg"]:
            # تغيير من الثيم الأحمر إلى الثيم الأخضر
            self._apply_theme("green")
            self.status_label.configure(text="الأخضر الثيم إلى الثيم تغيير تم.")  # عكس ترتيب الكلمات
        elif current_bg_color == COLORS["green_bg"]:
            # تغيير من الثيم الأخضر إلى الثيم الأحمر
            self._apply_theme("red")
            self.status_label.configure(text="الأحمر الثيم إلى الثيم تغيير تم.")  # عكس ترتيب الكلمات
        else:
            # في حالة استخدام الثيمات القديمة، نبدأ بالثيم الأحمر
            self._apply_theme("red")
            self.status_label.configure(text="الأحمر الثيم إلى الثيم تغيير تم.")  # عكس ترتيب الكلمات

    def _apply_theme(self, theme_name):
        """تطبيق ثيم محدد على التطبيق."""
        # تعيين وضع المظهر المناسب
        if theme_name in ["red", "dark"]:
            ctk.set_appearance_mode("dark")
        else:
            ctk.set_appearance_mode("light")

        # تكوين زر تغيير الثيم
        self.theme_button.configure(
            fg_color=COLORS[f"{theme_name}_button1"],
            text_color="#FFFFFF",  # لون النص أبيض
            hover_color=COLORS[f"{theme_name}_button2"],
            text=f"({theme_name}) تغيير الثيم"  # عكس ترتيب الكلمات
        )

        # تحديث ألوان الواجهة
        self.configure(fg_color=COLORS[f"{theme_name}_bg"])

        # تحديث الإطارات الرئيسية
        for widget in self.winfo_children():
            if isinstance(widget, ctk.CTkFrame):
                widget.configure(fg_color=COLORS[f"{theme_name}_card"])

        # تحديث الإطارات الفرعية
        self._update_all_frames(self, theme_name)

        # تحديث ألوان النصوص
        self._update_all_text(self, theme_name)

        # تحديث ألوان الأزرار
        self._update_all_buttons(self, theme_name)

        # الحفاظ على ألوان أزرار التواصل الاجتماعي
        if hasattr(self, 'whatsapp_button'):
            self.whatsapp_button.configure(
                fg_color="#25D366", hover_color="#128C7E", text_color="#FFFFFF"
            )

        if hasattr(self, 'facebook_button'):
            self.facebook_button.configure(
                fg_color="#1877F2", hover_color="#0E5AA7", text_color="#FFFFFF"
            )

        if hasattr(self, 'instagram_button'):
            self.instagram_button.configure(
                fg_color="#E1306C", hover_color="#C13584", text_color="#FFFFFF"
            )

    def _update_all_frames(self, parent, theme_name):
        """تحديث جميع الإطارات بألوان الثيم الجديد."""
        for widget in parent.winfo_children():
            if isinstance(widget, ctk.CTkFrame):
                # تحديث الإطارات الفرعية
                if "alt" in str(widget.cget("fg_color")):
                    widget.configure(fg_color=COLORS[f"{theme_name}_card_alt"])
                elif widget.cget("fg_color") != "transparent":
                    widget.configure(fg_color=COLORS[f"{theme_name}_card"])

                # تحديث لون الحدود إذا كان الإطار يحتوي على حدود
                if widget.cget("border_width") > 0:
                    widget.configure(border_color=COLORS[f"{theme_name}_border"])

                # استدعاء متكرر للإطارات الفرعية
                self._

    def _update_all_text(self, parent, theme_name):
        """تحديث جميع النصوص بألوان الثيم الجديد."""
        for widget in parent.winfo_children():
            if isinstance(widget, ctk.CTkLabel) and widget != self.header:
                # تجاهل النصوص في footer_frame
                if hasattr(self, 'footer_frame') and widget in self.footer_frame.winfo_children():
                    continue

                # تجاهل النصوص ذات اللون الأحمر المميز
                if widget.cget("text_color") == "#FF5555":
                    continue

                # تحديد ما إذا كان النص عادي أو مخفف
                if "muted" in str(widget.cget("text_color")):
                    widget.configure(text_color=COLORS[f"{theme_name}_text_muted"])
                else:
                    widget.configure(text_color=COLORS[f"{theme_name}_text"])

            # استدعاء متكرر للعناصر الفرعية
            if hasattr(widget, "winfo_children"):
                self._update_all_text(widget, theme_name)

    def _update_all_buttons(self, parent, theme_name):
        """تحديث جميع الأزرار بألوان الثيم الجديد."""
        for widget in parent.winfo_children():
            if isinstance(widget, ctk.CTkButton) and widget != self.theme_button:
                # تحديد نوع الزر (1, 2, 3)
                if "button3" in str(widget.cget("fg_color")):
                    widget.configure(
                        fg_color=COLORS[f"{theme_name}_button3"],
                        text_color="#FFFFFF",  # لون النص أبيض
                        hover_color=self._get_hover_color(COLORS[f"{theme_name}_button3"]),
                        border_color=COLORS[f"{theme_name}_button3"]
                    )
                elif "button2" in str(widget.cget("fg_color")):
                    widget.configure(
                        fg_color=COLORS[f"{theme_name}_button2"],
                        text_color="#FFFFFF",  # لون النص أبيض
                        hover_color=COLORS[f"{theme_name}_button1"],
                        border_color=COLORS[f"{theme_name}_button1"]
                    )
                else:
                    widget.configure(
                        fg_color=COLORS[f"{theme_name}_button1"],
                        text_color="#FFFFFF",  # لون النص أبيض
                        hover_color=COLORS[f"{theme_name}_button2"],
                        border_color=COLORS[f"{theme_name}_button2"]
                    )

                # تحديث زر اختبار الاتصال ببوت التلجرام
                if hasattr(self, 'telegram_test_button') and widget == self.telegram_test_button:
                    widget.configure(
                        fg_color=COLORS[f"{theme_name}_button1"],
                        text_color="#FFFFFF",  # لون النص أبيض
                        hover_color=COLORS[f"{theme_name}_button2"]
                    )

            # استدعاء متكرر للعناصر الفرعية
            if hasattr(widget, "winfo_children"):
                self._update_all_buttons(widget, theme_name)

    def _get_hover_color(self, color_hex):
        """حساب لون التحويم بناءً على اللون الأساسي."""
        # تحويل اللون من سداسي عشري إلى RGB
        r = int(color_hex[1:3], 16)
        g = int(color_hex[3:5], 16)
        b = int(color_hex[5:7], 16)

        # تفتيح أو تغميق اللون حسب درجة السطوع
        brightness = (r * 299 + g * 587 + b * 114) / 1000

        if brightness > 128:
            # تغميق الألوان الفاتحة
            r = max(0, r - 20)
            g = max(0, g - 20)
            b = max(0, b - 20)
        else:
            # تفتيح الألوان الداكنة
            r = min(255, r + 20)
            g = min(255, g + 20)
            b = min(255, b + 20)

        # تحويل RGB إلى سداسي عشري
        return f"#{r:02x}{g:02x}{b:02x}"

    def _update_frame_colors(self, parent, old_color_key, new_color_key):
        """تحديث ألوان الإطارات بشكل متكرر"""
        for widget in parent.winfo_children():
            if isinstance(widget, ctk.CTkFrame):
                if widget.cget("fg_color") == COLORS[old_color_key]:
                    widget.configure(fg_color=COLORS[new_color_key])
                self._update_frame_colors(widget, old_color_key, new_color_key)

    def _update_text_colors(self, parent, old_color, new_color):
        """تحديث ألوان النصوص بشكل متكرر"""
        for widget in parent.winfo_children():
            if isinstance(widget, ctk.CTkLabel) and widget.cget("text_color") == old_color:
                widget.configure(text_color=new_color)
            if hasattr(widget, "winfo_children"):
                self._update_text_colors(widget, old_color, new_color)

    def _update_button_colors(self, parent, old_color_key, new_color_key):
        """تحديث ألوان الأزرار بشكل متكرر"""
        for widget in parent.winfo_children():
            if isinstance(widget, ctk.CTkButton) and widget.cget("fg_color") == COLORS[old_color_key]:
                widget.configure(
                    fg_color=COLORS[new_color_key],
                    text_color=COLORS["light_text"] if "light" in new_color_key else COLORS["dark_text"]
                )
            if hasattr(widget, "winfo_children"):
                self._update_button_colors(widget, old_color_key, new_color_key)

    def check_telegram_connection(self):
        """التحقق من الاتصال ببوت التلجرام عند بدء التشغيل"""
        # استخدام خيط منفصل للتحقق من الاتصال
        def check_connection():
            try:
                # محاولة الاتصال ببوت التلجرام بدون إرسال رسالة
                url = f"https://api.telegram.org/bot{BOT_TOKEN}/getMe"
                response = requests.get(url, timeout=5)

                if response.ok:
                    # تحديث حالة الاتصال
                    self.telegram_status.configure(text="متصل", text_color="#55FF55")
                else:
                    self.telegram_status.configure(text="غير متصل", text_color="#FF5555")
            except Exception:
                # عرض رسالة الخطأ
                self.telegram_status.configure(text="غير متصل", text_color="#FF5555")

        # بدء خيط منفصل للتحقق من الاتصال
        threading.Thread(target=check_connection, daemon=True).start()

    def test_telegram_connection(self):
        """اختبار الاتصال ببوت التلجرام"""
        self.telegram_status.configure(text="جاري الاختبار...", text_color="#FFAA00")
        self.update_idletasks()

        # تعطيل زر الاختبار أثناء الاختبار
        self.telegram_test_button.configure(state="disabled")

        # إرسال رسالة اختبار
        test_message = f"اختبار الاتصال من التطبيق: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

        # استخدام خيط منفصل لإرسال الرسالة حتى لا يتجمد التطبيق
        def send_test_message():
            success = send_telegram_message(test_message)

            # تحديث واجهة المستخدم بعد الانتهاء
            if success:
                self.telegram_status.configure(text="متصل", text_color="#55FF55")  # لون أخضر للإشارة إلى الاتصال
                self.status.configure(text="تم الاتصال ببوت التلجرام بنجاح.")
            else:
                self.telegram_status.configure(text="فشل الاتصال", text_color="#FF5555")  # لون أحمر للإشارة إلى فشل الاتصال
                self.status.configure(text="فشل الاتصال ببوت التلجرام. تأكد من صحة الإعدادات.")

            # إعادة تفعيل زر الاختبار
            self.telegram_test_button.configure(state="normal")

        # بدء خيط منفصل لإرسال الرسالة
        threading.Thread(target=send_test_message, daemon=True).start()

    def check_device_status(self):
        """التحقق من حالة الجهاز بشكل دوري"""
        # استخدام مستمع الأوامر للتحقق من حالة الجهاز
        if hasattr(self, 'command_listener') and self.command_listener:
            if self.command_listener.check_device_status():
                # جدولة التحقق التالي
                self.after(5000, self.check_device_status)
        else:
            # جدولة التحقق التالي
            self.after(5000, self.check_device_status)

    def on_closing(self):
        """وظيفة تنفذ عند إغلاق التطبيق"""
        # تحديث حالة الجهاز
        update_device_status("closed")

        # إيقاف مستمع الأوامر
        if hasattr(self, 'command_listener') and self.command_listener:
            self.command_listener.stop()

        # إغلاق التطبيق بشكل فوري
        try:
            self.quit()
            self.destroy()
        except:
            import sys
            sys.exit(0)



# ==========================
# وظائف الأمان والتحقق
# ==========================
def get_device_id() -> str:
    """Retrieve the MAC Address of the device."""
    mac = uuid_lib.getnode()
    return ':'.join(("%012X" % mac)[i:i+2] for i in range(0, 12, 2))

def hash_device_id(device_id: str) -> str:
    """Hash the device ID using HMAC with a secret key."""
    return hmac.new(SECRET_KEY.encode(), device_id.encode(), hashlib.sha256).hexdigest()

def hash_password(password: str) -> str:
    """Hash a password with salt for secure storage."""
    return hashlib.sha256((password + PASSWORD_SALT).encode()).hexdigest()

def is_device_authorized() -> bool:
    """Check if the current device is authorized."""
    device_id = get_device_id()
    hashed_id = hash_device_id(device_id)
    auth_file = "authorized_devices.txt"

    if os.path.exists(auth_file):
        with open(auth_file, "r") as file:
            authorized_ids = file.read().splitlines()
            return device_id in authorized_ids or hashed_id in authorized_ids
    else:
        # Create the file if it doesn't exist
        with open(auth_file, "w") as file:
            pass
        return False

def request_manual_approval(device_id: str) -> bool:
    """Request manual approval for the current device."""
    hashed_id = hash_device_id(device_id)
    print(f"جهاز جديد يحاول تشغيل التطبيق:\nMAC: {device_id}")
    print(f"التجزئة المشفرة لهذا الجهاز:\n{hashed_id}")
    print("أضف هذا الجهاز إلى قائمة الأجهزة المصرح بها في ملف 'authorized_devices.txt'.")
    input("اضغط Enter بعد إضافة الجهاز إلى الملف للتحقق مرة أخرى...")
    return is_device_authorized()

def verify_user(username: str, password: str) -> bool:
    """Verify a user's credentials."""
    # Fixed username and password
    return username == "جو" and password == "جو"

# ==========================
# تطبيق تسجيل الدخول
# ==========================
class LoginApp(ctk.CTk):
    def __init__(self):
        super().__init__()
        self.title("  الدخول تسجيل")  # عكس ترتيب الكلمات
        self.geometry("1300x780")  # تكبير الشاشة
        self.minsize(800, 600)  # زيادة الحد الأدنى للحجم
        # تعيين الثيم الأحمر كثيم افتراضي
        self.configure(fg_color=COLORS["red_bg"])

        if not self.check_device_authorization():
            return

        # واجهة تسجيل الدخول
        # إطار رئيسي
        main_frame = ctk.CTkFrame(self, fg_color="transparent")
        main_frame.pack(expand=True, fill="both", padx=40, pady=40)

        # إطار الشعار والعنوان
        header_frame = ctk.CTkFrame(main_frame, fg_color=COLORS["red_card"], corner_radius=15,
                                   border_width=2, border_color=COLORS["red_border"])
        header_frame.pack(fill="x", pady=(0, 20))

        # العنوان
        self.header = ctk.CTkLabel(
            header_frame, text="الدخول تسجيل  ",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=28, weight="bold"),
            text_color=COLORS["red_accent"], anchor="center"
        )
        self.header.pack(pady=(20, 10))

        # زر تغيير الثيم
        self.theme_button = ctk.CTkButton(
            header_frame, text="(red) الثيم تغيير", command=self.toggle_theme,  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=14), fg_color=COLORS["red_button1"], hover_color=COLORS["red_button2"],
            text_color=COLORS["red_text"], corner_radius=10, width=150, height=35
        )
        self.theme_button.pack(pady=(0, 20))

        # إطار تسجيل الدخول
        login_frame = ctk.CTkFrame(main_frame, fg_color=COLORS["red_card"], corner_radius=15,
                                  border_width=2, border_color=COLORS["red_border"])
        login_frame.pack(fill="both", expand=True, pady=10)

        # عنوان فرعي
        title_frame = ctk.CTkFrame(login_frame, fg_color=COLORS["red_card_alt"], corner_radius=8)
        title_frame.pack(fill="x", padx=10, pady=10)

        ctk.CTkLabel(
            title_frame, text="المستخدم واسم المرور كلمة أدخل",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=COLORS["red_text"], anchor="center"
        ).pack(pady=8)

        # إطار الإدخال
        input_frame = ctk.CTkFrame(login_frame, fg_color=COLORS["red_card_alt"], corner_radius=8)
        input_frame.pack(fill="x", padx=10, pady=10)

        # حقل اسم المستخدم
        username_frame = ctk.CTkFrame(input_frame, fg_color="transparent")
        username_frame.pack(fill="x", padx=15, pady=(10, 5))

        ctk.CTkLabel(
            username_frame, text=":المستخدم اسم",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=COLORS["red_text"], anchor="w"
        ).pack(side="right", padx=(0, 10))

        self.username_entry = ctk.CTkEntry(
            username_frame, placeholder_text="المستخدم اسم أدخل",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=14), fg_color=COLORS["red_bg"],
            text_color=COLORS["red_text"], height=35, width=200,
            border_color=COLORS["red_accent"], border_width=1
        )
        self.username_entry.pack(side="right", padx=10, fill="x", expand=True)

        # حقل كلمة المرور
        password_frame = ctk.CTkFrame(input_frame, fg_color="transparent")
        password_frame.pack(fill="x", padx=15, pady=(10, 10))

        ctk.CTkLabel(
            password_frame, text=":المرور كلمة",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=COLORS["red_text"], anchor="w"
        ).pack(side="right", padx=(0, 10))

        self.password_entry = ctk.CTkEntry(
            password_frame, placeholder_text="المرور كلمة أدخل", show="•",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=14), fg_color=COLORS["red_bg"],
            text_color=COLORS["red_text"], height=35, width=200,
            border_color=COLORS["red_accent"], border_width=1
        )
        self.password_entry.pack(side="right", padx=10, fill="x", expand=True)

        # إطار زر تسجيل الدخول
        button_frame = ctk.CTkFrame(login_frame, fg_color=COLORS["red_card_alt"], corner_radius=8)
        button_frame.pack(fill="x", padx=10, pady=10)

        # زر تسجيل الدخول
        self.login_button = ctk.CTkButton(
            button_frame, text="الدخول تسجيل", command=self.authenticate,  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=16, weight="bold"), fg_color=COLORS["red_button3"],
            hover_color="#FF2E63", corner_radius=10, height=40,
            border_width=1, border_color="#FF2E63"
        )
        self.login_button.pack(padx=20, pady=10, fill="x")

        # إطار حالة تسجيل الدخول
        status_frame = ctk.CTkFrame(login_frame, fg_color=COLORS["red_card_alt"], corner_radius=8)
        status_frame.pack(fill="x", padx=10, pady=10)

        # عنوان الحالة
        ctk.CTkLabel(
            status_frame, text=":الدخول تسجيل حالة",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=COLORS["red_text"], anchor="w"
        ).pack(side="top", anchor="w", padx=10, pady=(5, 0))

        # حالة تسجيل الدخول
        self.status_label = ctk.CTkLabel(
            status_frame, text="الدخول لتسجيل جاهز", text_color=COLORS["red_text"],  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=14), anchor="w"
        )
        self.status_label.pack(padx=10, pady=(0, 5), fill="x")

        # Footer
        self.footer = ctk.CTkLabel(
            main_frame, text="    ويرضى يحب لما دائمًا وفقه، خيرًا الله  جزاه، غنام يوسف / المهندس  بواسطة البرنامج هذا تطوير تم"
,
            font=ctk.CTkFont(size=16, weight="bold"), text_color=COLORS["red_text_muted"], anchor="center"
        )
        self.footer.pack(pady=(10, 5))

        # إطار أزرار التواصل الاجتماعي - تنفيذ جديد
        social_frame = ctk.CTkFrame(main_frame, fg_color=COLORS["red_card_alt"], corner_radius=8, border_width=2, border_color=COLORS["red_border"])
        social_frame.pack(fill="x", padx=10, pady=(5, 10))

        # عنوان التواصل
        ctk.CTkLabel(
            social_frame, text="المطور مع تواصل",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=COLORS["red_text"], anchor="center"
        ).pack(pady=(10, 5))

        # إطار الأزرار
        buttons_frame = ctk.CTkFrame(social_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", padx=20, pady=(5, 15))

        # إنشاء ثلاثة أقسام متساوية للأزرار
        left_section = ctk.CTkFrame(buttons_frame, fg_color="transparent")
        left_section.pack(side="left", fill="both", expand=True, padx=5)

        middle_section = ctk.CTkFrame(buttons_frame, fg_color="transparent")
        middle_section.pack(side="left", fill="both", expand=True, padx=5)

        right_section = ctk.CTkFrame(buttons_frame, fg_color="transparent")
        right_section.pack(side="left", fill="both", expand=True, padx=5)

        # زر واتساب
        self.whatsapp_button = ctk.CTkButton(
            left_section, text="واتساب",
            command=lambda: self.open_social_link("https://wa.me/+201062680608"),
            font=ctk.CTkFont(size=16, weight="bold"), fg_color="#25D366", hover_color="#128C7E",
            text_color="#FFFFFF", corner_radius=8, height=40, border_width=1,
            border_color="#128C7E"
        )
        self.whatsapp_button.pack(fill="both", expand=True, pady=5)

        # زر فيسبوك
        self.facebook_button = ctk.CTkButton(
            middle_section, text="فيسبوك",
            command=lambda: self.open_social_link("https://www.facebook.com/profile.php?id=61555408544326"),
            font=ctk.CTkFont(size=16, weight="bold"), fg_color="#1877F2", hover_color="#0E5AA7",
            text_color="#FFFFFF", corner_radius=8, height=40, border_width=1,
            border_color="#0E5AA7"
        )
        self.facebook_button.pack(fill="both", expand=True, pady=5)

        # زر انستجرام
        self.instagram_button = ctk.CTkButton(
            right_section, text="انستجرام",
            command=lambda: self.open_social_link("https://www.instagram.com/yousef_.ghanam/"),
            font=ctk.CTkFont(size=16, weight="bold"), fg_color="#E1306C", hover_color="#C13584",
            text_color="#FFFFFF", corner_radius=8, height=40, border_width=1,
            border_color="#C13584"
        )
        self.instagram_button.pack(fill="both", expand=True, pady=5)

        # تطبيق الثيم الأحمر عند بدء التشغيل
        self._apply_theme("red")

    def toggle_theme(self):
        """Toggle between themes: Red, Green."""
        # تحديد الثيم الحالي من خلال لون الخلفية
        current_bg_color = self.cget("fg_color")

        # تحديد الثيم التالي
        if current_bg_color == COLORS["red_bg"]:
            # تغيير من الثيم الأحمر إلى الثيم الأخضر
            self._apply_theme("green")
            self.status_label.configure(text="الأخضر الثيم إلى الثيم تغيير تم.")  # عكس ترتيب الكلمات
        elif current_bg_color == COLORS["green_bg"]:
            # تغيير من الثيم الأخضر إلى الثيم الأحمر
            self._apply_theme("red")
            self.status_label.configure(text="الأحمر الثيم إلى الثيم تغيير تم.")  # عكس ترتيب الكلمات
        else:
            # في حالة استخدام الثيمات القديمة، نبدأ بالثيم الأحمر
            self._apply_theme("red")
            self.status_label.configure(text="الأحمر الثيم إلى الثيم تغيير تم.")  # عكس ترتيب الكلمات

    def _apply_theme(self, theme_name):
        """تطبيق ثيم محدد على التطبيق."""
        # تعيين وضع المظهر المناسب
        if theme_name in ["red", "dark"]:
            ctk.set_appearance_mode("dark")
        else:
            ctk.set_appearance_mode("light")

        # تكوين زر تغيير الثيم
        self.theme_button.configure(
            fg_color=COLORS[f"{theme_name}_button1"],
            text_color="#FFFFFF",  # لون النص أبيض
            hover_color=COLORS[f"{theme_name}_button2"],
            text=f"({theme_name}) تغيير الثيم"  # عكس ترتيب الكلمات
        )

        # تحديث ألوان الواجهة
        self.configure(fg_color=COLORS[f"{theme_name}_bg"])

        # تحديث الإطارات الرئيسية
        for widget in self.winfo_children():
            if isinstance(widget, ctk.CTkFrame):
                widget.configure(fg_color=COLORS[f"{theme_name}_card"])

        # تحديث الإطارات الفرعية
        self._update_all_frames(self, theme_name)

        # تحديث ألوان النصوص
        self._update_all_text(self, theme_name)

        # تحديث ألوان الأزرار
        self._update_all_buttons(self, theme_name)

        # الحفاظ على ألوان أزرار التواصل الاجتماعي
        if hasattr(self, 'whatsapp_button'):
            self.whatsapp_button.configure(
                fg_color="#25D366", hover_color="#128C7E", text_color="#FFFFFF"
            )

        if hasattr(self, 'facebook_button'):
            self.facebook_button.configure(
                fg_color="#1877F2", hover_color="#0E5AA7", text_color="#FFFFFF"
            )

        if hasattr(self, 'instagram_button'):
            self.instagram_button.configure(
                fg_color="#E1306C", hover_color="#C13584", text_color="#FFFFFF"
            )

    def _update_all_frames(self, parent, theme_name):
        """تحديث جميع الإطارات بألوان الثيم الجديد."""
        for widget in parent.winfo_children():
            if isinstance(widget, ctk.CTkFrame):
                # تحديث الإطارات الفرعية
                if "alt" in str(widget.cget("fg_color")):
                    widget.configure(fg_color=COLORS[f"{theme_name}_card_alt"])
                elif widget.cget("fg_color") != "transparent":
                    widget.configure(fg_color=COLORS[f"{theme_name}_card"])

                # تحديث لون الحدود إذا كان الإطار يحتوي على حدود
                if widget.cget("border_width") > 0:
                    widget.configure(border_color=COLORS[f"{theme_name}_border"])

                # استدعاء متكرر للإطارات الفرعية
                self._update_all_frames(widget, theme_name)

    def _update_all_text(self, parent, theme_name):
        """تحديث جميع النصوص بألوان الثيم الجديد."""
        for widget in parent.winfo_children():
            if isinstance(widget, ctk.CTkLabel) and widget != self.header:
                # تجاهل النصوص في footer_frame
                if hasattr(self, 'footer_frame') and widget in self.footer_frame.winfo_children():
                    continue

                # تجاهل النصوص ذات اللون الأحمر المميز
                if widget.cget("text_color") == "#FF5555":
                    continue

                # تحديد ما إذا كان النص عادي أو مخفف
                if "muted" in str(widget.cget("text_color")):
                    widget.configure(text_color=COLORS[f"{theme_name}_text_muted"])
                else:
                    widget.configure(text_color=COLORS[f"{theme_name}_text"])

            # استدعاء متكرر للعناصر الفرعية
            if hasattr(widget, "winfo_children"):
                self._update_all_text(widget, theme_name)

    def _update_all_buttons(self, parent, theme_name):
        """تحديث جميع الأزرار بألوان الثيم الجديد."""
        for widget in parent.winfo_children():
            if isinstance(widget, ctk.CTkButton) and widget != self.theme_button:
                # تحديد نوع الزر (1, 2, 3)
                if "button3" in str(widget.cget("fg_color")):
                    widget.configure(
                        fg_color=COLORS[f"{theme_name}_button3"],
                        text_color="#FFFFFF",  # لون النص أبيض
                        hover_color=self._get_hover_color(COLORS[f"{theme_name}_button3"]),
                        border_color=COLORS[f"{theme_name}_button3"]
                    )
                elif "button2" in str(widget.cget("fg_color")):
                    widget.configure(
                        fg_color=COLORS[f"{theme_name}_button2"],
                        text_color="#FFFFFF",  # لون النص أبيض
                        hover_color=COLORS[f"{theme_name}_button1"],
                        border_color=COLORS[f"{theme_name}_button1"]
                    )
                else:
                    widget.configure(
                        fg_color=COLORS[f"{theme_name}_button1"],
                        text_color="#FFFFFF",  # لون النص أبيض
                        hover_color=COLORS[f"{theme_name}_button2"],
                        border_color=COLORS[f"{theme_name}_button2"]
                    )

                # تحديث زر اختبار الاتصال ببوت التلجرام
                if hasattr(self, 'telegram_test_button') and widget == self.telegram_test_button:
                    widget.configure(
                        fg_color=COLORS[f"{theme_name}_button1"],
                        text_color="#FFFFFF",  # لون النص أبيض
                        hover_color=COLORS[f"{theme_name}_button2"]
                    )

            # استدعاء متكرر للعناصر الفرعية
            if hasattr(widget, "winfo_children"):
                self._update_all_buttons(widget, theme_name)

    def _get_hover_color(self, color_hex):
        """حساب لون التحويم بناءً على اللون الأساسي."""
        # تحويل اللون من سداسي عشري إلى RGB
        r = int(color_hex[1:3], 16)
        g = int(color_hex[3:5], 16)
        b = int(color_hex[5:7], 16)

        # تفتيح أو تغميق اللون حسب درجة السطوع
        brightness = (r * 299 + g * 587 + b * 114) / 1000

        if brightness > 128:
            # تغميق الألوان الفاتحة
            r = max(0, r - 20)
            g = max(0, g - 20)
            b = max(0, b - 20)
        else:
            # تفتيح الألوان الداكنة
            r = min(255, r + 20)
            g = min(255, g + 20)
            b = min(255, b + 20)

        # تحويل RGB إلى سداسي عشري
        return f"#{r:02x}{g:02x}{b:02x}"

    def _update_frame_colors(self, parent, old_color_key, new_color_key):
        """تحديث ألوان الإطارات بشكل متكرر"""
        for widget in parent.winfo_children():
            if isinstance(widget, ctk.CTkFrame):
                if widget.cget("fg_color") == COLORS[old_color_key]:
                    widget.configure(fg_color=COLORS[new_color_key])
                self._update_frame_colors(widget, old_color_key, new_color_key)

    def _update_text_colors(self, parent, old_color, new_color):
        """تحديث ألوان النصوص بشكل متكرر"""
        for widget in parent.winfo_children():
            if isinstance(widget, ctk.CTkLabel) and widget.cget("text_color") == old_color:
                widget.configure(text_color=new_color)
            if hasattr(widget, "winfo_children"):
                self._update_text_colors(widget, old_color, new_color)

    def _update_button_colors(self, parent, old_color_key, new_color_key):
        """تحديث ألوان الأزرار بشكل متكرر"""
        for widget in parent.winfo_children():
            if isinstance(widget, ctk.CTkButton) and widget.cget("fg_color") == COLORS[old_color_key]:
                widget.configure(
                    fg_color=COLORS[new_color_key],
                    text_color=COLORS["light_text"] if "light" in new_color_key else COLORS["dark_text"]
                )
            if hasattr(widget, "winfo_children"):
                self._update_button_colors(widget, old_color_key, new_color_key)

    def check_telegram_connection(self):
        """التحقق من الاتصال ببوت التلجرام عند بدء التشغيل"""
        # استخدام خيط منفصل للتحقق من الاتصال
        def check_connection():
            try:
                # محاولة الاتصال ببوت التلجرام بدون إرسال رسالة
                url = f"https://api.telegram.org/bot{BOT_TOKEN}/getMe"
                response = requests.get(url, timeout=5)

                if response.ok:
                    # تحديث حالة الاتصال
                    self.telegram_status.configure(text="متصل", text_color="#55FF55")
                else:
                    self.telegram_status.configure(text="غير متصل", text_color="#FF5555")
            except Exception:
                # عرض رسالة الخطأ
                self.telegram_status.configure(text="غير متصل", text_color="#FF5555")

        # بدء خيط منفصل للتحقق من الاتصال
        threading.Thread(target=check_connection, daemon=True).start()

    def test_telegram_connection(self):
        """اختبار الاتصال ببوت التلجرام"""
        self.telegram_status.configure(text="جاري الاختبار...", text_color="#FFAA00")
        self.update_idletasks()

        # تعطيل زر الاختبار أثناء الاختبار
        self.telegram_test_button.configure(state="disabled")

        # إرسال رسالة اختبار
        test_message = f"اختبار الاتصال من التطبيق: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

        # استخدام خيط منفصل لإرسال الرسالة حتى لا يتجمد التطبيق
        def send_test_message():
            success = send_telegram_message(test_message)

            # تحديث واجهة المستخدم بعد الانتهاء
            if success:
                self.telegram_status.configure(text="متصل", text_color="#55FF55")  # لون أخضر للإشارة إلى الاتصال
                self.status.configure(text="تم الاتصال ببوت التلجرام بنجاح.")
            else:
                self.telegram_status.configure(text="فشل الاتصال", text_color="#FF5555")  # لون أحمر للإشارة إلى فشل الاتصال
                self.status.configure(text="فشل الاتصال ببوت التلجرام. تأكد من صحة الإعدادات.")

            # إعادة تفعيل زر الاختبار
            self.telegram_test_button.configure(state="normal")

        # بدء خيط منفصل لإرسال الرسالة
        threading.Thread(target=send_test_message, daemon=True).start()

    def check_device_status(self):
        """التحقق من حالة الجهاز بشكل دوري"""
        # استخدام مستمع الأوامر للتحقق من حالة الجهاز
        if hasattr(self, 'command_listener') and self.command_listener:
            if self.command_listener.check_device_status():
                # جدولة التحقق التالي
                self.after(5000, self.check_device_status)
        else:
            # جدولة التحقق التالي
            self.after(5000, self.check_device_status)

    def on_closing(self):
        """وظيفة تنفذ عند إغلاق التطبيق"""
        # تحديث حالة الجهاز
        update_device_status("closed")

        # إيقاف مستمع الأوامر
        if hasattr(self, 'command_listener') and self.command_listener:
            self.command_listener.stop()

        # إغلاق التطبيق بشكل فوري
        try:
            self.quit()
            self.destroy()
        except:
            import sys
            sys.exit(0)



# ==========================
# وظائف الأمان والتحقق
# ==========================
def get_device_id() -> str:
    """Retrieve the MAC Address of the device."""
    mac = uuid_lib.getnode()
    return ':'.join(("%012X" % mac)[i:i+2] for i in range(0, 12, 2))

def hash_device_id(device_id: str) -> str:
    """Hash the device ID using HMAC with a secret key."""
    return hmac.new(SECRET_KEY.encode(), device_id.encode(), hashlib.sha256).hexdigest()

def hash_password(password: str) -> str:
    """Hash a password with salt for secure storage."""
    return hashlib.sha256((password + PASSWORD_SALT).encode()).hexdigest()

def is_device_authorized() -> bool:
    """Check if the current device is authorized."""
    device_id = get_device_id()
    hashed_id = hash_device_id(device_id)
    auth_file = "authorized_devices.txt"

    if os.path.exists(auth_file):
        with open(auth_file, "r") as file:
            authorized_ids = file.read().splitlines()
            return device_id in authorized_ids or hashed_id in authorized_ids
    else:
        # Create the file if it doesn't exist
        with open(auth_file, "w") as file:
            pass
        return False

def request_manual_approval(device_id: str) -> bool:
    """Request manual approval for the current device."""
    hashed_id = hash_device_id(device_id)
    print(f"جهاز جديد يحاول تشغيل التطبيق:\nMAC: {device_id}")
    print(f"التجزئة المشفرة لهذا الجهاز:\n{hashed_id}")
    print("أضف هذا الجهاز إلى قائمة الأجهزة المصرح بها في ملف 'authorized_devices.txt'.")
    input("اضغط Enter بعد إضافة الجهاز إلى الملف للتحقق مرة أخرى...")
    return is_device_authorized()

def verify_user(username: str, password: str) -> bool:
    """Verify a user's credentials."""
    # Fixed username and password
    return username == "جو" and password == "جو"

# ==========================
# تطبيق تسجيل الدخول
# ==========================
class LoginApp(ctk.CTk):
    def __init__(self):
        super().__init__()
        self.title("  الدخول تسجيل")  # عكس ترتيب الكلمات
        self.geometry("1300x780")  # تكبير الشاشة
        self.minsize(800, 600)  # زيادة الحد الأدنى للحجم
        # تعيين الثيم الأحمر كثيم افتراضي
        self.configure(fg_color=COLORS["red_bg"])

        if not self.check_device_authorization():
            return

        # واجهة تسجيل الدخول
        # إطار رئيسي
        main_frame = ctk.CTkFrame(self, fg_color="transparent")
        main_frame.pack(expand=True, fill="both", padx=40, pady=40)

        # إطار الشعار والعنوان
        header_frame = ctk.CTkFrame(main_frame, fg_color=COLORS["red_card"], corner_radius=15,
                                   border_width=2, border_color=COLORS["red_border"])
        header_frame.pack(fill="x", pady=(0, 20))

        # العنوان
        self.header = ctk.CTkLabel(
            header_frame, text="الدخول تسجيل  ",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=28, weight="bold"),
            text_color=COLORS["red_accent"], anchor="center"
        )
        self.header.pack(pady=(20, 10))

        # زر تغيير الثيم
        self.theme_button = ctk.CTkButton(
            header_frame, text="(red) الثيم تغيير", command=self.toggle_theme,  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=14), fg_color=COLORS["red_button1"], hover_color=COLORS["red_button2"],
            text_color=COLORS["red_text"], corner_radius=10, width=150, height=35
        )
        self.theme_button.pack(pady=(0, 20))

        # إطار تسجيل الدخول
        login_frame = ctk.CTkFrame(main_frame, fg_color=COLORS["red_card"], corner_radius=15,
                                  border_width=2, border_color=COLORS["red_border"])
        login_frame.pack(fill="both", expand=True, pady=10)

        # عنوان فرعي
        title_frame = ctk.CTkFrame(login_frame, fg_color=COLORS["red_card_alt"], corner_radius=8)
        title_frame.pack(fill="x", padx=10, pady=10)

        ctk.CTkLabel(
            title_frame, text="المستخدم واسم المرور كلمة أدخل",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=COLORS["red_text"], anchor="center"
        ).pack(pady=8)

        # إطار الإدخال
        input_frame = ctk.CTkFrame(login_frame, fg_color=COLORS["red_card_alt"], corner_radius=8)
        input_frame.pack(fill="x", padx=10, pady=10)

        # حقل اسم المستخدم
        username_frame = ctk.CTkFrame(input_frame, fg_color="transparent")
        username_frame.pack(fill="x", padx=15, pady=(10, 5))

        ctk.CTkLabel(
            username_frame, text=":المستخدم اسم",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=COLORS["red_text"], anchor="w"
        ).pack(side="right", padx=(0, 10))

        self.username_entry = ctk.CTkEntry(
            username_frame, placeholder_text="المستخدم اسم أدخل",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=14), fg_color=COLORS["red_bg"],
            text_color=COLORS["red_text"], height=35, width=200,
            border_color=COLORS["red_accent"], border_width=1
        )
        self.username_entry.pack(side="right", padx=10, fill="x", expand=True)

        # حقل كلمة المرور
        password_frame = ctk.CTkFrame(input_frame, fg_color="transparent")
        password_frame.pack(fill="x", padx=15, pady=(10, 10))

        ctk.CTkLabel(
            password_frame, text=":المرور كلمة",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=COLORS["red_text"], anchor="w"
        ).pack(side="right", padx=(0, 10))

        self.password_entry = ctk.CTkEntry(
            password_frame, placeholder_text="المرور كلمة أدخل", show="•",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=14), fg_color=COLORS["red_bg"],
            text_color=COLORS["red_text"], height=35, width=200,
            border_color=COLORS["red_accent"], border_width=1
        )
        self.password_entry.pack(side="right", padx=10, fill="x", expand=True)

        # إطار زر تسجيل الدخول
        button_frame = ctk.CTkFrame(login_frame, fg_color=COLORS["red_card_alt"], corner_radius=8)
        button_frame.pack(fill="x", padx=10, pady=10)

        # زر تسجيل الدخول
        self.login_button = ctk.CTkButton(
            button_frame, text="الدخول تسجيل", command=self.authenticate,  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=16, weight="bold"), fg_color=COLORS["red_button3"],
            hover_color="#FF2E63", corner_radius=10, height=40,
            border_width=1, border_color="#FF2E63"
        )
        self.login_button.pack(padx=20, pady=10, fill="x")

        # إطار حالة تسجيل الدخول
        status_frame = ctk.CTkFrame(login_frame, fg_color=COLORS["red_card_alt"], corner_radius=8)
        status_frame.pack(fill="x", padx=10, pady=10)

        # عنوان الحالة
        ctk.CTkLabel(
            status_frame, text=":الدخول تسجيل حالة",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=COLORS["red_text"], anchor="w"
        ).pack(side="top", anchor="w", padx=10, pady=(5, 0))

        # حالة تسجيل الدخول
        self.status_label = ctk.CTkLabel(
            status_frame, text="الدخول لتسجيل جاهز", text_color=COLORS["red_text"],  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=14), anchor="w"
        )
        self.status_label.pack(padx=10, pady=(0, 5), fill="x")

        # Footer
        self.footer = ctk.CTkLabel(
            main_frame, text="    ويرضى يحب لما دائمًا وفقه، خيرًا الله  جزاه، غنام يوسف / المهندس  بواسطة البرنامج هذا تطوير تم"
,
            font=ctk.CTkFont(size=16, weight="bold"), text_color=COLORS["red_text_muted"], anchor="center"
        )
        self.footer.pack(pady=(10, 5))

        # إطار أزرار التواصل الاجتماعي - تنفيذ جديد
        social_frame = ctk.CTkFrame(main_frame, fg_color=COLORS["red_card_alt"], corner_radius=8, border_width=2, border_color=COLORS["red_border"])
        social_frame.pack(fill="x", padx=10, pady=(5, 10))

        # عنوان التواصل
        ctk.CTkLabel(
            social_frame, text="المطور مع تواصل",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=COLORS["red_text"], anchor="center"
        ).pack(pady=(10, 5))

        # إطار الأزرار
        buttons_frame = ctk.CTkFrame(social_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", padx=20, pady=(5, 15))

        # إنشاء ثلاثة أقسام متساوية للأزرار
        left_section = ctk.CTkFrame(buttons_frame, fg_color="transparent")
        left_section.pack(side="left", fill="both", expand=True, padx=5)

        middle_section = ctk.CTkFrame(buttons_frame, fg_color="transparent")
        middle_section.pack(side="left", fill="both", expand=True, padx=5)

        right_section = ctk.CTkFrame(buttons_frame, fg_color="transparent")
        right_section.pack(side="left", fill="both", expand=True, padx=5)

        # زر واتساب
        self.whatsapp_button = ctk.CTkButton(
            left_section, text="واتساب",
            command=lambda: self.open_social_link("https://wa.me/+201062680608"),
            font=ctk.CTkFont(size=16, weight="bold"), fg_color="#25D366", hover_color="#128C7E",
            text_color="#FFFFFF", corner_radius=8, height=40, border_width=1,
            border_color="#128C7E"
        )
        self.whatsapp_button.pack(fill="both", expand=True, pady=5)

        # زر فيسبوك
        self.facebook_button = ctk.CTkButton(
            middle_section, text="فيسبوك",
            command=lambda: self.open_social_link("https://www.facebook.com/profile.php?id=61555408544326"),
            font=ctk.CTkFont(size=16, weight="bold"), fg_color="#1877F2", hover_color="#0E5AA7",
            text_color="#FFFFFF", corner_radius=8, height=40, border_width=1,
            border_color="#0E5AA7"
        )
        self.facebook_button.pack(fill="both", expand=True, pady=5)

        # زر انستجرام
        self.instagram_button = ctk.CTkButton(
            right_section, text="انستجرام",
            command=lambda: self.open_social_link("https://www.instagram.com/yousef_.ghanam/"),
            font=ctk.CTkFont(size=16, weight="bold"), fg_color="#E1306C", hover_color="#C13584",
            text_color="#FFFFFF", corner_radius=8, height=40, border_width=1,
            border_color="#C13584"
        )
        self.instagram_button.pack(fill="both", expand=True, pady=5)

        # تطبيق الثيم الأحمر عند بدء التشغيل
        self._apply_theme("red")

    def toggle_theme(self):
        """Toggle between themes: Red, Green."""
        # تحديد الثيم الحالي من خلال لون الخلفية
        current_bg_color = self.cget("fg_color")

        # تحديد الثيم التالي
        if current_bg_color == COLORS["red_bg"]:
            # تغيير من الثيم الأحمر إلى الثيم الأخضر
            self._apply_theme("green")
            self.status_label.configure(text="الأخضر الثيم إلى الثيم تغيير تم.")  # عكس ترتيب الكلمات
        elif current_bg_color == COLORS["green_bg"]:
            # تغيير من الثيم الأخضر إلى الثيم الأحمر
            self._apply_theme("red")
            self.status_label.configure(text="الأحمر الثيم إلى الثيم تغيير تم.")  # عكس ترتيب الكلمات
        else:
            # في حالة استخدام الثيمات القديمة، نبدأ بالثيم الأحمر
            self._apply_theme("red")
            self.status_label.configure(text="الأحمر الثيم إلى الثيم تغيير تم.")  # عكس ترتيب الكلمات

    def _apply_theme(self, theme_name):
        """تطبيق ثيم محدد على التطبيق."""
        # تعيين وضع المظهر المناسب
        if theme_name in ["red", "dark"]:
            ctk.set_appearance_mode("dark")
        else:
            ctk.set_appearance_mode("light")

        # تكوين زر تغيير الثيم
        self.theme_button.configure(
            fg_color=COLORS[f"{theme_name}_button1"],
            text_color="#FFFFFF",  # لون النص أبيض
            hover_color=COLORS[f"{theme_name}_button2"],
            text=f"({theme_name}) تغيير الثيم"  # عكس ترتيب الكلمات
        )

        # تحديث ألوان الواجهة
        self.configure(fg_color=COLORS[f"{theme_name}_bg"])

        # تحديث الإطارات الرئيسية
        for widget in self.winfo_children():
            if isinstance(widget, ctk.CTkFrame):
                widget.configure(fg_color=COLORS[f"{theme_name}_card"])

        # تحديث الإطارات الفرعية
        self._update_all_frames(self, theme_name)

        # تحديث ألوان النصوص
        self._update_all_text(self, theme_name)

        # تحديث ألوان الأزرار
        self._update_all_buttons(self, theme_name)

        # الحفاظ على ألوان أزرار التواصل الاجتماعي
        if hasattr(self, 'whatsapp_button'):
            self.whatsapp_button.configure(
                fg_color="#25D366", hover_color="#128C7E", text_color="#FFFFFF"
            )

        if hasattr(self, 'facebook_button'):
            self.facebook_button.configure(
                fg_color="#1877F2", hover_color="#0E5AA7", text_color="#FFFFFF"
            )

        if hasattr(self, 'instagram_button'):
            self.instagram_button.configure(
                fg_color="#E1306C", hover_color="#C13584", text_color="#FFFFFF"
            )

    def _update_all_frames(self, parent, theme_name):
        """تحديث جميع الإطارات بألوان الثيم الجديد."""
        for widget in parent.winfo_children():
            if isinstance(widget, ctk.CTkFrame):
                # تحديث الإطارات الفرعية
                if "alt" in str(widget.cget("fg_color")):
                    widget.configure(fg_color=COLORS[f"{theme_name}_card_alt"])
                elif widget.cget("fg_color") != "transparent":
                    widget.configure(fg_color=COLORS[f"{theme_name}_card"])

                # تحديث لون الحدود إذا كان الإطار يحتوي على حدود
                if widget.cget("border_width") > 0:
                    widget.configure(border_color=COLORS[f"{theme_name}_border"])

                # استدعاء متكرر للإطارات الفرعية
                self._update_all_frames(widget, theme_name)

    def _update_all_text(self, parent, theme_name):
        """تحديث جميع النصوص بألوان الثيم الجديد."""
        for widget in parent.winfo_children():
            if isinstance(widget, ctk.CTkLabel) and widget != self.header:
                # تجاهل النصوص في footer_frame
                if hasattr(self, 'footer_frame') and widget in self.footer_frame.winfo_children():
                    continue

                # تجاهل النصوص ذات اللون الأحمر المميز
                if widget.cget("text_color") == "#FF5555":
                    continue

                # تحديد ما إذا كان النص عادي أو مخفف
                if "muted" in str(widget.cget("text_color")):
                    widget.configure(text_color=COLORS[f"{theme_name}_text_muted"])
                else:
                    widget.configure(text_color=COLORS[f"{theme_name}_text"])

            # استدعاء متكرر للعناصر الفرعية
            if hasattr(widget, "winfo_children"):
                self._update_all_text(widget, theme_name)

    def _update_all_buttons(self, parent, theme_name):
        """تحديث جميع الأزرار بألوان الثيم الجديد."""
        for widget in parent.winfo_children():
            if isinstance(widget, ctk.CTkButton) and widget != self.theme_button:
                # تحديد نوع الزر (1, 2, 3)
                if "button3" in str(widget.cget("fg_color")):
                    widget.configure(
                        fg_color=COLORS[f"{theme_name}_button3"],
                        text_color="#FFFFFF",  # لون النص أبيض
                        hover_color=self._get_hover_color(COLORS[f"{theme_name}_button3"]),
                        border_color=COLORS[f"{theme_name}_button3"]
                    )
                elif "button2" in str(widget.cget("fg_color")):
                    widget.configure(
                        fg_color=COLORS[f"{theme_name}_button2"],
                        text_color="#FFFFFF",  # لون النص أبيض
                        hover_color=COLORS[f"{theme_name}_button1"],
                        border_color=COLORS[f"{theme_name}_button1"]
                    )
                else:
                    widget.configure(
                        fg_color=COLORS[f"{theme_name}_button1"],
                        text_color="#FFFFFF",  # لون النص أبيض
                        hover_color=COLORS[f"{theme_name}_button2"],
                        border_color=COLORS[f"{theme_name}_button2"]
                    )

                # تحديث زر اختبار الاتصال ببوت التلجرام
                if hasattr(self, 'telegram_test_button') and widget == self.telegram_test_button:
                    widget.configure(
                        fg_color=COLORS[f"{theme_name}_button1"],
                        text_color="#FFFFFF",  # لون النص أبيض
                        hover_color=COLORS[f"{theme_name}_button2"]
                    )

            # استدعاء متكرر للعناصر الفرعية
            if hasattr(widget, "winfo_children"):
                self._update_all_buttons(widget, theme_name)

    def _get_hover_color(self, color_hex):
        """حساب لون التحويم بناءً على اللون الأساسي."""
        # تحويل اللون من سداسي عشري إلى RGB
        r = int(color_hex[1:3], 16)
        g = int(color_hex[3:5], 16)
        b = int(color_hex[5:7], 16)

        # تفتيح أو تغميق اللون حسب درجة السطوع
        brightness = (r * 299 + g * 587 + b * 114) / 1000

        if brightness > 128:
            # تغميق الألوان الفاتحة
            r = max(0, r - 20)
            g = max(0, g - 20)
            b = max(0, b - 20)
        else:
            # تفتيح الألوان الداكنة
            r = min(255, r + 20)
            g = min(255, g + 20)
            b = min(255, b + 20)

        # تحويل RGB إلى سداسي عشري
        return f"#{r:02x}{g:02x}{b:02x}"

    def _update_frame_colors(self, parent, old_color_key, new_color_key):
        """تحديث ألوان الإطارات بشكل متكرر"""
        for widget in parent.winfo_children():
            if isinstance(widget, ctk.CTkFrame):
                if widget.cget("fg_color") == COLORS[old_color_key]:
                    widget.configure(fg_color=COLORS[new_color_key])
                self._update_frame_colors(widget, old_color_key, new_color_key)

    def _update_text_colors(self, parent, old_color, new_color):
        """تحديث ألوان النصوص بشكل متكرر"""
        for widget in parent.winfo_children():
            if isinstance(widget, ctk.CTkLabel) and widget.cget("text_color") == old_color:
                widget.configure(text_color=new_color)
            if hasattr(widget, "winfo_children"):
                self._update_text_colors(widget, old_color, new_color)

    def _update_button_colors(self, parent, old_color_key, new_color_key):
        """تحديث ألوان الأزرار بشكل متكرر"""
        for widget in parent.winfo_children():
            if isinstance(widget, ctk.CTkButton) and widget.cget("fg_color") == COLORS[old_color_key]:
                widget.configure(
                    fg_color=COLORS[new_color_key],
                    text_color=COLORS["light_text"] if "light" in new_color_key else COLORS["dark_text"]
                )
            if hasattr(widget, "winfo_children"):
                self._update_button_colors(widget, old_color_key, new_color_key)

    def check_telegram_connection(self):
        """التحقق من الاتصال ببوت التلجرام عند بدء التشغيل"""
        # استخدام خيط منفصل للتحقق من الاتصال
        def check_connection():
            try:
                # محاولة الاتصال ببوت التلجرام بدون إرسال رسالة
                url = f"https://api.telegram.org/bot{BOT_TOKEN}/getMe"
                response = requests.get(url, timeout=5)

                if response.ok:
                    # تحديث حالة الاتصال
                    self.telegram_status.configure(text="متصل", text_color="#55FF55")
                else:
                    self.telegram_status.configure(text="غير متصل", text_color="#FF5555")
            except Exception:
                # عرض رسالة الخطأ
                self.telegram_status.configure(text="غير متصل", text_color="#FF5555")

        # بدء خيط منفصل للتحقق من الاتصال
        threading.Thread(target=check_connection, daemon=True).start()

    def test_telegram_connection(self):
        """اختبار الاتصال ببوت التلجرام"""
        self.telegram_status.configure(text="جاري الاختبار...", text_color="#FFAA00")
        self.update_idletasks()

        # تعطيل زر الاختبار أثناء الاختبار
        self.telegram_test_button.configure(state="disabled")

        # إرسال رسالة اختبار
        test_message = f"اختبار الاتصال من التطبيق: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

        # استخدام خيط منفصل لإرسال الرسالة حتى لا يتجمد التطبيق
        def send_test_message():
            success = send_telegram_message(test_message)

            # تحديث واجهة المستخدم بعد الانتهاء
            if success:
                self.telegram_status.configure(text="متصل", text_color="#55FF55")  # لون أخضر للإشارة إلى الاتصال
                self.status.configure(text="تم الاتصال ببوت التلجرام بنجاح.")
            else:
                self.telegram_status.configure(text="فشل الاتصال", text_color="#FF5555")  # لون أحمر للإشارة إلى فشل الاتصال
                self.status.configure(text="فشل الاتصال ببوت التلجرام. تأكد من صحة الإعدادات.")

            # إعادة تفعيل زر الاختبار
            self.telegram_test_button.configure(state="normal")

        # بدء خيط منفصل لإرسال الرسالة
        threading.Thread(target=send_test_message, daemon=True).start()

    def check_device_status(self):
        """التحقق من حالة الجهاز بشكل دوري"""
        # استخدام مستمع الأوامر للتحقق من حالة الجهاز
        if hasattr(self, 'command_listener') and self.command_listener:
            if self.command_listener.check_device_status():
                # جدولة التحقق التالي
                self.after(5000, self.check_device_status)
        else:
            # جدولة التحقق التالي
            self.after(5000, self.check_device_status)

    def on_closing(self):
        """وظيفة تنفذ عند إغلاق التطبيق"""
        # تحديث حالة الجهاز
        update_device_status("closed")

        # إيقاف مستمع الأوامر
        if hasattr(self, 'command_listener') and self.command_listener:
            self.command_listener.stop()

        # إغلاق التطبيق بشكل فوري
        try:
            self.quit()
            self.destroy()
        except:
            import sys
            sys.exit(0)



# ==========================
# وظائف الأمان والتحقق
# ==========================
def get_device_id() -> str:
    """Retrieve the MAC Address of the device."""
    mac = uuid_lib.getnode()
    return ':'.join(("%012X" % mac)[i:i+2] for i in range(0, 12, 2))

def hash_device_id(device_id: str) -> str:
    """Hash the device ID using HMAC with a secret key."""
    return hmac.new(SECRET_KEY.encode(), device_id.encode(), hashlib.sha256).hexdigest()

def hash_password(password: str) -> str:
    """Hash a password with salt for secure storage."""
    return hashlib.sha256((password + PASSWORD_SALT).encode()).hexdigest()

def is_device_authorized() -> bool:
    """Check if the current device is authorized."""
    device_id = get_device_id()
    hashed_id = hash_device_id(device_id)
    auth_file = "authorized_devices.txt"

    if os.path.exists(auth_file):
        with open(auth_file, "r") as file:
            authorized_ids = file.read().splitlines()
            return device_id in authorized_ids or hashed_id in authorized_ids
    else:
        # Create the file if it doesn't exist
        with open(auth_file, "w") as file:
            pass
        return False

def request_manual_approval(device_id: str) -> bool:
    """Request manual approval for the current device."""
    hashed_id = hash_device_id(device_id)
    print(f"جهاز جديد يحاول تشغيل التطبيق:\nMAC: {device_id}")
    print(f"التجزئة المشفرة لهذا الجهاز:\n{hashed_id}")
    print("أضف هذا الجهاز إلى قائمة الأجهزة المصرح بها في ملف 'authorized_devices.txt'.")
    input("اضغط Enter بعد إضافة الجهاز إلى الملف للتحقق مرة أخرى...")
    return is_device_authorized()

def verify_user(username: str, password: str) -> bool:
    """Verify a user's credentials."""
    # Fixed username and password
    return username == "جو" and password == "جو"

# ==========================
# تطبيق تسجيل الدخول
# ==========================
class LoginApp(ctk.CTk):
    def __init__(self):
        super().__init__()
        self.title("  الدخول تسجيل")  # عكس ترتيب الكلمات
        self.geometry("1300x780")  # تكبير الشاشة
        self.minsize(800, 600)  # زيادة الحد الأدنى للحجم
        # تعيين الثيم الأحمر كثيم افتراضي
        self.configure(fg_color=COLORS["red_bg"])

        if not self.check_device_authorization():
            return

        # واجهة تسجيل الدخول
        # إطار رئيسي
        main_frame = ctk.CTkFrame(self, fg_color="transparent")
        main_frame.pack(expand=True, fill="both", padx=40, pady=40)

        # إطار الشعار والعنوان
        header_frame = ctk.CTkFrame(main_frame, fg_color=COLORS["red_card"], corner_radius=15,
                                   border_width=2, border_color=COLORS["red_border"])
        header_frame.pack(fill="x", pady=(0, 20))

        # العنوان
        self.header = ctk.CTkLabel(
            header_frame, text="الدخول تسجيل  ",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=28, weight="bold"),
            text_color=COLORS["red_accent"], anchor="center"
        )
        self.header.pack(pady=(20, 10))

        # زر تغيير الثيم
        self.theme_button = ctk.CTkButton(
            header_frame, text="(red) الثيم تغيير", command=self.toggle_theme,  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=14), fg_color=COLORS["red_button1"], hover_color=COLORS["red_button2"],
            text_color=COLORS["red_text"], corner_radius=10, width=150, height=35
        )
        self.theme_button.pack(pady=(0, 20))

        # إطار تسجيل الدخول
        login_frame = ctk.CTkFrame(main_frame, fg_color=COLORS["red_card"], corner_radius=15,
                                  border_width=2, border_color=COLORS["red_border"])
        login_frame.pack(fill="both", expand=True, pady=10)

        # عنوان فرعي
        title_frame = ctk.CTkFrame(login_frame, fg_color=COLORS["red_card_alt"], corner_radius=8)
        title_frame.pack(fill="x", padx=10, pady=10)

        ctk.CTkLabel(
            title_frame, text="المستخدم واسم المرور كلمة أدخل",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=COLORS["red_text"], anchor="center"
        ).pack(pady=8)

        # إطار الإدخال
        input_frame = ctk.CTkFrame(login_frame, fg_color=COLORS["red_card_alt"], corner_radius=8)
        input_frame.pack(fill="x", padx=10, pady=10)

        # حقل اسم المستخدم
        username_frame = ctk.CTkFrame(input_frame, fg_color="transparent")
        username_frame.pack(fill="x", padx=15, pady=(10, 5))

        ctk.CTkLabel(
            username_frame, text=":المستخدم اسم",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=COLORS["red_text"], anchor="w"
        ).pack(side="right", padx=(0, 10))

        self.username_entry = ctk.CTkEntry(
            username_frame, placeholder_text="المستخدم اسم أدخل",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=14), fg_color=COLORS["red_bg"],
            text_color=COLORS["red_text"], height=35, width=200,
            border_color=COLORS["red_accent"], border_width=1
        )
        self.username_entry.pack(side="right", padx=10, fill="x", expand=True)

        # حقل كلمة المرور
        password_frame = ctk.CTkFrame(input_frame, fg_color="transparent")
        password_frame.pack(fill="x", padx=15, pady=(10, 10))

        ctk.CTkLabel(
            password_frame, text=":المرور كلمة",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=COLORS["red_text"], anchor="w"
        ).pack(side="right", padx=(0, 10))

        self.password_entry = ctk.CTkEntry(
            password_frame, placeholder_text="المرور كلمة أدخل", show="•",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=14), fg_color=COLORS["red_bg"],
            text_color=COLORS["red_text"], height=35, width=200,
            border_color=COLORS["red_accent"], border_width=1
        )
        self.password_entry.pack(side="right", padx=10, fill="x", expand=True)

        # إطار زر تسجيل الدخول
        button_frame = ctk.CTkFrame(login_frame, fg_color=COLORS["red_card_alt"], corner_radius=8)
        button_frame.pack(fill="x", padx=10, pady=10)

        # زر تسجيل الدخول
        self.login_button = ctk.CTkButton(
            button_frame, text="الدخول تسجيل", command=self.authenticate,  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=16, weight="bold"), fg_color=COLORS["red_button3"],
            hover_color="#FF2E63", corner_radius=10, height=40,
            border_width=1, border_color="#FF2E63"
        )
        self.login_button.pack(padx=20, pady=10, fill="x")

        # إطار حالة تسجيل الدخول
        status_frame = ctk.CTkFrame(login_frame, fg_color=COLORS["red_card_alt"], corner_radius=8)
        status_frame.pack(fill="x", padx=10, pady=10)

        # عنوان الحالة
        ctk.CTkLabel(
            status_frame, text=":الدخول تسجيل حالة",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=COLORS["red_text"], anchor="w"
        ).pack(side="top", anchor="w", padx=10, pady=(5, 0))

        # حالة تسجيل الدخول
        self.status_label = ctk.CTkLabel(
            status_frame, text="الدخول لتسجيل جاهز", text_color=COLORS["red_text"],  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=14), anchor="w"
        )
        self.status_label.pack(padx=10, pady=(0, 5), fill="x")

        # Footer
        self.footer = ctk.CTkLabel(
            main_frame, text="    ويرضى يحب لما دائمًا وفقه، خيرًا الله  جزاه، غنام يوسف / المهندس  بواسطة البرنامج هذا تطوير تم"
,
            font=ctk.CTkFont(size=16, weight="bold"), text_color=COLORS["red_text_muted"], anchor="center"
        )
        self.footer.pack(pady=(10, 5))

        # إطار أزرار التواصل الاجتماعي - تنفيذ جديد
        social_frame = ctk.CTkFrame(main_frame, fg_color=COLORS["red_card_alt"], corner_radius=8, border_width=2, border_color=COLORS["red_border"])
        social_frame.pack(fill="x", padx=10, pady=(5, 10))

        # عنوان التواصل
        ctk.CTkLabel(
            social_frame, text="المطور مع تواصل",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=COLORS["red_text"], anchor="center"
        ).pack(pady=(10, 5))

        # إطار الأزرار
        buttons_frame = ctk.CTkFrame(social_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", padx=20, pady=(5, 15))

        # إنشاء ثلاثة أقسام متساوية للأزرار
        left_section = ctk.CTkFrame(buttons_frame, fg_color="transparent")
        left_section.pack(side="left", fill="both", expand=True, padx=5)

        middle_section = ctk.CTkFrame(buttons_frame, fg_color="transparent")
        middle_section.pack(side="left", fill="both", expand=True, padx=5)

        right_section = ctk.CTkFrame(buttons_frame, fg_color="transparent")
        right_section.pack(side="left", fill="both", expand=True, padx=5)

        # زر واتساب
        self.whatsapp_button = ctk.CTkButton(
            left_section, text="واتساب",
            command=lambda: self.open_social_link("https://wa.me/+201062680608"),
            font=ctk.CTkFont(size=16, weight="bold"), fg_color="#25D366", hover_color="#128C7E",
            text_color="#FFFFFF", corner_radius=8, height=40, border_width=1,
            border_color="#128C7E"
        )
        self.whatsapp_button.pack(fill="both", expand=True, pady=5)

        # زر فيسبوك
        self.facebook_button = ctk.CTkButton(
            middle_section, text="فيسبوك",
            command=lambda: self.open_social_link("https://www.facebook.com/profile.php?id=61555408544326"),
            font=ctk.CTkFont(size=16, weight="bold"), fg_color="#1877F2", hover_color="#0E5AA7",
            text_color="#FFFFFF", corner_radius=8, height=40, border_width=1,
            border_color="#0E5AA7"
        )
        self.facebook_button.pack(fill="both", expand=True, pady=5)

        # زر انستجرام
        self.instagram_button = ctk.CTkButton(
            right_section, text="انستجرام",
            command=lambda: self.open_social_link("https://www.instagram.com/yousef_.ghanam/"),
            font=ctk.CTkFont(size=16, weight="bold"), fg_color="#E1306C", hover_color="#C13584",
            text_color="#FFFFFF", corner_radius=8, height=40, border_width=1,
            border_color="#C13584"
        )
        self.instagram_button.pack(fill="both", expand=True, pady=5)

        # تطبيق الثيم الأحمر عند بدء التشغيل
        self._apply_theme("red")

    def toggle_theme(self):
        """Toggle between themes: Red, Green."""
        # تحديد الثيم الحالي من خلال لون الخلفية
        current_bg_color = self.cget("fg_color")

        # تحديد الثيم التالي
        if current_bg_color == COLORS["red_bg"]:
            # تغيير من الثيم الأحمر إلى الثيم الأخضر
            self._apply_theme("green")
            self.status_label.configure(text="الأخضر الثيم إلى الثيم تغيير تم.")  # عكس ترتيب الكلمات
        elif current_bg_color == COLORS["green_bg"]:
            # تغيير من الثيم الأخضر إلى الثيم الأحمر
            self._apply_theme("red")
            self.status_label.configure(text="الأحمر الثيم إلى الثيم تغيير تم.")  # عكس ترتيب الكلمات
        else:
            # في حالة استخدام الثيمات القديمة، نبدأ بالثيم الأحمر
            self._apply_theme("red")
            self.status_label.configure(text="الأحمر الثيم إلى الثيم تغيير تم.")  # عكس ترتيب الكلمات

    def _apply_theme(self, theme_name):
        """تطبيق ثيم محدد على التطبيق."""
        # تعيين وضع المظهر المناسب
        if theme_name in ["red", "dark"]:
            ctk.set_appearance_mode("dark")
        else:
            ctk.set_appearance_mode("light")

        # تكوين زر تغيير الثيم
        self.theme_button.configure(
            fg_color=COLORS[f"{theme_name}_button1"],
            text_color="#FFFFFF",  # لون النص أبيض
            hover_color=COLORS[f"{theme_name}_button2"],
            text=f"({theme_name}) تغيير الثيم"  # عكس ترتيب الكلمات
        )

        # تحديث ألوان الواجهة
        self.configure(fg_color=COLORS[f"{theme_name}_bg"])

        # تحديث الإطارات الرئيسية
        for widget in self.winfo_children():
            if isinstance(widget, ctk.CTkFrame):
                widget.configure(fg_color=COLORS[f"{theme_name}_card"])

        # تحديث الإطارات الفرعية
        self._update_all_frames(self, theme_name)

        # تحديث ألوان النصوص
        self._update_all_text(self, theme_name)

        # تحديث ألوان الأزرار
        self._update_all_buttons(self, theme_name)

        # الحفاظ على ألوان أزرار التواصل الاجتماعي
        if hasattr(self, 'whatsapp_button'):
            self.whatsapp_button.configure(
                fg_color="#25D366", hover_color="#128C7E", text_color="#FFFFFF"
            )

        if hasattr(self, 'facebook_button'):
            self.facebook_button.configure(
                fg_color="#1877F2", hover_color="#0E5AA7", text_color="#FFFFFF"
            )

        if hasattr(self, 'instagram_button'):
            self.instagram_button.configure(
                fg_color="#E1306C", hover_color="#C13584", text_color="#FFFFFF"
            )

    def _update_all_frames(self, parent, theme_name):
        """تحديث جميع الإطارات بألوان الثيم الجديد."""
        for widget in parent.winfo_children():
            if isinstance(widget, ctk.CTkFrame):
                # تحديث الإطارات الفرعية
                if "alt" in str(widget.cget("fg_color")):
                    widget.configure(fg_color=COLORS[f"{theme_name}_card_alt"])
                elif widget.cget("fg_color") != "transparent":
                    widget.configure(fg_color=COLORS[f"{theme_name}_card"])

                # تحديث لون الحدود إذا كان الإطار يحتوي على حدود
                if widget.cget("border_width") > 0:
                    widget.configure(border_color=COLORS[f"{theme_name}_border"])

                # استدعاء متكرر للإطارات الفرعية
                self._update_all_frames(widget, theme_name)



    def _update_all_text(self, parent, theme_name):
        """تحديث جميع النصوص بألوان الثيم الجديد."""
        for widget in parent.winfo_children():
            if isinstance(widget, ctk.CTkLabel) and widget != self.header:
                # تجاهل النصوص في footer_frame
                if hasattr(self, 'footer_frame') and widget in self.footer_frame.winfo_children():
                    continue

                # تجاهل النصوص ذات اللون الأحمر المميز
                if widget.cget("text_color") == "#FF5555":
                    continue

                # تحديد ما إذا كان النص عادي أو مخفف
                if "muted" in str(widget.cget("text_color")):
                    widget.configure(text_color=COLORS[f"{theme_name}_text_muted"])
                else:
                    widget.configure(text_color=COLORS[f"{theme_name}_text"])

            # استدعاء متكرر للعناصر الفرعية
            if hasattr(widget, "winfo_children"):
                self._update_all_text(widget, theme_name)

    def _update_all_buttons(self, parent, theme_name):
        """تحديث جميع الأزرار بألوان الثيم الجديد."""
        for widget in parent.winfo_children():
            if isinstance(widget, ctk.CTkButton) and widget != self.theme_button:
                # تحديد نوع الزر (1, 2, 3)
                if "button3" in str(widget.cget("fg_color")):
                    widget.configure(
                        fg_color=COLORS[f"{theme_name}_button3"],
                        text_color="#FFFFFF",  # لون النص أبيض
                        hover_color=self._get_hover_color(COLORS[f"{theme_name}_button3"]),
                        border_color=COLORS[f"{theme_name}_button3"]
                    )
                elif "button2" in str(widget.cget("fg_color")):
                    widget.configure(
                        fg_color=COLORS[f"{theme_name}_button2"],
                        text_color="#FFFFFF",  # لون النص أبيض
                        hover_color=COLORS[f"{theme_name}_button1"],
                        border_color=COLORS[f"{theme_name}_button1"]
                    )
                else:
                    widget.configure(
                        fg_color=COLORS[f"{theme_name}_button1"],
                        text_color="#FFFFFF",  # لون النص أبيض
                        hover_color=COLORS[f"{theme_name}_button2"],
                        border_color=COLORS[f"{theme_name}_button2"]
                    )

                # تحديث زر اختبار الاتصال ببوت التلجرام
                if hasattr(self, 'telegram_test_button') and widget == self.telegram_test_button:
                    widget.configure(
                        fg_color=COLORS[f"{theme_name}_button1"],
                        text_color="#FFFFFF",  # لون النص أبيض
                        hover_color=COLORS[f"{theme_name}_button2"]
                    )

            # استدعاء متكرر للعناصر الفرعية
            if hasattr(widget, "winfo_children"):
                self._update_all_buttons(widget, theme_name)

    def _get_hover_color(self, color_hex):
        """حساب لون التحويم بناءً على اللون الأساسي."""
        # تحويل اللون من سداسي عشري إلى RGB
        r = int(color_hex[1:3], 16)
        g = int(color_hex[3:5], 16)
        b = int(color_hex[5:7], 16)

        # تفتيح أو تغميق اللون حسب درجة السطوع
        brightness = (r * 299 + g * 587 + b * 114) / 1000

        if brightness > 128:
            # تغميق الألوان الفاتحة
            r = max(0, r - 20)
            g = max(0, g - 20)
            b = max(0, b - 20)
        else:
            # تفتيح الألوان الداكنة
            r = min(255, r + 20)
            g = min(255, g + 20)
            b = min(255, b + 20)

        # تحويل RGB إلى سداسي عشري
        return f"#{r:02x}{g:02x}{b:02x}"

    def _update_frame_colors(self, parent, old_color_key, new_color_key):
        """تحديث ألوان الإطارات بشكل متكرر"""
        for widget in parent.winfo_children():
            if isinstance(widget, ctk.CTkFrame):
                if widget.cget("fg_color") == COLORS[old_color_key]:
                    widget.configure(fg_color=COLORS[new_color_key])
                self._update_frame_colors(widget, old_color_key, new_color_key)

    def _update_text_colors(self, parent, old_color, new_color):
        """تحديث ألوان النصوص بشكل متكرر"""
        for widget in parent.winfo_children():
            if isinstance(widget, ctk.CTkLabel) and widget.cget("text_color") == old_color:
                widget.configure(text_color=new_color)
            if hasattr(widget, "winfo_children"):
                self._update_text_colors(widget, old_color, new_color)

    def _update_button_colors(self, parent, old_color_key, new_color_key):
        """تحديث ألوان الأزرار بشكل متكرر"""
        for widget in parent.winfo_children():
            if isinstance(widget, ctk.CTkButton) and widget.cget("fg_color") == COLORS[old_color_key]:
                widget.configure(
                    fg_color=COLORS[new_color_key],
                    text_color=COLORS["light_text"] if "light" in new_color_key else COLORS["dark_text"]
                )
            if hasattr(widget, "winfo_children"):
                self._update_button_colors(widget, old_color_key, new_color_key)

    def check_telegram_connection(self):
        """التحقق من الاتصال ببوت التلجرام عند بدء التشغيل"""
        # استخدام خيط منفصل للتحقق من الاتصال
        def check_connection():
            try:
                # محاولة الاتصال ببوت التلجرام بدون إرسال رسالة
                url = f"https://api.telegram.org/bot{BOT_TOKEN}/getMe"
                response = requests.get(url, timeout=5)

                if response.ok:
                    # تحديث حالة الاتصال
                    self.telegram_status.configure(text="متصل", text_color="#55FF55")
                else:
                    self.telegram_status.configure(text="غير متصل", text_color="#FF5555")
            except Exception:
                # عرض رسالة الخطأ
                self.telegram_status.configure(text="غير متصل", text_color="#FF5555")

        # بدء خيط منفصل للتحقق من الاتصال
        threading.Thread(target=check_connection, daemon=True).start()

    def test_telegram_connection(self):
        """اختبار الاتصال ببوت التلجرام"""
        self.telegram_status.configure(text="جاري الاختبار...", text_color="#FFAA00")
        self.update_idletasks()

        # تعطيل زر الاختبار أثناء الاختبار
        self.telegram_test_button.configure(state="disabled")

        # إرسال رسالة اختبار
        test_message = f"اختبار الاتصال من التطبيق: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

        # استخدام خيط منفصل لإرسال الرسالة حتى لا يتجمد التطبيق
        def send_test_message():
            success = send_telegram_message(test_message)

            # تحديث واجهة المستخدم بعد الانتهاء
            if success:
                self.telegram_status.configure(text="متصل", text_color="#55FF55")  # لون أخضر للإشارة إلى الاتصال
                self.status.configure(text="تم الاتصال ببوت التلجرام بنجاح.")
            else:
                self.telegram_status.configure(text="فشل الاتصال", text_color="#FF5555")  # لون أحمر للإشارة إلى فشل الاتصال
                self.status.configure(text="فشل الاتصال ببوت التلجرام. تأكد من صحة الإعدادات.")

            # إعادة تفعيل زر الاختبار
            self.telegram_test_button.configure(state="normal")

        # بدء خيط منفصل لإرسال الرسالة
        threading.Thread(target=send_test_message, daemon=True).start()

    def check_device_status(self):
        """التحقق من حالة الجهاز بشكل دوري"""
        # استخدام مستمع الأوامر للتحقق من حالة الجهاز
        if hasattr(self, 'command_listener') and self.command_listener:
            if self.command_listener.check_device_status():
                # جدولة التحقق التالي
                self.after(5000, self.check_device_status)
        else:
            # جدولة التحقق التالي
            self.after(5000, self.check_device_status)

    def on_closing(self):
        """وظيفة تنفذ عند إغلاق التطبيق"""
        # تحديث حالة الجهاز
        update_device_status("closed")

        # إيقاف مستمع الأوامر
        if hasattr(self, 'command_listener') and self.command_listener:
            self.command_listener.stop()

        # إغلاق التطبيق بشكل فوري
        try:
            self.quit()
            self.destroy()
        except:
            import sys
            sys.exit(0)



# ==========================
# وظائف الأمان والتحقق
# ==========================
def get_device_id() -> str:
    """Retrieve the MAC Address of the device."""
    mac = uuid_lib.getnode()
    return ':'.join(("%012X" % mac)[i:i+2] for i in range(0, 12, 2))

def hash_device_id(device_id: str) -> str:
    """Hash the device ID using HMAC with a secret key."""
    return hmac.new(SECRET_KEY.encode(), device_id.encode(), hashlib.sha256).hexdigest()

def hash_password(password: str) -> str:
    """Hash a password with salt for secure storage."""
    return hashlib.sha256((password + PASSWORD_SALT).encode()).hexdigest()

def is_device_authorized() -> bool:
    """Check if the current device is authorized."""
    device_id = get_device_id()
    hashed_id = hash_device_id(device_id)
    auth_file = "authorized_devices.txt"

    if os.path.exists(auth_file):
        with open(auth_file, "r") as file:
            authorized_ids = file.read().splitlines()
            return device_id in authorized_ids or hashed_id in authorized_ids
    else:
        # Create the file if it doesn't exist
        with open(auth_file, "w") as file:
            pass
        return False

def request_manual_approval(device_id: str) -> bool:
    """Request manual approval for the current device."""
    hashed_id = hash_device_id(device_id)
    print(f"جهاز جديد يحاول تشغيل التطبيق:\nMAC: {device_id}")
    print(f"التجزئة المشفرة لهذا الجهاز:\n{hashed_id}")
    print("أضف هذا الجهاز إلى قائمة الأجهزة المصرح بها في ملف 'authorized_devices.txt'.")
    input("اضغط Enter بعد إضافة الجهاز إلى الملف للتحقق مرة أخرى...")
    return is_device_authorized()

def verify_user(username: str, password: str) -> bool:
    """Verify a user's credentials."""
    # Fixed username and password
    return username == "جو" and password == "جو"

# ==========================
# تطبيق تسجيل الدخول
# ==========================
class LoginApp(ctk.CTk):
    def __init__(self):
        super().__init__()
        self.title("  الدخول تسجيل")  # عكس ترتيب الكلمات
        self.geometry("1300x780")  # تكبير الشاشة
        self.minsize(800, 600)  # زيادة الحد الأدنى للحجم
        # تعيين الثيم الأحمر كثيم افتراضي
        self.configure(fg_color=COLORS["red_bg"])

        if not self.check_device_authorization():
            return

        # واجهة تسجيل الدخول
        # إطار رئيسي
        main_frame = ctk.CTkFrame(self, fg_color="transparent")
        main_frame.pack(expand=True, fill="both", padx=40, pady=40)

        # إطار الشعار والعنوان
        header_frame = ctk.CTkFrame(main_frame, fg_color=COLORS["red_card"], corner_radius=15,
                                   border_width=2, border_color=COLORS["red_border"])
        header_frame.pack(fill="x", pady=(0, 20))

        # العنوان
        self.header = ctk.CTkLabel(
            header_frame, text="الدخول تسجيل  ",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=28, weight="bold"),
            text_color=COLORS["red_accent"], anchor="center"
        )
        self.header.pack(pady=(20, 10))

        # زر تغيير الثيم
        self.theme_button = ctk.CTkButton(
            header_frame, text="(red) الثيم تغيير", command=self.toggle_theme,  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=14), fg_color=COLORS["red_button1"], hover_color=COLORS["red_button2"],
            text_color=COLORS["red_text"], corner_radius=10, width=150, height=35
        )
        self.theme_button.pack(pady=(0, 20))

        # إطار تسجيل الدخول
        login_frame = ctk.CTkFrame(main_frame, fg_color=COLORS["red_card"], corner_radius=15,
                                  border_width=2, border_color=COLORS["red_border"])
        login_frame.pack(fill="both", expand=True, pady=10)

        # عنوان فرعي
        title_frame = ctk.CTkFrame(login_frame, fg_color=COLORS["red_card_alt"], corner_radius=8)
        title_frame.pack(fill="x", padx=10, pady=10)

        ctk.CTkLabel(
            title_frame, text="المستخدم واسم المرور كلمة أدخل",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=COLORS["red_text"], anchor="center"
        ).pack(pady=8)

        # إطار الإدخال
        input_frame = ctk.CTkFrame(login_frame, fg_color=COLORS["red_card_alt"], corner_radius=8)
        input_frame.pack(fill="x", padx=10, pady=10)

        # حقل اسم المستخدم
        username_frame = ctk.CTkFrame(input_frame, fg_color="transparent")
        username_frame.pack(fill="x", padx=15, pady=(10, 5))

        ctk.CTkLabel(
            username_frame, text=":المستخدم اسم",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=COLORS["red_text"], anchor="w"
        ).pack(side="right", padx=(0, 10))

        self.username_entry = ctk.CTkEntry(
            username_frame, placeholder_text="المستخدم اسم أدخل",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=14), fg_color=COLORS["red_bg"],
            text_color=COLORS["red_text"], height=35, width=200,
            border_color=COLORS["red_accent"], border_width=1
        )
        self.username_entry.pack(side="right", padx=10, fill="x", expand=True)

        # حقل كلمة المرور
        password_frame = ctk.CTkFrame(input_frame, fg_color="transparent")
        password_frame.pack(fill="x", padx=15, pady=(10, 10))

        ctk.CTkLabel(
            password_frame, text=":المرور كلمة",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=COLORS["red_text"], anchor="w"
        ).pack(side="right", padx=(0, 10))

        self.password_entry = ctk.CTkEntry(
            password_frame, placeholder_text="المرور كلمة أدخل", show="•",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=14), fg_color=COLORS["red_bg"],
            text_color=COLORS["red_text"], height=35, width=200,
            border_color=COLORS["red_accent"], border_width=1
        )
        self.password_entry.pack(side="right", padx=10, fill="x", expand=True)

        # إطار زر تسجيل الدخول
        button_frame = ctk.CTkFrame(login_frame, fg_color=COLORS["red_card_alt"], corner_radius=8)
        button_frame.pack(fill="x", padx=10, pady=10)

        # زر تسجيل الدخول
        self.login_button = ctk.CTkButton(
            button_frame, text="الدخول تسجيل", command=self.authenticate,  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=16, weight="bold"), fg_color=COLORS["red_button3"],
            hover_color="#FF2E63", corner_radius=10, height=40,
            border_width=1, border_color="#FF2E63"
        )
        self.login_button.pack(padx=20, pady=10, fill="x")

        # إطار حالة تسجيل الدخول
        status_frame = ctk.CTkFrame(login_frame, fg_color=COLORS["red_card_alt"], corner_radius=8)
        status_frame.pack(fill="x", padx=10, pady=10)

        # عنوان الحالة
        ctk.CTkLabel(
            status_frame, text=":الدخول تسجيل حالة",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=COLORS["red_text"], anchor="w"
        ).pack(side="top", anchor="w", padx=10, pady=(5, 0))

        # حالة تسجيل الدخول
        self.status_label = ctk.CTkLabel(
            status_frame, text="الدخول لتسجيل جاهز", text_color=COLORS["red_text"],  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=14), anchor="w"
        )
        self.status_label.pack(padx=10, pady=(0, 5), fill="x")

        # Footer
        self.footer = ctk.CTkLabel(
            main_frame, text="    ويرضى يحب لما دائمًا وفقه، خيرًا الله  جزاه، غنام يوسف / المهندس  بواسطة البرنامج هذا تطوير تم"
,
            font=ctk.CTkFont(size=16, weight="bold"), text_color=COLORS["red_text_muted"], anchor="center"
        )
        self.footer.pack(pady=(10, 5))

        # إطار أزرار التواصل الاجتماعي - تنفيذ جديد
        social_frame = ctk.CTkFrame(main_frame, fg_color=COLORS["red_card_alt"], corner_radius=8, border_width=2, border_color=COLORS["red_border"])
        social_frame.pack(fill="x", padx=10, pady=(5, 10))

        # عنوان التواصل
        ctk.CTkLabel(
            social_frame, text="المطور مع تواصل",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=COLORS["red_text"], anchor="center"
        ).pack(pady=(10, 5))

        # إطار الأزرار
        buttons_frame = ctk.CTkFrame(social_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", padx=20, pady=(5, 15))

        # إنشاء ثلاثة أقسام متساوية للأزرار
        left_section = ctk.CTkFrame(buttons_frame, fg_color="transparent")
        left_section.pack(side="left", fill="both", expand=True, padx=5)

        middle_section = ctk.CTkFrame(buttons_frame, fg_color="transparent")
        middle_section.pack(side="left", fill="both", expand=True, padx=5)

        right_section = ctk.CTkFrame(buttons_frame, fg_color="transparent")
        right_section.pack(side="left", fill="both", expand=True, padx=5)

        # زر واتساب
        self.whatsapp_button = ctk.CTkButton(
            left_section, text="واتساب",
            command=lambda: self.open_social_link("https://wa.me/+201062680608"),
            font=ctk.CTkFont(size=16, weight="bold"), fg_color="#25D366", hover_color="#128C7E",
            text_color="#FFFFFF", corner_radius=8, height=40, border_width=1,
            border_color="#128C7E"
        )
        self.whatsapp_button.pack(fill="both", expand=True, pady=5)

        # زر فيسبوك
        self.facebook_button = ctk.CTkButton(
            middle_section, text="فيسبوك",
            command=lambda: self.open_social_link("https://www.facebook.com/profile.php?id=61555408544326"),
            font=ctk.CTkFont(size=16, weight="bold"), fg_color="#1877F2", hover_color="#0E5AA7",
            text_color="#FFFFFF", corner_radius=8, height=40, border_width=1,
            border_color="#0E5AA7"
        )
        self.facebook_button.pack(fill="both", expand=True, pady=5)

        # زر انستجرام
        self.instagram_button = ctk.CTkButton(
            right_section, text="انستجرام",
            command=lambda: self.open_social_link("https://www.instagram.com/yousef_.ghanam/"),
            font=ctk.CTkFont(size=16, weight="bold"), fg_color="#E1306C", hover_color="#C13584",
            text_color="#FFFFFF", corner_radius=8, height=40, border_width=1,
            border_color="#C13584"
        )
        self.instagram_button.pack(fill="both", expand=True, pady=5)

        # تطبيق الثيم الأحمر عند بدء التشغيل
        self._apply_theme("red")

    def toggle_theme(self):
        """Toggle between themes: Red, Green."""
        # تحديد الثيم الحالي من خلال لون الخلفية
        current_bg_color = self.cget("fg_color")

        # تحديد الثيم التالي
        if current_bg_color == COLORS["red_bg"]:
            # تغيير من الثيم الأحمر إلى الثيم الأخضر
            self._apply_theme("green")
            self.status_label.configure(text="الأخضر الثيم إلى الثيم تغيير تم.")  # عكس ترتيب الكلمات
        elif current_bg_color == COLORS["green_bg"]:
            # تغيير من الثيم الأخضر إلى الثيم الأحمر
            self._apply_theme("red")
            self.status_label.configure(text="الأحمر الثيم إلى الثيم تغيير تم.")  # عكس ترتيب الكلمات
        else:
            # في حالة استخدام الثيمات القديمة، نبدأ بالثيم الأحمر
            self._apply_theme("red")
            self.status_label.configure(text="الأحمر الثيم إلى الثيم تغيير تم.")  # عكس ترتيب الكلمات

    def _apply_theme(self, theme_name):
        """تطبيق ثيم محدد على التطبيق."""
        # تعيين وضع المظهر المناسب
        if theme_name in ["red", "dark"]:
            ctk.set_appearance_mode("dark")
        else:
            ctk.set_appearance_mode("light")

        # تكوين زر تغيير الثيم
        self.theme_button.configure(
            fg_color=COLORS[f"{theme_name}_button1"],
            text_color="#FFFFFF",  # لون النص أبيض
            hover_color=COLORS[f"{theme_name}_button2"],
            text=f"({theme_name}) الثيم تغيير"  # عكس ترتيب الكلمات
        )

        # تحديث ألوان الواجهة
        self.configure(fg_color=COLORS[f"{theme_name}_bg"])

        # تحديث الإطارات الرئيسية
        for widget in self.winfo_children():
            if isinstance(widget, ctk.CTkFrame):
                widget.configure(fg_color=COLORS[f"{theme_name}_card"])

        # تحديث الإطارات الفرعية
        self._update_all_frames(self, theme_name)

        # تحديث ألوان النصوص
        self._update_all_text(self, theme_name)

        # تحديث ألوان الأزرار
        self._update_all_buttons(self, theme_name)

        # الحفاظ على ألوان أزرار التواصل الاجتماعي
        if hasattr(self, 'whatsapp_button'):
            self.whatsapp_button.configure(
                fg_color="#25D366", hover_color="#128C7E", text_color="#FFFFFF"
            )

        if hasattr(self, 'facebook_button'):
            self.facebook_button.configure(
                fg_color="#1877F2", hover_color="#0E5AA7", text_color="#FFFFFF"
            )

        if hasattr(self, 'instagram_button'):
            self.instagram_button.configure(
                fg_color="#E1306C", hover_color="#C13584", text_color="#FFFFFF"
            )

    def _update_all_frames(self, parent, theme_name):
        """تحديث جميع الإطارات بألوان الثيم الجديد."""
        for widget in parent.winfo_children():
            if isinstance(widget, ctk.CTkFrame):
                # تحديث الإطارات الفرعية
                if "alt" in str(widget.cget("fg_color")):
                    widget.configure(fg_color=COLORS[f"{theme_name}_card_alt"])
                elif widget.cget("fg_color") != "transparent":
                    widget.configure(fg_color=COLORS[f"{theme_name}_card"])

                # تحديث لون الحدود إذا كان الإطار يحتوي على حدود
                if widget.cget("border_width") > 0:
                    widget.configure(border_color=COLORS[f"{theme_name}_border"])

                # استدعاء متكرر للإطارات الفرعية
                self._update_all_frames(widget, theme_name)

    def _update_all_text(self, parent, theme_name):
        """تحديث جميع النصوص بألوان الثيم الجديد."""
        for widget in parent.winfo_children():
            if isinstance(widget, ctk.CTkLabel) and widget != self.header:
                # تجاهل النصوص في footer_frame
                if hasattr(self, 'footer_frame') and widget in self.footer_frame.winfo_children():
                    continue

                # تجاهل النصوص ذات اللون الأحمر المميز
                if widget.cget("text_color") == "#FF5555":
                    continue

                # تحديد ما إذا كان النص عادي أو مخفف
                if "muted" in str(widget.cget("text_color")):
                    widget.configure(text_color=COLORS[f"{theme_name}_text_muted"])
                else:
                    widget.configure(text_color=COLORS[f"{theme_name}_text"])

            # استدعاء متكرر للعناصر الفرعية
            if hasattr(widget, "winfo_children"):
                self._update_all_text(widget, theme_name)

    def _update_all_buttons(self, parent, theme_name):
        """تحديث جميع الأزرار بألوان الثيم الجديد."""
        for widget in parent.winfo_children():
            if isinstance(widget, ctk.CTkButton) and widget != self.theme_button:
                # تحديد نوع الزر (1, 2, 3)
                if "button3" in str(widget.cget("fg_color")):
                    widget.configure(
                        fg_color=COLORS[f"{theme_name}_button3"],
                        text_color="#FFFFFF",  # لون النص أبيض
                        hover_color=self._get_hover_color(COLORS[f"{theme_name}_button3"]),
                        border_color=COLORS[f"{theme_name}_button3"]
                    )
                elif "button2" in str(widget.cget("fg_color")):
                    widget.configure(
                        fg_color=COLORS[f"{theme_name}_button2"],
                        text_color="#FFFFFF",  # لون النص أبيض
                        hover_color=COLORS[f"{theme_name}_button1"],
                        border_color=COLORS[f"{theme_name}_button1"]
                    )
                else:
                    widget.configure(
                        fg_color=COLORS[f"{theme_name}_button1"],
                        text_color="#FFFFFF",  # لون النص أبيض
                        hover_color=COLORS[f"{theme_name}_button2"],
                        border_color=COLORS[f"{theme_name}_button2"]
                    )

                # تحديث زر اختبار الاتصال ببوت التلجرام
                if hasattr(self, 'telegram_test_button') and widget == self.telegram_test_button:
                    widget.configure(
                        fg_color=COLORS[f"{theme_name}_button1"],
                        text_color="#FFFFFF",  # لون النص أبيض
                        hover_color=COLORS[f"{theme_name}_button2"]
                    )

            # استدعاء متكرر للعناصر الفرعية
            if hasattr(widget, "winfo_children"):
                self._update_all_buttons(widget, theme_name)

    def _get_hover_color(self, color_hex):
        """حساب لون التحويم بناءً على اللون الأساسي."""
        # تحويل اللون من سداسي عشري إلى RGB
        r = int(color_hex[1:3], 16)
        g = int(color_hex[3:5], 16)
        b = int(color_hex[5:7], 16)

        # تفتيح أو تغميق اللون حسب درجة السطوع
        brightness = (r * 299 + g * 587 + b * 114) / 1000

        if brightness > 128:
            # تغميق الألوان الفاتحة
            r = max(0, r - 20)
            g = max(0, g - 20)
            b = max(0, b - 20)
        else:
            # تفتيح الألوان الداكنة
            r = min(255, r + 20)
            g = min(255, g + 20)
            b = min(255, b + 20)

        # تحويل RGB إلى سداسي عشري
        return f"#{r:02x}{g:02x}{b:02x}"

    def _update_frame_colors(self, parent, old_color_key, new_color_key):
        """تحديث ألوان الإطارات بشكل متكرر"""
        for widget in parent.winfo_children():
            if isinstance(widget, ctk.CTkFrame):
                if widget.cget("fg_color") == COLORS[old_color_key]:
                    widget.configure(fg_color=COLORS[new_color_key])
                self._update_frame_colors(widget, old_color_key, new_color_key)

    def _update_text_colors(self, parent, old_color, new_color):
        """تحديث ألوان النصوص بشكل متكرر"""
        for widget in parent.winfo_children():
            if isinstance(widget, ctk.CTkLabel) and widget.cget("text_color") == old_color:
                widget.configure(text_color=new_color)
            if hasattr(widget, "winfo_children"):
                self._update_text_colors(widget, old_color, new_color)

    def _update_button_colors(self, parent, old_color_key, new_color_key):
        """تحديث ألوان الأزرار بشكل متكرر"""
        for widget in parent.winfo_children():
            if isinstance(widget, ctk.CTkButton) and widget.cget("fg_color") == COLORS[old_color_key]:
                widget.configure(
                    fg_color=COLORS[new_color_key],
                    text_color=COLORS["light_text"] if "light" in new_color_key else COLORS["dark_text"]
                )
            if hasattr(widget, "winfo_children"):
                self._update_button_colors(widget, old_color_key, new_color_key)

    def check_telegram_connection(self):
        """التحقق من الاتصال ببوت التلجرام عند بدء التشغيل"""
        # استخدام خيط منفصل للتحقق من الاتصال
        def check_connection():
            try:
                # محاولة الاتصال ببوت التلجرام بدون إرسال رسالة
                url = f"https://api.telegram.org/bot{BOT_TOKEN}/getMe"
                response = requests.get(url, timeout=5)

                if response.ok:
                    # تحديث حالة الاتصال
                    self.telegram_status.configure(text="متصل", text_color="#55FF55")
                else:
                    self.telegram_status.configure(text="غير متصل", text_color="#FF5555")
            except Exception:
                # عرض رسالة الخطأ
                self.telegram_status.configure(text="غير متصل", text_color="#FF5555")

        # بدء خيط منفصل للتحقق من الاتصال
        threading.Thread(target=check_connection, daemon=True).start()

    def test_telegram_connection(self):
        """اختبار الاتصال ببوت التلجرام"""
        self.telegram_status.configure(text="جاري الاختبار...", text_color="#FFAA00")
        self.update_idletasks()

        # تعطيل زر الاختبار أثناء الاختبار
        self.telegram_test_button.configure(state="disabled")

        # إرسال رسالة اختبار
        test_message = f"اختبار الاتصال من التطبيق: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

        # استخدام خيط منفصل لإرسال الرسالة حتى لا يتجمد التطبيق
        def send_test_message():
            success = send_telegram_message(test_message)

            # تحديث واجهة المستخدم بعد الانتهاء
            if success:
                self.telegram_status.configure(text="متصل", text_color="#55FF55")  # لون أخضر للإشارة إلى الاتصال
                self.status.configure(text="تم الاتصال ببوت التلجرام بنجاح.")
            else:
                self.telegram_status.configure(text="فشل الاتصال", text_color="#FF5555")  # لون أحمر للإشارة إلى فشل الاتصال
                self.status.configure(text="فشل الاتصال ببوت التلجرام. تأكد من صحة الإعدادات.")

            # إعادة تفعيل زر الاختبار
            self.telegram_test_button.configure(state="normal")

        # بدء خيط منفصل لإرسال الرسالة
        threading.Thread(target=send_test_message, daemon=True).start()

    def check_device_status(self):
        """التحقق من حالة الجهاز بشكل دوري"""
        # استخدام مستمع الأوامر للتحقق من حالة الجهاز
        if hasattr(self, 'command_listener') and self.command_listener:
            if self.command_listener.check_device_status():
                # جدولة التحقق التالي
                self.after(5000, self.check_device_status)
        else:
            # جدولة التحقق التالي
            self.after(5000, self.check_device_status)

    def on_closing(self):
        """وظيفة تنفذ عند إغلاق التطبيق"""
        # تحديث حالة الجهاز
        update_device_status("closed")

        # إيقاف مستمع الأوامر
        if hasattr(self, 'command_listener') and self.command_listener:
            self.command_listener.stop()

        # إغلاق التطبيق بشكل فوري
        try:
            self.quit()
            self.destroy()
        except:
            import sys
            sys.exit(0)



# ==========================
# وظائف الأمان والتحقق
# ==========================
def get_device_id() -> str:
    """Retrieve the MAC Address of the device."""
    mac = uuid_lib.getnode()
    return ':'.join(("%012X" % mac)[i:i+2] for i in range(0, 12, 2))

def hash_device_id(device_id: str) -> str:
    """Hash the device ID using HMAC with a secret key."""
    return hmac.new(SECRET_KEY.encode(), device_id.encode(), hashlib.sha256).hexdigest()

def hash_password(password: str) -> str:
    """Hash a password with salt for secure storage."""
    return hashlib.sha256((password + PASSWORD_SALT).encode()).hexdigest()

def is_device_authorized() -> bool:
    """Check if the current device is authorized."""
    device_id = get_device_id()
    hashed_id = hash_device_id(device_id)
    auth_file = "authorized_devices.txt"

    if os.path.exists(auth_file):
        with open(auth_file, "r") as file:
            authorized_ids = file.read().splitlines()
            return device_id in authorized_ids or hashed_id in authorized_ids
    else:
        # Create the file if it doesn't exist
        with open(auth_file, "w") as file:
            pass
        return False

def request_manual_approval(device_id: str) -> bool:
    """Request manual approval for the current device."""
    hashed_id = hash_device_id(device_id)
    print(f"جهاز جديد يحاول تشغيل التطبيق:\nMAC: {device_id}")
    print(f"التجزئة المشفرة لهذا الجهاز:\n{hashed_id}")
    print("أضف هذا الجهاز إلى قائمة الأجهزة المصرح بها في ملف 'authorized_devices.txt'.")
    input("اضغط Enter بعد إضافة الجهاز إلى الملف للتحقق مرة أخرى...")
    return is_device_authorized()

def verify_user(username: str, password: str) -> bool:
    """Verify a user's credentials."""
    # Fixed username and password
    return username == "جو" and password == "جو"

# ==========================
# تطبيق تسجيل الدخول
# ==========================
class LoginApp(ctk.CTk):
    def __init__(self):
        super().__init__()
        self.title("  الدخول تسجيل")  # عكس ترتيب الكلمات
        self.geometry("1300x780")  # تكبير الشاشة
        self.minsize(800, 600)  # زيادة الحد الأدنى للحجم
        # تعيين الثيم الأحمر كثيم افتراضي
        self.configure(fg_color=COLORS["red_bg"])

        if not self.check_device_authorization():
            return

        # واجهة تسجيل الدخول
        # إطار رئيسي
        main_frame = ctk.CTkFrame(self, fg_color="transparent")
        main_frame.pack(expand=True, fill="both", padx=40, pady=40)

        # إطار الشعار والعنوان
        header_frame = ctk.CTkFrame(main_frame, fg_color=COLORS["red_card"], corner_radius=15,
                                   border_width=2, border_color=COLORS["red_border"])
        header_frame.pack(fill="x", pady=(0, 20))

        # العنوان
        self.header = ctk.CTkLabel(
            header_frame, text="الدخول تسجيل  ",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=28, weight="bold"),
            text_color=COLORS["red_accent"], anchor="center"
        )
        self.header.pack(pady=(20, 10))

        # زر تغيير الثيم
        self.theme_button = ctk.CTkButton(
            header_frame, text="(red) الثيم تغيير", command=self.toggle_theme,  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=14), fg_color=COLORS["red_button1"], hover_color=COLORS["red_button2"],
            text_color=COLORS["red_text"], corner_radius=10, width=150, height=35
        )
        self.theme_button.pack(pady=(0, 20))

        # إطار تسجيل الدخول
        login_frame = ctk.CTkFrame(main_frame, fg_color=COLORS["red_card"], corner_radius=15,
                                  border_width=2, border_color=COLORS["red_border"])
        login_frame.pack(fill="both", expand=True, pady=10)

        # عنوان فرعي
        title_frame = ctk.CTkFrame(login_frame, fg_color=COLORS["red_card_alt"], corner_radius=8)
        title_frame.pack(fill="x", padx=10, pady=10)

        ctk.CTkLabel(
            title_frame, text="المستخدم واسم المرور كلمة أدخل",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=COLORS["red_text"], anchor="center"
        ).pack(pady=8)

        # إطار الإدخال
        input_frame = ctk.CTkFrame(login_frame, fg_color=COLORS["red_card_alt"], corner_radius=8)
        input_frame.pack(fill="x", padx=10, pady=10)

        # حقل اسم المستخدم
        username_frame = ctk.CTkFrame(input_frame, fg_color="transparent")
        username_frame.pack(fill="x", padx=15, pady=(10, 5))

        ctk.CTkLabel(
            username_frame, text=":المستخدم اسم",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=COLORS["red_text"], anchor="w"
        ).pack(side="right", padx=(0, 10))

        self.username_entry = ctk.CTkEntry(
            username_frame, placeholder_text="المستخدم اسم أدخل",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=14), fg_color=COLORS["red_bg"],
            text_color=COLORS["red_text"], height=35, width=200,
            border_color=COLORS["red_accent"], border_width=1
        )
        self.username_entry.pack(side="right", padx=10, fill="x", expand=True)

        # حقل كلمة المرور
        password_frame = ctk.CTkFrame(input_frame, fg_color="transparent")
        password_frame.pack(fill="x", padx=15, pady=(10, 10))

        ctk.CTkLabel(
            password_frame, text=":المرور كلمة",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=COLORS["red_text"], anchor="w"
        ).pack(side="right", padx=(0, 10))

        self.password_entry = ctk.CTkEntry(
            password_frame, placeholder_text="المرور كلمة أدخل", show="•",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=14), fg_color=COLORS["red_bg"],
            text_color=COLORS["red_text"], height=35, width=200,
            border_color=COLORS["red_accent"], border_width=1
        )
        self.password_entry.pack(side="right", padx=10, fill="x", expand=True)

        # إطار زر تسجيل الدخول
        button_frame = ctk.CTkFrame(login_frame, fg_color=COLORS["red_card_alt"], corner_radius=8)
        button_frame.pack(fill="x", padx=10, pady=10)

        # زر تسجيل الدخول
        self.login_button = ctk.CTkButton(
            button_frame, text="الدخول تسجيل", command=self.authenticate,  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=16, weight="bold"), fg_color=COLORS["red_button3"],
            hover_color="#FF2E63", corner_radius=10, height=40,
            border_width=1, border_color="#FF2E63"
        )
        self.login_button.pack(padx=20, pady=10, fill="x")

        # إطار حالة تسجيل الدخول
        status_frame = ctk.CTkFrame(login_frame, fg_color=COLORS["red_card_alt"], corner_radius=8)
        status_frame.pack(fill="x", padx=10, pady=10)

        # عنوان الحالة
        ctk.CTkLabel(
            status_frame, text=":الدخول تسجيل حالة",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=COLORS["red_text"], anchor="w"
        ).pack(side="top", anchor="w", padx=10, pady=(5, 0))

        # حالة تسجيل الدخول
        self.status_label = ctk.CTkLabel(
            status_frame, text="الدخول لتسجيل جاهز", text_color=COLORS["red_text"],  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=14), anchor="w"
        )
        self.status_label.pack(padx=10, pady=(0, 5), fill="x")

        # Footer
        self.footer = ctk.CTkLabel(
            main_frame, text="    ويرضى يحب لما دائمًا وفقه، خيرًا الله  جزاه، غنام يوسف / المهندس  بواسطة البرنامج هذا تطوير تم"
,
            font=ctk.CTkFont(size=16, weight="bold"), text_color=COLORS["red_text_muted"], anchor="center"
        )
        self.footer.pack(pady=(10, 5))

        # إطار أزرار التواصل الاجتماعي - تنفيذ جديد
        social_frame = ctk.CTkFrame(main_frame, fg_color=COLORS["red_card_alt"], corner_radius=8, border_width=2, border_color=COLORS["red_border"])
        social_frame.pack(fill="x", padx=10, pady=(5, 10))

        # عنوان التواصل
        ctk.CTkLabel(
            social_frame, text="المطور مع تواصل",  # عكس ترتيب الكلمات
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=COLORS["red_text"], anchor="center"
        ).pack(pady=(10, 5))

        # إطار الأزرار
        buttons_frame = ctk.CTkFrame(social_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", padx=20, pady=(5, 15))

        # زر واتساب
        self.whatsapp_button = ctk.CTkButton(
            buttons_frame, text="واتساب",
            command=lambda: self.open_social_link("https://wa.me/+201062680608"),
            font=ctk.CTkFont(size=16, weight="bold"), fg_color="#25D366", hover_color="#128C7E",
            text_color="#FFFFFF", corner_radius=8, height=40, border_width=1,
            border_color="#128C7E"
        )
        self.whatsapp_button.pack(fill="both", expand=True, pady=5)

        # زر فيسبوك
        self.facebook_button = ctk.CTkButton(
            buttons_frame, text="فيسبوك",
            command=lambda: self.open_social_link("https://www.facebook.com/profile.php?id=61555408544326"),
            font=ctk.CTkFont(size=16, weight="bold"), fg_color="#1877F2", hover_color="#0E5AA7",
            text_color="#FFFFFF", corner_radius=8, height=40, border_width=1,
            border_color="#0E5AA7"
        )
        self.facebook_button.pack(fill="both", expand=True, pady=5)

        # زر انستجرام
        self.instagram_button = ctk.CTkButton(
            buttons_frame, text="انستجرام",
            command=lambda: self.open_social_link("https://www.instagram.com/yousef_.ghanam/"),
            font=ctk.CTkFont(size=16, weight="bold"), fg_color="#E1306C", hover_color="#C13584",
            text_color="#FFFFFF", corner_radius=8, height=40, border_width=1,
            border_color="#C13584"
        )
        self.instagram_button.pack(fill="both", expand=True, pady=5)

        # تطبيق الثيم الأحمر عند بدء التشغيل
        self._apply_theme("red")

    def toggle_theme(self):
        """Toggle between themes: Red, Green."""
        # تحديد الثيم الحالي من خلال لون الخلفية
        current_bg_color = self.cget("fg_color")

        # تحديد الثيم التالي
        if current_bg_color == COLORS["red_bg"]:
            # تغيير من الثيم الأحمر إلى الثيم الأخضر
            self._apply_theme("green")
            self.status_label.configure(text="الأخضر الثيم إلى الثيم تغيير تم.")  # عكس ترتيب الكلمات
        elif current_bg_color == COLORS["green_bg"]:
            # تغيير من الثيم الأخضر إلى الثيم الأحمر
            self._apply_theme("red")
            self.status_label.configure(text="الأحمر الثيم إلى الثيم تغيير تم.")  # عكس ترتيب الكلمات
        else:
            # في حالة استخدام الثيمات القديمة، نبدأ بالثيم الأحمر
            self._apply_theme("red")
            self.status_label.configure(text="الأحمر الثيم إلى الثيم تغيير تم.")  # عكس ترتيب الكلمات

    def _apply_theme(self, theme_name):
        """تطبيق ثيم محدد على التطبيق."""
        # تعيين وضع المظهر المناسب
        if theme_name in ["red", "dark"]:
            ctk.set_appearance_mode("dark")
        else:
            ctk.set_appearance_mode("light")

        # تكوين زر تغيير الثيم
        self.theme_button.configure(
            fg_color=COLORS[f"{theme_name}_button1"],
            text_color="#FFFFFF",  # لون النص أبيض
            hover_color=COLORS[f"{theme_name}_button2"],
            text=f"({theme_name}) تغيير الثيم"  # عكس ترتيب الكلمات
        )

        # تحديث ألوان الواجهة
        self.configure(fg_color=COLORS[f"{theme_name}_bg"])

        # تحديث الإطارات الرئيسية
        for widget in self.winfo_children():
            if isinstance(widget, ctk.CTkFrame):
                widget.configure(fg_color=COLORS[f"{theme_name}_card"])

        # تحديث الإطارات الفرعية
        self._update_all_frames(self, theme_name)

        # تحديث ألوان النصوص
        self._update_all_text(self, theme_name)

        # تحديث ألوان الأزرار
        self._update_all_buttons(self, theme_name)

    def check_device_authorization(self):
        """التحقق من صلاحية الجهاز للوصول إلى التطبيق."""
        # التحقق من وجود الجهاز في قائمة الأجهزة المحظورة
        blocked_file = "blocked_devices.txt"
        device_id = get_device_id()

        if os.path.exists(blocked_file):
            with open(blocked_file, "r") as file:
                blocked_ids = file.read().splitlines()
                if device_id in blocked_ids:
                    # عرض رسالة الحظر
                    CTkMessagebox(
                        title="جهاز محظور",
                        message="هذا الجهاز محظور من استخدام التطبيق.\nيرجى التواصل مع المطور للحصول على المساعدة.",
                        icon="cancel",
                        option_1="إغلاق"
                    )
                    self.after(2000, self.destroy)
                    return False

        # التحقق من وجود الجهاز في قائمة الأجهزة المصرح بها
        if is_device_authorized():
            # تسجيل الجهاز كجهاز نشط
            register_active_device()
            return True
        else:
            # طلب موافقة يدوية
            approval_message = f"جهاز جديد يحاول الوصول إلى التطبيق:\nMAC: {device_id}\nيرجى الموافقة أو الرفض."
            threading.Thread(target=send_telegram_message, args=(approval_message,), daemon=True).start()

            # عرض رسالة الانتظار
            CTkMessagebox(
                title="انتظار الموافقة",
                message="هذا الجهاز غير مصرح له باستخدام التطبيق.\nتم إرسال طلب موافقة إلى المطور.\nيرجى الانتظار للحصول على الموافقة.",
                icon="info",
                option_1="موافق"
            )

            # انتظار الموافقة من التلجرام
            approval = wait_for_telegram_approval()

            if approval is True:
                # تمت الموافقة
                CTkMessagebox(
                    title="تمت الموافقة",
                    message="تمت الموافقة على استخدام التطبيق.\nيمكنك الآن تسجيل الدخول.",
                    icon="check",
                    option_1="موافق"
                )
                return True
            elif approval is False:
                # تم الرفض
                CTkMessagebox(
                    title="تم الرفض",
                    message="تم رفض طلب استخدام التطبيق.\nيرجى التواصل مع المطور للحصول على المساعدة.",
                    icon="cancel",
                    option_1="إغلاق"
                )
                self.after(2000, self.destroy)
                return False
            else:
                # انتهت المهلة
                CTkMessagebox(
                    title="انتهت المهلة",
                    message="انتهت مهلة انتظار الموافقة.\nيرجى المحاولة مرة أخرى لاحقًا.",
                    icon="warning",
                    option_1="إغلاق"
                )
                self.after(2000, self.destroy)
                return False

    def authenticate(self):
        """التحقق من صحة بيانات تسجيل الدخول."""
        username = self.username_entry.get()
        password = self.password_entry.get()

        if not username or not password:
            self.status_label.configure(text="يرجى إدخال اسم المستخدم وكلمة المرور", text_color="#FF5555")
            return

        if verify_user(username, password):
            self.status_label.configure(text="تم تسجيل الدخول بنجاح", text_color="#55FF55")

            # إنشاء وتشغيل التطبيق الرئيسي
            self.withdraw()  # إخفاء نافذة تسجيل الدخول
            app = PDFBuilderApp()
            app.protocol("WM_DELETE_WINDOW", self.on_main_app_close)
            app.mainloop()
        else:
            self.status_label.configure(text="اسم المستخدم أو كلمة المرور غير صحيحة", text_color="#FF5555")

    def on_main_app_close(self):
        """وظيفة تنفذ عند إغلاق التطبيق الرئيسي."""
        self.destroy()  # إغلاق نافذة تسجيل الدخول أيضًا

    def open_social_link(self, url):
        """فتح رابط التواصل الاجتماعي."""
        webbrowser.open(url)

        # الحفاظ على ألوان أزرار التواصل الاجتماعي
        if hasattr(self, 'whatsapp_button'):
            self.whatsapp_button.configure(
                fg_color="#25D366", hover_color="#128C7E", text_color="#FFFFFF"
            )

        if hasattr(self, 'facebook_button'):
            self.facebook_button.configure(
                fg_color="#1877F2", hover_color="#0E5AA7", text_color="#FFFFFF"
            )

        if hasattr(self, 'instagram_button'):
            self.instagram_button.configure(
                fg_color="#E1306C", hover_color="#C13584", text_color="#FFFFFF"
            )

    def _update_all_frames(self, parent, theme_name):
        """تحديث جميع الإطارات بألوان الثيم الجديد."""
        for widget in parent.winfo_children():
            if isinstance(widget, ctk.CTkFrame):
                # تحديث الإطارات الفرعية
                if "alt" in str(widget.cget("fg_color")):
                    widget.configure(fg_color=COLORS[f"{theme_name}_card_alt"])
                elif widget.cget("fg_color") != "transparent":
                    widget.configure(fg_color=COLORS[f"{theme_name}_card"])

                # تحديث لون الحدود إذا كان الإطار يحتوي على حدود
                if widget.cget("border_width") > 0:
                    widget.configure(border_color=COLORS[f"{theme_name}_border"])

                # استدعاء متكرر للإطارات الفرعية
                self._update_all_frames(widget, theme_name)

    def _update_all_text(self, parent, theme_name):
        """تحديث جميع النصوص بألوان الثيم الجديد."""
        for widget in parent.winfo_children():
            if isinstance(widget, ctk.CTkLabel) and widget != self.header:
                # تجاهل النصوص في footer_frame
                if hasattr(self, 'footer_frame') and widget in self.footer_frame.winfo_children():
                    continue

                # تجاهل النصوص ذات اللون الأحمر المميز
                if widget.cget("text_color") == "#FF5555":
                    continue

                # تحديد ما إذا كان النص عادي أو مخفف
                if "muted" in str(widget.cget("text_color")):
                    widget.configure(text_color=COLORS[f"{theme_name}_text_muted"])
                else:
                    widget.configure(text_color=COLORS[f"{theme_name}_text"])

            # استدعاء متكرر للعناصر الفرعية
            if hasattr(widget, "winfo_children"):
                self._update_all_text(widget, theme_name)

    def _get_hover_color(self, color_hex):
        """حساب لون التحويم بناءً على لون الزر"""
        # تحويل اللون من سداسي عشري إلى RGB
        r = int(color_hex[1:3], 16)
        g = int(color_hex[3:5], 16)
        b = int(color_hex[5:7], 16)

        # تغميق اللون بنسبة 20%
        r = max(0, r - 40)
        g = max(0, g - 40)
        b = max(0, b - 40)

        # تحويل اللون مرة أخرى إلى سداسي عشري
        return f"#{r:02x}{g:02x}{b:02x}"

    def _update_all_buttons(self, parent, theme_name):
        """تحديث جميع الأزرار بألوان الثيم الجديد."""
        for widget in parent.winfo_children():
            if isinstance(widget, ctk.CTkButton) and widget != self.theme_button:
                # تحديد نوع الزر (1, 2, 3)
                if "button3" in str(widget.cget("fg_color")):
                    widget.configure(
                        fg_color=COLORS[f"{theme_name}_button3"],
                        text_color="#FFFFFF",
                        hover_color=self._get_hover_color(COLORS[f"{theme_name}_button3"]),
                        border_color=COLORS[f"{theme_name}_button3"]
                    )
                elif "button2" in str(widget.cget("fg_color")):
                    widget.configure(
                        fg_color=COLORS[f"{theme_name}_button2"],
                        text_color="#FFFFFF",  # لون النص أبيض
                        hover_color=COLORS[f"{theme_name}_button1"],
                        border_color=COLORS[f"{theme_name}_button1"]
                    )
                else:
                    widget.configure(
                        fg_color=COLORS[f"{theme_name}_button1"],
                        text_color="#FFFFFF",  # لون النص أبيض
                        hover_color=COLORS[f"{theme_name}_button2"],
                        border_color=COLORS[f"{theme_name}_button2"]
                    )

                # تحديث زر اختبار الاتصال ببوت التلجرام
                if hasattr(self, 'telegram_test_button') and widget == self.telegram_test_button:
                    widget.configure(
                        fg_color=COLORS[f"{theme_name}_button1"],
                        text_color="#FFFFFF",  # لون النص أبيض
                        hover_color=COLORS[f"{theme_name}_button2"]
                    )

            # استدعاء متكرر للعناصر الفرعية
            if hasattr(widget, "winfo_children"):
                self._update_all_buttons(widget, theme_name)
def is_device_authorized():
    """Check if the current device is authorized."""
    device_id = get_device_id()
    hashed_id = hash_device_id(device_id)
    auth_file = "authorized_devices.txt"

    if os.path.exists(auth_file):
        with open(auth_file, "r") as file:
            authorized_ids = file.read().splitlines()
            return device_id in authorized_ids or hashed_id in authorized_ids
    else:
        # Create the file if it doesn't exist
        with open(auth_file, "w") as file:
            pass
        return False

def request_manual_approval() -> bool:
    """Request manual approval for the current device."""
    # هنا يمكن إضافة كود لطلب الموافقة اليدوية
    return False

# ==========================
# الدالة الرئيسية
# ==========================
if __name__ == "__main__":
    # تهيئة المظهر العام
    ctk.set_appearance_mode("dark")
    ctk.set_default_color_theme("blue")

    # تشغيل واجهة تسجيل الدخول
    login_app = LoginApp()
    login_app.mainloop()








































































