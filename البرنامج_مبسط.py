# البرنامج المبسط - إزالة الأجزاء غير الضرورية
import os
import tempfile
from io import BytesIO

# لدعم النصوص العربية
try:
    import arabic_reshaper
    from bidi.algorithm import get_display
    ARABIC_SUPPORT = True
except ImportError:
    ARABIC_SUPPORT = False

# File dialog imports
from tkinter import filedialog

# PDF generation imports
from reportlab.lib.utils import ImageReader
from reportlab.lib.pagesizes import letter

# UI imports
import customtkinter as ctk
from PIL import Image, ImageDraw, ImageFont

# استيراد وحدة الخطوط
try:
    from font_utils import get_font_display_names, get_font_path_by_name
except ImportError:
    def get_font_display_names():
        return ["Arial", "Times New Roman", "Calibri", "Tahoma"]
    def get_font_path_by_name(font_name):
        return ["arial.ttf", "C:\\Windows\\Fonts\\arial.ttf"]

# ==========================
# إعدادات الثيم واللون
# ==========================
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("dark-blue")

# دالة مساعدة لرسم النصوص العربية
def draw_arabic_text(draw, text, position, font, fill="black", anchor="la", direction="rtl"):
    """رسم نص عربي بشكل صحيح على صورة"""
    if not text:
        return

    # تحديد ما إذا كان النص يحتوي على حروف عربية
    has_arabic = any(ord(char) > 127 for char in text)

    if ARABIC_SUPPORT and has_arabic and direction == "rtl":
        try:
            # إعادة تشكيل النص العربي
            reshaped_text = arabic_reshaper.reshape(text)
            # ضبط اتجاه النص من اليمين إلى اليسار
            bidi_text = get_display(reshaped_text)
            # رسم النص
            draw.text(position, bidi_text, font=font, fill=fill, anchor=anchor)
        except Exception as e:
            print(f"خطأ في رسم النص العربي: {e}")
            # في حالة حدوث خطأ، استخدم الطريقة العادية
            draw.text(position, text, font=font, fill=fill, anchor=anchor)
    else:
        # إذا لم تكن مكتبات دعم العربية متاحة أو النص ليس عربيًا، استخدم الطريقة العادية
        draw.text(position, text, font=font, fill=fill, anchor=anchor)

# إعدادات الألوان
COLORS = {
    # الثيم الأحمر
    "red_bg": "#0F0A1A",
    "red_card": "#622872",
    "red_card_alt": "#4A1D59",
    "red_accent": "#caa5cb",
    "red_text": "#e8ddea",
    "red_text_muted": "#a58fa8",
    "red_button1": "#9C27B0",
    "red_button2": "#BA68C8",
    "red_button3": "#F50057",
    "red_border": "#caa5cb",

    # الثيم الأخضر
    "green_bg": "#f0f7e9",
    "green_card": "#e0ecd5",
    "green_card_alt": "#c5e0b4",
    "green_accent": "#4caf50",
    "green_text": "#2e7d32",
    "green_text_muted": "#558b2f",
    "green_button1": "#4caf50",
    "green_button2": "#388e3c",
    "green_button3": "#1b5e20",
    "green_border": "#81c784",

    # الوضع الداكن
    "dark_bg": "#0A1931",
    "dark_card": "#185ADB",
    "dark_card_alt": "#0F52BA",
    "dark_accent": "#FFC947",
    "dark_text": "#FFFFFF",
    "dark_text_muted": "#DDDDDD",
    "dark_button1": "#1E56A0",
    "dark_button2": "#4F80E1",
    "dark_button3": "#D72323",
    "dark_border": "#FFC947",

    # الوضع الفاتح
    "light_bg": "#F6F8FA",
    "light_card": "#FFFFFF",
    "light_card_alt": "#F0F0F0",
    "light_accent": "#1E56A0",
    "light_text": "#333333",
    "light_text_muted": "#666666",
    "light_button1": "#4F80E1",
    "light_button2": "#1E56A0",
    "light_button3": "#D72323",
    "light_border": "#1E56A0"
}

# دالة تبديل الثيم
def toggle_theme():
    current_theme = ctk.get_appearance_mode()
    if current_theme == "dark":
        ctk.set_appearance_mode("light")
    else:
        ctk.set_appearance_mode("dark")

# ==========================
# نظام تسجيل الدخول المبسط
# ==========================
def verify_user(username: str, password: str) -> bool:
    """التحقق من بيانات المستخدم"""
    return username == "جو" and password == "جو"

class LoginApp(ctk.CTk):
    """نافذة تسجيل الدخول المبسطة"""
    def __init__(self):
        super().__init__()
        
        self.title("تسجيل الدخول - برنامج الامتحانات")
        self.geometry("400x300")
        self.resizable(False, False)
        
        # تطبيق الثيم
        self.configure(fg_color=COLORS["dark_bg"])
        
        self.create_widgets()
        
    def create_widgets(self):
        # العنوان
        title_label = ctk.CTkLabel(
            self, text="تسجيل الدخول",
            font=ctk.CTkFont(size=24, weight="bold"),
            text_color=COLORS["dark_text"]
        )
        title_label.pack(pady=30)
        
        # إطار تسجيل الدخول
        login_frame = ctk.CTkFrame(self, fg_color=COLORS["dark_card"], corner_radius=15)
        login_frame.pack(pady=20, padx=40, fill="both", expand=True)
        
        # اسم المستخدم
        ctk.CTkLabel(
            login_frame, text="اسم المستخدم:",
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"]
        ).pack(pady=(20, 5))
        
        self.username_entry = ctk.CTkEntry(
            login_frame, width=200, justify="center",
            font=ctk.CTkFont(size=14)
        )
        self.username_entry.pack(pady=5)
        self.username_entry.insert(0, "جو")
        
        # كلمة المرور
        ctk.CTkLabel(
            login_frame, text="كلمة المرور:",
            font=ctk.CTkFont(size=14), text_color=COLORS["dark_text"]
        ).pack(pady=(10, 5))
        
        self.password_entry = ctk.CTkEntry(
            login_frame, width=200, justify="center",
            font=ctk.CTkFont(size=14), show="*"
        )
        self.password_entry.pack(pady=5)
        self.password_entry.insert(0, "جو")
        
        # زر تسجيل الدخول
        login_button = ctk.CTkButton(
            login_frame, text="تسجيل الدخول",
            command=self.authenticate,
            font=ctk.CTkFont(size=16, weight="bold"),
            fg_color=COLORS["dark_button1"],
            hover_color=COLORS["dark_button2"],
            width=150, height=40
        )
        login_button.pack(pady=20)
        
        # رسالة الحالة
        self.status_label = ctk.CTkLabel(
            login_frame, text="",
            font=ctk.CTkFont(size=12)
        )
        self.status_label.pack(pady=5)
        
    def authenticate(self):
        username = self.username_entry.get()
        password = self.password_entry.get()
        
        if verify_user(username, password):
            self.status_label.configure(text="تم تسجيل الدخول بنجاح", text_color="#55FF55")
            
            # إخفاء نافذة تسجيل الدخول
            self.withdraw()
            
            # إنشاء التطبيق الرئيسي
            app = PDFBuilderApp()
            
            # تعريف دالة إغلاق مخصصة
            def on_app_close():
                app.destroy()
                self.on_main_app_close()
            
            app.protocol("WM_DELETE_WINDOW", on_app_close)
            app.mainloop()
        else:
            self.status_label.configure(text="اسم المستخدم أو كلمة المرور غير صحيحة", text_color="#FF5555")
    
    def on_main_app_close(self):
        """وظيفة تنفذ عند إغلاق التطبيق الرئيسي"""
        try:
            if self.winfo_exists():
                self.destroy()
        except Exception:
            pass
        try:
            self.quit()
        except Exception:
            pass

# ==========================
# التطبيق الرئيسي المبسط
# ==========================
