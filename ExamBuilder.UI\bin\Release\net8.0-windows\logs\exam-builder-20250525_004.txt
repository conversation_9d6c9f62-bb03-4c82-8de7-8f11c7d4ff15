2025-05-25 13:33:22.906 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-05-25 13:33:22.941 +03:00 [INF] Hosting environment: Production
2025-05-25 13:33:22.942 +03:00 [INF] Content root path: D:\mr\qqqq\ExamBuilder.UI\bin\Release\net8.0-windows
2025-05-25 13:33:22.943 +03:00 [INF] تم بدء تشغيل التطبيق بنجاح
2025-05-25 13:33:23.181 +03:00 [INF] تم تحميل نافذة تسجيل الدخول
2025-05-25 13:33:35.992 +03:00 [INF] محاولة تسجيل دخول - المستخدم: 'جو', كلمة المرور: 'جو'
2025-05-25 13:33:35.992 +03:00 [INF] تحقق المستخدم: True, تحقق كلمة المرور: True
2025-05-25 13:33:35.992 +03:00 [INF] تم التحقق من البيانات بنجاح
2025-05-25 13:33:39.402 +03:00 [INF] محاولة فتح النافذة الرئيسية
2025-05-25 13:33:39.411 +03:00 [ERR] خطأ في فتح النافذة الرئيسية
System.Windows.Markup.XamlParseException: Provide value on 'System.Windows.Baml2006.TypeConverterMarkupExtension' threw an exception.
 ---> System.FormatException: Settings is not a valid value for PackIconMaterialKind.
 ---> System.ArgumentException: Requested value 'Settings' was not found.
   at System.Enum.TryParseByName[TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TStorage& result)
   at System.Enum.TryParseByValueOrName[TUnderlying,TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TUnderlying& result)
   at System.Enum.TryParse(Type enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, Object& result)
   at System.Enum.Parse(Type enumType, String value, Boolean ignoreCase)
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   --- End of inner exception stack trace ---
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   at MS.Internal.Xaml.Runtime.ClrObjectRuntime.CallProvideValue(MarkupExtension me, IServiceProvider serviceProvider)
   --- End of inner exception stack trace ---
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.LoadBaml(XamlReader xamlReader, Boolean skipJournaledProperties, Object rootObject, XamlAccessLevel accessLevel, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at ExamBuilder.UI.Views.MainWindow.InitializeComponent() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml:line 1
   at ExamBuilder.UI.Views.MainWindow..ctor() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml.cs:line 18
   at ExamBuilder.UI.Views.LoginWindow.PerformLogin() in D:\mr\qqqq\ExamBuilder.UI\Views\LoginWindow.xaml.cs:line 102
2025-05-25 13:33:39.420 +03:00 [INF] تم تسجيل دخول المستخدم: جو
2025-05-25 13:33:52.781 +03:00 [INF] محاولة تسجيل دخول - المستخدم: 'جو', كلمة المرور: 'جو'
2025-05-25 13:33:52.781 +03:00 [INF] تحقق المستخدم: True, تحقق كلمة المرور: True
2025-05-25 13:33:52.781 +03:00 [INF] تم التحقق من البيانات بنجاح
2025-05-25 13:33:56.933 +03:00 [INF] محاولة فتح النافذة الرئيسية
2025-05-25 13:33:56.938 +03:00 [ERR] خطأ في فتح النافذة الرئيسية
System.Windows.Markup.XamlParseException: Provide value on 'System.Windows.Baml2006.TypeConverterMarkupExtension' threw an exception.
 ---> System.FormatException: Settings is not a valid value for PackIconMaterialKind.
 ---> System.ArgumentException: Requested value 'Settings' was not found.
   at System.Enum.TryParseByName[TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TStorage& result)
   at System.Enum.TryParseByValueOrName[TUnderlying,TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TUnderlying& result)
   at System.Enum.TryParse(Type enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, Object& result)
   at System.Enum.Parse(Type enumType, String value, Boolean ignoreCase)
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   --- End of inner exception stack trace ---
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   at MS.Internal.Xaml.Runtime.ClrObjectRuntime.CallProvideValue(MarkupExtension me, IServiceProvider serviceProvider)
   --- End of inner exception stack trace ---
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.LoadBaml(XamlReader xamlReader, Boolean skipJournaledProperties, Object rootObject, XamlAccessLevel accessLevel, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at ExamBuilder.UI.Views.MainWindow.InitializeComponent() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml:line 1
   at ExamBuilder.UI.Views.MainWindow..ctor() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml.cs:line 18
   at ExamBuilder.UI.Views.LoginWindow.PerformLogin() in D:\mr\qqqq\ExamBuilder.UI\Views\LoginWindow.xaml.cs:line 102
2025-05-25 13:33:56.939 +03:00 [INF] تم تسجيل دخول المستخدم: جو
2025-05-25 13:33:59.283 +03:00 [INF] تم تطبيق الثيم الأخضر
2025-05-25 13:33:59.873 +03:00 [INF] تم تطبيق الثيم الأخضر
2025-05-25 13:34:00.101 +03:00 [INF] تم تطبيق الثيم الأخضر
2025-05-25 13:34:00.374 +03:00 [INF] تم تطبيق الثيم الأخضر
2025-05-25 13:34:00.604 +03:00 [INF] تم تطبيق الثيم الأخضر
2025-05-25 13:34:01.324 +03:00 [INF] تم تطبيق الثيم الداكن
2025-05-25 13:34:01.504 +03:00 [INF] تم تطبيق الثيم الداكن
2025-05-25 13:34:01.685 +03:00 [INF] تم تطبيق الثيم الداكن
2025-05-25 13:34:01.858 +03:00 [INF] تم تطبيق الثيم الداكن
2025-05-25 13:34:02.054 +03:00 [INF] تم تطبيق الثيم الداكن
2025-05-25 13:34:02.236 +03:00 [INF] تم تطبيق الثيم الداكن
2025-05-25 13:34:02.425 +03:00 [INF] تم تطبيق الثيم الداكن
2025-05-25 13:34:02.645 +03:00 [INF] تم تطبيق الثيم الداكن
2025-05-25 13:34:03.309 +03:00 [INF] تم تطبيق الثيم الداكن
2025-05-25 13:34:03.469 +03:00 [INF] تم تطبيق الثيم الداكن
2025-05-25 13:34:05.574 +03:00 [INF] محاولة تسجيل دخول - المستخدم: 'جو', كلمة المرور: 'جو'
2025-05-25 13:34:05.575 +03:00 [INF] تحقق المستخدم: True, تحقق كلمة المرور: True
2025-05-25 13:34:05.575 +03:00 [INF] تم التحقق من البيانات بنجاح
2025-05-25 13:34:08.349 +03:00 [INF] تم رفض طلب الدخول من قبل المستخدم
2025-05-25 13:34:13.128 +03:00 [INF] محاولة تسجيل دخول - المستخدم: 'جو', كلمة المرور: 'جو'
2025-05-25 13:34:13.128 +03:00 [INF] تحقق المستخدم: True, تحقق كلمة المرور: True
2025-05-25 13:34:13.128 +03:00 [INF] تم التحقق من البيانات بنجاح
2025-05-25 13:34:20.768 +03:00 [INF] محاولة فتح النافذة الرئيسية
2025-05-25 13:34:20.772 +03:00 [ERR] خطأ في فتح النافذة الرئيسية
System.Windows.Markup.XamlParseException: Provide value on 'System.Windows.Baml2006.TypeConverterMarkupExtension' threw an exception.
 ---> System.FormatException: Settings is not a valid value for PackIconMaterialKind.
 ---> System.ArgumentException: Requested value 'Settings' was not found.
   at System.Enum.TryParseByName[TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TStorage& result)
   at System.Enum.TryParseByValueOrName[TUnderlying,TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TUnderlying& result)
   at System.Enum.TryParse(Type enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, Object& result)
   at System.Enum.Parse(Type enumType, String value, Boolean ignoreCase)
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   --- End of inner exception stack trace ---
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   at MS.Internal.Xaml.Runtime.ClrObjectRuntime.CallProvideValue(MarkupExtension me, IServiceProvider serviceProvider)
   --- End of inner exception stack trace ---
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.LoadBaml(XamlReader xamlReader, Boolean skipJournaledProperties, Object rootObject, XamlAccessLevel accessLevel, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at ExamBuilder.UI.Views.MainWindow.InitializeComponent() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml:line 1
   at ExamBuilder.UI.Views.MainWindow..ctor() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml.cs:line 18
   at ExamBuilder.UI.Views.LoginWindow.PerformLogin() in D:\mr\qqqq\ExamBuilder.UI\Views\LoginWindow.xaml.cs:line 102
2025-05-25 13:34:20.773 +03:00 [INF] تم تسجيل دخول المستخدم: جو
2025-05-25 13:34:39.343 +03:00 [INF] محاولة تسجيل دخول - المستخدم: 'Jo', كلمة المرور: 'Jo'
2025-05-25 13:34:39.343 +03:00 [INF] تحقق المستخدم: True, تحقق كلمة المرور: True
2025-05-25 13:34:39.343 +03:00 [INF] تم التحقق من البيانات بنجاح
2025-05-25 13:34:43.224 +03:00 [INF] محاولة فتح النافذة الرئيسية
2025-05-25 13:34:43.228 +03:00 [ERR] خطأ في فتح النافذة الرئيسية
System.Windows.Markup.XamlParseException: Provide value on 'System.Windows.Baml2006.TypeConverterMarkupExtension' threw an exception.
 ---> System.FormatException: Settings is not a valid value for PackIconMaterialKind.
 ---> System.ArgumentException: Requested value 'Settings' was not found.
   at System.Enum.TryParseByName[TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TStorage& result)
   at System.Enum.TryParseByValueOrName[TUnderlying,TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TUnderlying& result)
   at System.Enum.TryParse(Type enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, Object& result)
   at System.Enum.Parse(Type enumType, String value, Boolean ignoreCase)
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   --- End of inner exception stack trace ---
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   at MS.Internal.Xaml.Runtime.ClrObjectRuntime.CallProvideValue(MarkupExtension me, IServiceProvider serviceProvider)
   --- End of inner exception stack trace ---
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.LoadBaml(XamlReader xamlReader, Boolean skipJournaledProperties, Object rootObject, XamlAccessLevel accessLevel, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at ExamBuilder.UI.Views.MainWindow.InitializeComponent() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml:line 1
   at ExamBuilder.UI.Views.MainWindow..ctor() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml.cs:line 18
   at ExamBuilder.UI.Views.LoginWindow.PerformLogin() in D:\mr\qqqq\ExamBuilder.UI\Views\LoginWindow.xaml.cs:line 102
2025-05-25 13:34:43.229 +03:00 [INF] تم تسجيل دخول المستخدم: Jo
2025-05-25 13:34:51.954 +03:00 [INF] محاولة تسجيل دخول - المستخدم: 'l', كلمة المرور: 'Jo'
2025-05-25 13:34:51.954 +03:00 [INF] تحقق المستخدم: False, تحقق كلمة المرور: True
2025-05-25 13:34:51.954 +03:00 [WRN] فشل في تسجيل الدخول للمستخدم: 'l' مع كلمة المرور: 'Jo'
2025-05-25 13:34:55.648 +03:00 [INF] محاولة تسجيل دخول - المستخدم: 'l', كلمة المرور: 'Jo'
2025-05-25 13:34:55.648 +03:00 [INF] تحقق المستخدم: False, تحقق كلمة المرور: True
2025-05-25 13:34:55.648 +03:00 [WRN] فشل في تسجيل الدخول للمستخدم: 'l' مع كلمة المرور: 'Jo'
