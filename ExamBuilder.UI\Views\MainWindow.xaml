<mah:MetroWindow x:Class="ExamBuilder.UI.Views.MainWindow"
                 xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                 xmlns:mah="http://metro.mahapps.com/winfx/xaml/controls"
                 xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
                 xmlns:iconPacks="http://metro.mahapps.com/winfx/xaml/iconpacks"
                 Title="برنامج إنشاء الامتحانات - المهندس يوسف غنام"
                 Height="900" Width="1400"
                 WindowStartupLocation="CenterScreen"
                 FlowDirection="RightToLeft"
                 Background="{StaticResource BackgroundBrush}"
                 MinHeight="600" MinWidth="1000">

    <mah:MetroWindow.Resources>
        <!-- Animation Storyboards -->
        <Storyboard x:Key="FadeInAnimation">
            <DoubleAnimation Storyboard.TargetProperty="Opacity" From="0" To="1" Duration="0:0:0.5"/>
        </Storyboard>
    </mah:MetroWindow.Resources>

    <mah:MetroWindow.Triggers>
        <EventTrigger RoutedEvent="Loaded">
            <BeginStoryboard Storyboard="{StaticResource FadeInAnimation}"/>
        </EventTrigger>
    </mah:MetroWindow.Triggers>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header Section -->
        <Border Grid.Row="0" Background="{StaticResource PrimaryBrush}" Padding="20,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Title and User Info -->
                <StackPanel Grid.Column="0" Orientation="Vertical">
                    <TextBlock Text="برنامج إنشاء الامتحانات"
                              Style="{StaticResource HeaderTextStyle}"
                              FontSize="24"
                              Foreground="White"
                              HorizontalAlignment="Right"/>
                    <TextBlock Text="{Binding CurrentUser, StringFormat='مرحباً {0}'}"
                              Style="{StaticResource BodyTextStyle}"
                              FontSize="14"
                              Foreground="{StaticResource InfoBrush}"
                              HorizontalAlignment="Right"/>
                </StackPanel>

                <!-- Theme Buttons -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="20,0">
                    <Button x:Name="RedThemeButton"
                           Content="أحمر"
                           Width="60" Height="30"
                           FontSize="12"
                           Background="{StaticResource PrimaryBrush}"
                           Foreground="White"
                           BorderThickness="0"
                           Margin="5,0"
                           Click="RedThemeButton_Click"/>

                    <Button x:Name="GreenThemeButton"
                           Content="أخضر"
                           Width="60" Height="30"
                           FontSize="12"
                           Background="#2E8B57"
                           Foreground="White"
                           BorderThickness="0"
                           Margin="5,0"
                           Click="GreenThemeButton_Click"/>

                    <Button x:Name="DarkThemeButton"
                           Content="داكن"
                           Width="60" Height="30"
                           FontSize="12"
                           Background="#2C2C2C"
                           Foreground="White"
                           BorderThickness="0"
                           Margin="5,0"
                           Click="DarkThemeButton_Click"/>
                </StackPanel>

                <!-- Action Buttons -->
                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <Button Command="{Binding SettingsCommand}"
                           Style="{StaticResource SecondaryButtonStyle}"
                           Width="40" Height="40"
                           Margin="5,0"
                           ToolTip="الإعدادات">
                        <iconPacks:PackIconMaterial Kind="Settings" Width="20" Height="20"/>
                    </Button>

                    <Button Command="{Binding AboutCommand}"
                           Style="{StaticResource SecondaryButtonStyle}"
                           Width="40" Height="40"
                           Margin="5,0"
                           ToolTip="حول البرنامج">
                        <iconPacks:PackIconMaterial Kind="Information" Width="20" Height="20"/>
                    </Button>

                    <Button Command="{Binding LogoutCommand}"
                           Style="{StaticResource SecondaryButtonStyle}"
                           Width="40" Height="40"
                           Margin="5,0"
                           ToolTip="تسجيل الخروج">
                        <iconPacks:PackIconMaterial Kind="Logout" Width="20" Height="20"/>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="300"/>
            </Grid.ColumnDefinitions>

            <!-- Left Panel - Exams List -->
            <Border Grid.Column="0" Style="{StaticResource CardStyle}" Margin="0,0,10,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Exams Header -->
                    <TextBlock Grid.Row="0"
                              Text="الامتحانات"
                              Style="{StaticResource SubHeaderTextStyle}"
                              Margin="0,0,0,15"/>

                    <!-- Exam Actions -->
                    <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,15">
                        <Button Command="{Binding CreateExamCommand}"
                               Style="{StaticResource PrimaryButtonStyle}"
                               Content="جديد"
                               Width="60" Height="35"
                               FontSize="12"
                               Margin="0,0,5,0"/>

                        <Button Command="{Binding RefreshCommand}"
                               Style="{StaticResource SecondaryButtonStyle}"
                               Width="35" Height="35"
                               Margin="5,0,0,0"
                               ToolTip="تحديث">
                            <iconPacks:PackIconMaterial Kind="Refresh" Width="16" Height="16"/>
                        </Button>
                    </StackPanel>

                    <!-- Exams List -->
                    <ListBox Grid.Row="2"
                            ItemsSource="{Binding Exams}"
                            SelectedItem="{Binding SelectedExam}"
                            Background="Transparent"
                            BorderThickness="0"
                            ScrollViewer.HorizontalScrollBarVisibility="Disabled">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <Border Background="{StaticResource SurfaceBrush}"
                                       CornerRadius="8"
                                       Padding="12"
                                       Margin="0,2">
                                    <StackPanel>
                                        <TextBlock Text="{Binding Title}"
                                                  Style="{StaticResource BodyTextStyle}"
                                                  FontWeight="Bold"
                                                  TextTrimming="CharacterEllipsis"/>
                                        <TextBlock Text="{Binding Subject}"
                                                  Style="{StaticResource BodyTextStyle}"
                                                  FontSize="12"
                                                  Opacity="0.8"
                                                  Margin="0,2,0,0"/>
                                        <TextBlock Text="{Binding CreatedAt, StringFormat='dd/MM/yyyy'}"
                                                  Style="{StaticResource BodyTextStyle}"
                                                  FontSize="10"
                                                  Opacity="0.6"
                                                  Margin="0,2,0,0"/>
                                    </StackPanel>
                                </Border>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>
                </Grid>
            </Border>

            <!-- Center Panel - Main Content -->
            <Border Grid.Column="1" Style="{StaticResource CardStyle}" Margin="10,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Exam Details Header -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                        <TextBlock Text="{Binding SelectedExam.Title, FallbackValue='اختر امتحاناً'}"
                                  Style="{StaticResource SubHeaderTextStyle}"
                                  VerticalAlignment="Center"/>

                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Left" Margin="20,0,0,0">
                            <Button Command="{Binding EditExamCommand}"
                                   Style="{StaticResource PrimaryButtonStyle}"
                                   Content="تحرير"
                                   Width="60" Height="30"
                                   FontSize="12"
                                   Margin="0,0,5,0"/>

                            <Button Command="{Binding DuplicateExamCommand}"
                                   Style="{StaticResource SecondaryButtonStyle}"
                                   Content="نسخ"
                                   Width="50" Height="30"
                                   FontSize="12"
                                   Margin="5,0"/>

                            <Button Command="{Binding ExportExamCommand}"
                                   Style="{StaticResource PrimaryButtonStyle}"
                                   Content="تصدير"
                                   Width="60" Height="30"
                                   FontSize="12"
                                   Margin="5,0"/>

                            <Button Command="{Binding DeleteExamCommand}"
                                   Background="{StaticResource ErrorBrush}"
                                   Foreground="White"
                                   Content="حذف"
                                   Width="50" Height="30"
                                   FontSize="12"
                                   Margin="5,0,0,0"/>
                        </StackPanel>
                    </StackPanel>

                    <!-- Questions Header -->
                    <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,15">
                        <TextBlock Text="الأسئلة"
                                  Style="{StaticResource BodyTextStyle}"
                                  FontWeight="Bold"
                                  VerticalAlignment="Center"/>

                        <Button Command="{Binding AddQuestionCommand}"
                               Style="{StaticResource PrimaryButtonStyle}"
                               Content="إضافة سؤال"
                               Width="80" Height="30"
                               FontSize="12"
                               Margin="20,0,0,0"/>
                    </StackPanel>

                    <!-- Questions List -->
                    <ListBox Grid.Row="2"
                            ItemsSource="{Binding Questions}"
                            SelectedItem="{Binding SelectedQuestion}"
                            Background="Transparent"
                            BorderThickness="0">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <Border Background="{StaticResource SurfaceBrush}"
                                       CornerRadius="8"
                                       Padding="15"
                                       Margin="0,3">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <TextBlock Grid.Column="0"
                                                  Text="{Binding Order}"
                                                  Style="{StaticResource BodyTextStyle}"
                                                  FontWeight="Bold"
                                                  VerticalAlignment="Center"
                                                  Margin="0,0,10,0"/>

                                        <StackPanel Grid.Column="1">
                                            <TextBlock Text="{Binding Text}"
                                                      Style="{StaticResource BodyTextStyle}"
                                                      TextTrimming="CharacterEllipsis"
                                                      MaxHeight="40"/>
                                            <TextBlock Text="{Binding Type}"
                                                      Style="{StaticResource BodyTextStyle}"
                                                      FontSize="11"
                                                      Opacity="0.7"
                                                      Margin="0,2,0,0"/>
                                        </StackPanel>

                                        <TextBlock Grid.Column="2"
                                                  Text="{Binding Points, StringFormat='{}{0} نقطة'}"
                                                  Style="{StaticResource BodyTextStyle}"
                                                  FontSize="12"
                                                  VerticalAlignment="Center"
                                                  Margin="10,0"/>

                                        <StackPanel Grid.Column="3" Orientation="Horizontal">
                                            <Button Command="{Binding DataContext.EditQuestionCommand, RelativeSource={RelativeSource AncestorType=ListBox}}"
                                                   Style="{StaticResource SecondaryButtonStyle}"
                                                   Width="25" Height="25"
                                                   Margin="2"
                                                   ToolTip="تحرير">
                                                <iconPacks:PackIconMaterial Kind="Edit" Width="12" Height="12"/>
                                            </Button>

                                            <Button Command="{Binding DataContext.DeleteQuestionCommand, RelativeSource={RelativeSource AncestorType=ListBox}}"
                                                   Background="{StaticResource ErrorBrush}"
                                                   Foreground="White"
                                                   Width="25" Height="25"
                                                   Margin="2"
                                                   ToolTip="حذف">
                                                <iconPacks:PackIconMaterial Kind="Delete" Width="12" Height="12"/>
                                            </Button>
                                        </StackPanel>
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>
                </Grid>
            </Border>

            <!-- Right Panel - Recent Exams & Statistics -->
            <Border Grid.Column="2" Style="{StaticResource CardStyle}" Margin="10,0,0,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Recent Exams -->
                    <TextBlock Grid.Row="0"
                              Text="الامتحانات الحديثة"
                              Style="{StaticResource SubHeaderTextStyle}"
                              Margin="0,0,0,15"/>

                    <ListBox Grid.Row="1"
                            ItemsSource="{Binding RecentExams}"
                            Background="Transparent"
                            BorderThickness="0"
                            Margin="0,0,0,20">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <Border Background="{StaticResource SurfaceBrush}"
                                       CornerRadius="6"
                                       Padding="10"
                                       Margin="0,2">
                                    <StackPanel>
                                        <TextBlock Text="{Binding Title}"
                                                  Style="{StaticResource BodyTextStyle}"
                                                  FontSize="12"
                                                  FontWeight="Bold"
                                                  TextTrimming="CharacterEllipsis"/>
                                        <TextBlock Text="{Binding CreatedAt, StringFormat='dd/MM/yyyy'}"
                                                  Style="{StaticResource BodyTextStyle}"
                                                  FontSize="10"
                                                  Opacity="0.6"
                                                  Margin="0,2,0,0"/>
                                    </StackPanel>
                                </Border>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>

                    <!-- Statistics -->
                    <TextBlock Grid.Row="2"
                              Text="الإحصائيات"
                              Style="{StaticResource SubHeaderTextStyle}"
                              Margin="0,0,0,15"/>

                    <StackPanel Grid.Row="3">
                        <Border Background="{StaticResource SurfaceBrush}" CornerRadius="6" Padding="12" Margin="0,2">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="إجمالي الامتحانات" Style="{StaticResource BodyTextStyle}" FontSize="12"/>
                                <TextBlock Grid.Column="1" Text="{Binding Exams.Count}" Style="{StaticResource BodyTextStyle}" FontSize="12" FontWeight="Bold"/>
                            </Grid>
                        </Border>

                        <Border Background="{StaticResource SurfaceBrush}" CornerRadius="6" Padding="12" Margin="0,2">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="إجمالي الأسئلة" Style="{StaticResource BodyTextStyle}" FontSize="12"/>
                                <TextBlock Grid.Column="1" Text="{Binding Questions.Count}" Style="{StaticResource BodyTextStyle}" FontSize="12" FontWeight="Bold"/>
                            </Grid>
                        </Border>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>

        <!-- Status Bar -->
        <Border Grid.Row="2" Background="{StaticResource SecondaryBrush}" Padding="20,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0"
                          Text="{Binding StatusMessage}"
                          Style="{StaticResource BodyTextStyle}"
                          VerticalAlignment="Center"/>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <ProgressBar IsIndeterminate="{Binding IsLoading}"
                                Width="100" Height="4"
                                Foreground="{StaticResource InfoBrush}"
                                Margin="0,0,10,0"/>

                    <TextBlock Text="جاهز"
                              Style="{StaticResource BodyTextStyle}"
                              FontSize="12"
                              VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</mah:MetroWindow>
