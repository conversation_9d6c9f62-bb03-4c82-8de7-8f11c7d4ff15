using ExamBuilder.Core.Models;

namespace ExamBuilder.Core.Services
{
    /// <summary>
    /// تنفيذ مبسط لخدمة إدارة الامتحانات
    /// </summary>
    public class ExamServiceSimple : IExamService
    {
        private readonly IExamRepository _examRepository;
        private readonly IQuestionRepository _questionRepository;

        public ExamServiceSimple(
            IExamRepository examRepository,
            IQuestionRepository questionRepository)
        {
            _examRepository = examRepository;
            _questionRepository = questionRepository;
        }

        public async Task<Exam> CreateExamAsync(Exam exam)
        {
            var validationResult = await ValidateExamAsync(exam);
            if (!validationResult.IsValid)
            {
                throw new Exception($"بيانات الامتحان غير صحيحة: {string.Join(", ", validationResult.Errors)}");
            }

            exam.CreatedAt = DateTime.Now;
            exam.Id = 0;
            return await _examRepository.AddAsync(exam);
        }

        public async Task<Exam?> GetExamByIdAsync(int id)
        {
            return await _examRepository.GetByIdAsync(id);
        }

        public async Task<IEnumerable<Exam>> GetAllExamsAsync()
        {
            return await _examRepository.GetAllAsync();
        }

        public async Task<IEnumerable<Exam>> GetExamsByUserAsync(string userId)
        {
            return await _examRepository.GetByUserAsync(userId);
        }

        public async Task<Exam> UpdateExamAsync(Exam exam)
        {
            var existingExam = await _examRepository.GetByIdAsync(exam.Id);
            if (existingExam == null)
            {
                throw new Exception($"الامتحان غير موجود: {exam.Id}");
            }

            var validationResult = await ValidateExamAsync(exam);
            if (!validationResult.IsValid)
            {
                throw new Exception($"بيانات الامتحان غير صحيحة: {string.Join(", ", validationResult.Errors)}");
            }

            exam.UpdatedAt = DateTime.Now;
            return await _examRepository.UpdateAsync(exam);
        }

        public async Task<bool> DeleteExamAsync(int id)
        {
            if (!await CanDeleteExamAsync(id))
            {
                throw new Exception("لا يمكن حذف هذا الامتحان");
            }

            return await _examRepository.DeleteAsync(id);
        }

        public async Task<IEnumerable<Exam>> SearchExamsAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return await GetAllExamsAsync();
            }
            return await _examRepository.SearchAsync(searchTerm);
        }

        public async Task<IEnumerable<Exam>> GetExamsBySubjectAsync(string subject)
        {
            return await _examRepository.GetBySubjectAsync(subject);
        }

        public async Task<IEnumerable<Exam>> GetExamsByGradeAsync(string grade)
        {
            return await _examRepository.GetByGradeAsync(grade);
        }

        public async Task<IEnumerable<Exam>> GetRecentExamsAsync(int count = 10)
        {
            return await _examRepository.GetRecentAsync(count);
        }

        public async Task<Question> AddQuestionToExamAsync(int examId, Question question)
        {
            var exam = await _examRepository.GetByIdAsync(examId);
            if (exam == null)
            {
                throw new Exception($"الامتحان غير موجود: {examId}");
            }

            question.ExamId = examId;
            question.CreatedAt = DateTime.Now;
            return await _questionRepository.AddAsync(question);
        }

        public async Task<Question> UpdateQuestionAsync(Question question)
        {
            question.UpdatedAt = DateTime.Now;
            return await _questionRepository.UpdateAsync(question);
        }

        public async Task<bool> RemoveQuestionFromExamAsync(int examId, int questionId)
        {
            return await _questionRepository.DeleteAsync(questionId);
        }

        public async Task<bool> ReorderQuestionsAsync(int examId, List<int> questionIds)
        {
            return await _questionRepository.ReorderAsync(examId, questionIds);
        }

        public async Task<Exam> DuplicateExamAsync(int examId, string newTitle)
        {
            var originalExam = await _examRepository.GetByIdAsync(examId);
            if (originalExam == null)
            {
                throw new Exception($"الامتحان غير موجود: {examId}");
            }

            var duplicatedExam = new Exam
            {
                Title = newTitle,
                Description = originalExam.Description,
                Subject = originalExam.Subject,
                TeacherName = originalExam.TeacherName,
                SchoolName = originalExam.SchoolName,
                Grade = originalExam.Grade,
                DurationMinutes = originalExam.DurationMinutes,
                TotalMarks = originalExam.TotalMarks,
                CreatedBy = originalExam.CreatedBy,
                Settings = originalExam.Settings
            };

            var createdExam = await CreateExamAsync(duplicatedExam);

            foreach (var question in originalExam.Questions)
            {
                var duplicatedQuestion = new Question
                {
                    Text = question.Text,
                    Type = question.Type,
                    Points = question.Points,
                    Order = question.Order,
                    ImagePath = question.ImagePath,
                    Explanation = question.Explanation,
                    Difficulty = question.Difficulty,
                    Category = question.Category,
                    Tags = question.Tags
                };

                await AddQuestionToExamAsync(createdExam.Id, duplicatedQuestion);
            }

            return createdExam;
        }

        public async Task<Exam> CreateExamFromTemplateAsync(int templateId, string title)
        {
            await Task.Delay(100);
            throw new NotImplementedException();
        }

        public async Task<string> ExportExamAsync(int examId, ExportFormat format)
        {
            await Task.Delay(100);
            throw new NotImplementedException();
        }

        public async Task<Exam> ImportExamAsync(string filePath, ImportFormat format)
        {
            await Task.Delay(100);
            throw new NotImplementedException();
        }

        public async Task<ExamStatistics> GetExamStatisticsAsync(int examId)
        {
            await Task.Delay(100);
            return new ExamStatistics { ExamId = examId };
        }

        public async Task<IEnumerable<ExamStatistics>> GetUserExamStatisticsAsync(string userId)
        {
            await Task.Delay(100);
            return new List<ExamStatistics>();
        }

        public async Task<ValidationResult> ValidateExamAsync(Exam exam)
        {
            await Task.Delay(100);
            var result = new ValidationResult();

            if (string.IsNullOrWhiteSpace(exam.Title))
            {
                result.Errors.Add("عنوان الامتحان مطلوب");
            }

            if (string.IsNullOrWhiteSpace(exam.Subject))
            {
                result.Errors.Add("اسم المادة مطلوب");
            }

            if (exam.DurationMinutes <= 0)
            {
                result.Errors.Add("مدة الامتحان يجب أن تكون أكبر من صفر");
            }

            if (exam.TotalMarks <= 0)
            {
                result.Errors.Add("إجمالي الدرجات يجب أن يكون أكبر من صفر");
            }

            result.IsValid = !result.Errors.Any();
            return result;
        }

        public async Task<bool> CanDeleteExamAsync(int examId)
        {
            await Task.Delay(100);
            return true;
        }
    }
}
