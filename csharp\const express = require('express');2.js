const express = require('express');
const crypto = require('crypto'); // لتوليد الأكواد الآمنة

const app = express();
app.use(express.json()); // للسماح باستقبال بيانات JSON

const PORT = process.env.PORT || 3000;

// --- قاعدة بيانات وهمية للتوضيح ---
// في المشروع الحقيقي، سيتم استبدال هذا بـ MongoDB
const db = {
    videos: [],
    students: [],
    access_tokens: []
};
// ------------------------------------

/**
 * @route   GET /watch/:token
 * @desc    واجهة الطالب لمشاهدة الفيديو
 * @access  Public (but requires a valid token)
 */
app.get('/watch/:token', (req, res) => {
    const { token } = req.params;
    const clientIp = req.ip; // الحصول على IP الخاص بالمستخدم

    // 1. ابحث عن الكود في قاعدة البيانات
    const accessToken = db.access_tokens.find(t => t.token === token);

    // 2. تحقق من صلاحية الكود
    if (!accessToken || accessToken.isUsed || new Date() > accessToken.expiresAt) {
        return res.status(403).send('<h1>هذا الكود غير صالح أو تم استخدامه من قبل</h1>');
    }

    // 3. (اختياري) ربط أول استخدام بـ IP معين
    if (!accessToken.clientIp) {
        accessToken.clientIp = clientIp;
    } else if (accessToken.clientIp !== clientIp) {
        return res.status(403).send('<h1>هذا الكود مرتبط بجهاز آخر</h1>');
    }

    // 4. تحديث الكود كـ "مستخدم"
    accessToken.isUsed = true;
    accessToken.usedAt = new Date();

    // 5. جلب رابط الفيديو من قاعدة البيانات
    const video = db.videos.find(v => v.id === accessToken.videoId);
    if (!video) {
        return res.status(404).send('<h1>الفيديو غير موجود</h1>');
    }

    // استخراج معرف الفيديو من رابط يوتيوب
    const videoId = new URL(video.youtubeUrl).searchParams.get('v');

    // 6. عرض صفحة الفيديو
    // ملاحظة: لمنع التحميل، يتم تضمين الفيديو فقط (embed)
    // يتم إخفاء عناصر التحكم لمنع المشاركة السهلة
    res.send(`
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>${video.title}</title>
            <style>
                body { font-family: sans-serif; display: flex; justify-content: center; align-items: center; height: 100vh; margin: 0; background-color: #f0f0f0; }
                .video-container { position: relative; width: 80%; max-width: 900px; }
                .video-container::before { content: ""; display: block; padding-top: 56.25%; } /* 16:9 Aspect Ratio */
                iframe { position: absolute; top: 0; left: 0; width: 100%; height: 100%; border: 0; }
            </style>
        </head>
        <body>
            <div class="video-container">
                <iframe src="https://www.youtube.com/embed/${videoId}?rel=0&controls=0&modestbranding=1" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
            </div>
        </body>
        </html>
    `);
});

app.listen(PORT, () => {
    console.log(`Server is running on http://localhost:${PORT}`);
});