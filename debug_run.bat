@echo off
echo ========================================
echo    Debug Mode - Exam Builder
echo ========================================
echo.

echo Copying settings...
copy ExamBuilder.UI\appsettings.json ExamBuilder.UI\bin\Release\net8.0-windows\ >nul

echo.
echo Starting application in debug mode...
echo Login: Username=جو Password=جو
echo.
echo Debug info will be shown in console...
echo.

cd ExamBuilder.UI\bin\Release\net8.0-windows
dotnet ExamBuilder.UI.dll

echo.
echo Application closed.
echo Check logs folder for detailed information.
pause
