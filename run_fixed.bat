@echo off
echo ========================================
echo    Exam Builder - Fixed Version
echo ========================================
echo.

echo Building project...
dotnet build --configuration Release

if %errorlevel% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo Copying settings...
copy ExamBuilder.UI\appsettings.json ExamBuilder.UI\bin\Release\net8.0-windows\ >nul

echo.
echo Starting application...
echo.
echo FIXED ISSUES:
echo - No auto-fill for username/password
echo - Working theme buttons
echo - Permission request dialog
echo - Updated packages (no warnings)
echo.
echo LOGIN INSTRUCTIONS:
echo 1. Enter username: جو (or joe or Jo)
echo 2. Enter password: جو (or joe or Jo)  
echo 3. Click login button
echo 4. Approve permission when asked
echo 5. Main window will open
echo.

cd ExamBuilder.UI\bin\Release\net8.0-windows
start ExamBuilder.UI.exe

echo.
echo Application launched successfully!
pause
