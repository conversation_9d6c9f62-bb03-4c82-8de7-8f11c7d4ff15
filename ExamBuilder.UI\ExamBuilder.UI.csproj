<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>

    <AssemblyTitle>برنامج إنشاء الامتحانات - المهندس يوسف غنام</AssemblyTitle>
    <AssemblyDescription>برنامج احترافي لإنشاء وإدارة الامتحانات مع دعم كامل للغة العربية</AssemblyDescription>
    <AssemblyCompany>Youssef Ghannam</AssemblyCompany>
    <AssemblyProduct>Exam Builder Pro</AssemblyProduct>
    <AssemblyCopyright>© 2024 Youssef Ghannam. All rights reserved.</AssemblyCopyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MahApps.Metro" Version="2.4.10" />
    <PackageReference Include="MahApps.Metro.IconPacks" Version="4.11.0" />
    <PackageReference Include="MaterialDesignThemes" Version="4.9.0" />
    <PackageReference Include="MaterialDesignColors" Version="2.1.4" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
    <PackageReference Include="Serilog" Version="3.1.1" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ExamBuilder.Core\ExamBuilder.Core.csproj" />
    <ProjectReference Include="..\ExamBuilder.Data\ExamBuilder.Data.csproj" />
    <ProjectReference Include="..\ExamBuilder.Security\ExamBuilder.Security.csproj" />
    <ProjectReference Include="..\ExamBuilder.PDF\ExamBuilder.PDF.csproj" />
    <ProjectReference Include="..\ExamBuilder.Telegram\ExamBuilder.Telegram.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="Resources\**\*" />
  </ItemGroup>

  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
