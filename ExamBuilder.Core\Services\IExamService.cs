using ExamBuilder.Core.Models;

namespace ExamBuilder.Core.Services
{
    /// <summary>
    /// واجهة خدمة إدارة الامتحانات
    /// </summary>
    public interface IExamService
    {
        // العمليات الأساسية
        Task<Exam> CreateExamAsync(Exam exam);
        Task<Exam?> GetExamByIdAsync(int id);
        Task<IEnumerable<Exam>> GetAllExamsAsync();
        Task<IEnumerable<Exam>> GetExamsByUserAsync(string userId);
        Task<Exam> UpdateExamAsync(Exam exam);
        Task<bool> DeleteExamAsync(int id);

        // البحث والتصفية
        Task<IEnumerable<Exam>> SearchExamsAsync(string searchTerm);
        Task<IEnumerable<Exam>> GetExamsBySubjectAsync(string subject);
        Task<IEnumerable<Exam>> GetExamsByGradeAsync(string grade);
        Task<IEnumerable<Exam>> GetRecentExamsAsync(int count = 10);

        // إدارة الأسئلة
        Task<Question> AddQuestionToExamAsync(int examId, Question question);
        Task<Question> UpdateQuestionAsync(Question question);
        Task<bool> RemoveQuestionFromExamAsync(int examId, int questionId);
        Task<bool> ReorderQuestionsAsync(int examId, List<int> questionIds);

        // النسخ والاستنساخ
        Task<Exam> DuplicateExamAsync(int examId, string newTitle);
        Task<Exam> CreateExamFromTemplateAsync(int templateId, string title);

        // التصدير والاستيراد
        Task<string> ExportExamAsync(int examId, ExportFormat format);
        Task<Exam> ImportExamAsync(string filePath, ImportFormat format);

        // الإحصائيات
        Task<ExamStatistics> GetExamStatisticsAsync(int examId);
        Task<IEnumerable<ExamStatistics>> GetUserExamStatisticsAsync(string userId);

        // التحقق من الصحة
        Task<ValidationResult> ValidateExamAsync(Exam exam);
        Task<bool> CanDeleteExamAsync(int examId);
    }

    // النماذج المساعدة
    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new();
    }

    public class ExamStatistics
    {
        public int ExamId { get; set; }
        public int TotalQuestions { get; set; }
        public decimal AverageScore { get; set; }
        public int TimesUsed { get; set; }
        public DateTime LastUsed { get; set; }
    }

    public enum ExportFormat
    {
        PDF,
        Word,
        Excel,
        Json
    }

    public enum ImportFormat
    {
        Excel,
        Word,
        Json,
        Xml
    }
}
