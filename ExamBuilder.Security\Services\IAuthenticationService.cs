using System.Security.Cryptography;
using System.Text;
using BCrypt.Net;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace ExamBuilder.Security.Services
{
    /// <summary>
    /// واجهة خدمة المصادقة
    /// </summary>
    public interface IAuthenticationService
    {
        Task<AuthenticationResult> AuthenticateAsync(string username, string password);
        Task<bool> ChangePasswordAsync(string username, string oldPassword, string newPassword);
        Task<string> GeneratePasswordResetTokenAsync(string username);
        Task<bool> ResetPasswordAsync(string username, string token, string newPassword);
        Task<bool> ValidateSessionAsync(string sessionToken);
        Task<string> CreateSessionAsync(string username);
        Task<bool> InvalidateSessionAsync(string sessionToken);
        Task<UserInfo?> GetCurrentUserAsync(string sessionToken);
    }

    /// <summary>
    /// تنفيذ خدمة المصادقة
    /// </summary>
    public class AuthenticationService : IAuthenticationService
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<AuthenticationService> _logger;
        private readonly IEncryptionService _encryptionService;
        
        // بيانات المستخدم الثابتة (كما هو مطلوب)
        private readonly Dictionary<string, UserCredentials> _users;
        private readonly Dictionary<string, UserSession> _activeSessions;

        public AuthenticationService(
            IConfiguration configuration,
            ILogger<AuthenticationService> logger,
            IEncryptionService encryptionService)
        {
            _configuration = configuration;
            _logger = logger;
            _encryptionService = encryptionService;
            _activeSessions = new Dictionary<string, UserSession>();

            // إعداد المستخدم الثابت
            _users = new Dictionary<string, UserCredentials>
            {
                {
                    "جو",
                    new UserCredentials
                    {
                        Username = "جو",
                        PasswordHash = BCrypt.Net.BCrypt.HashPassword("جو"),
                        IsActive = true,
                        CreatedAt = DateTime.Now,
                        LastLoginAt = null,
                        Role = UserRole.Administrator
                    }
                }
            };
        }

        public async Task<AuthenticationResult> AuthenticateAsync(string username, string password)
        {
            try
            {
                _logger.LogInformation($"محاولة تسجيل دخول للمستخدم: {username}");

                // التحقق من وجود المستخدم
                if (!_users.TryGetValue(username, out var userCredentials))
                {
                    _logger.LogWarning($"المستخدم غير موجود: {username}");
                    return new AuthenticationResult
                    {
                        IsSuccess = false,
                        ErrorMessage = "اسم المستخدم أو كلمة المرور غير صحيحة"
                    };
                }

                // التحقق من حالة المستخدم
                if (!userCredentials.IsActive)
                {
                    _logger.LogWarning($"المستخدم غير نشط: {username}");
                    return new AuthenticationResult
                    {
                        IsSuccess = false,
                        ErrorMessage = "حساب المستخدم غير نشط"
                    };
                }

                // التحقق من كلمة المرور
                if (!BCrypt.Net.BCrypt.Verify(password, userCredentials.PasswordHash))
                {
                    _logger.LogWarning($"كلمة مرور خاطئة للمستخدم: {username}");
                    return new AuthenticationResult
                    {
                        IsSuccess = false,
                        ErrorMessage = "اسم المستخدم أو كلمة المرور غير صحيحة"
                    };
                }

                // تحديث آخر تسجيل دخول
                userCredentials.LastLoginAt = DateTime.Now;

                // إنشاء جلسة جديدة
                var sessionToken = await CreateSessionAsync(username);

                _logger.LogInformation($"تم تسجيل دخول المستخدم بنجاح: {username}");

                return new AuthenticationResult
                {
                    IsSuccess = true,
                    SessionToken = sessionToken,
                    User = new UserInfo
                    {
                        Username = userCredentials.Username,
                        Role = userCredentials.Role,
                        LastLoginAt = userCredentials.LastLoginAt
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"خطأ في تسجيل الدخول للمستخدم: {username}");
                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ErrorMessage = "حدث خطأ أثناء تسجيل الدخول"
                };
            }
        }

        public async Task<bool> ChangePasswordAsync(string username, string oldPassword, string newPassword)
        {
            try
            {
                // التحقق من المستخدم الحالي
                var authResult = await AuthenticateAsync(username, oldPassword);
                if (!authResult.IsSuccess)
                {
                    return false;
                }

                // تحديث كلمة المرور
                if (_users.TryGetValue(username, out var userCredentials))
                {
                    userCredentials.PasswordHash = BCrypt.Net.BCrypt.HashPassword(newPassword);
                    _logger.LogInformation($"تم تغيير كلمة المرور للمستخدم: {username}");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"خطأ في تغيير كلمة المرور للمستخدم: {username}");
                return false;
            }
        }

        public async Task<string> GeneratePasswordResetTokenAsync(string username)
        {
            try
            {
                if (!_users.ContainsKey(username))
                {
                    throw new ArgumentException("المستخدم غير موجود");
                }

                // إنشاء رمز إعادة تعيين كلمة المرور
                var token = Convert.ToBase64String(RandomNumberGenerator.GetBytes(32));
                var encryptedToken = _encryptionService.Encrypt($"{username}:{DateTime.Now.AddHours(1):yyyy-MM-dd HH:mm:ss}:{token}");

                _logger.LogInformation($"تم إنشاء رمز إعادة تعيين كلمة المرور للمستخدم: {username}");
                
                return encryptedToken;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"خطأ في إنشاء رمز إعادة تعيين كلمة المرور للمستخدم: {username}");
                throw;
            }
        }

        public async Task<bool> ResetPasswordAsync(string username, string token, string newPassword)
        {
            try
            {
                // فك تشفير الرمز والتحقق من صحته
                var decryptedToken = _encryptionService.Decrypt(token);
                var tokenParts = decryptedToken.Split(':');

                if (tokenParts.Length != 4 || tokenParts[0] != username)
                {
                    return false;
                }

                if (!DateTime.TryParse($"{tokenParts[1]} {tokenParts[2]}", out var expiryDate) || expiryDate < DateTime.Now)
                {
                    return false;
                }

                // تحديث كلمة المرور
                if (_users.TryGetValue(username, out var userCredentials))
                {
                    userCredentials.PasswordHash = BCrypt.Net.BCrypt.HashPassword(newPassword);
                    _logger.LogInformation($"تم إعادة تعيين كلمة المرور للمستخدم: {username}");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"خطأ في إعادة تعيين كلمة المرور للمستخدم: {username}");
                return false;
            }
        }

        public async Task<bool> ValidateSessionAsync(string sessionToken)
        {
            try
            {
                if (string.IsNullOrEmpty(sessionToken))
                {
                    return false;
                }

                if (_activeSessions.TryGetValue(sessionToken, out var session))
                {
                    // التحقق من انتهاء صلاحية الجلسة
                    if (session.ExpiresAt > DateTime.Now)
                    {
                        // تحديث آخر نشاط
                        session.LastActivity = DateTime.Now;
                        return true;
                    }
                    else
                    {
                        // إزالة الجلسة المنتهية الصلاحية
                        _activeSessions.Remove(sessionToken);
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحقق من صحة الجلسة");
                return false;
            }
        }

        public async Task<string> CreateSessionAsync(string username)
        {
            try
            {
                // إنشاء رمز جلسة جديد
                var sessionToken = Convert.ToBase64String(RandomNumberGenerator.GetBytes(32));
                
                var session = new UserSession
                {
                    SessionToken = sessionToken,
                    Username = username,
                    CreatedAt = DateTime.Now,
                    LastActivity = DateTime.Now,
                    ExpiresAt = DateTime.Now.AddHours(8) // انتهاء الصلاحية بعد 8 ساعات
                };

                _activeSessions[sessionToken] = session;

                _logger.LogInformation($"تم إنشاء جلسة جديدة للمستخدم: {username}");
                
                return sessionToken;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"خطأ في إنشاء جلسة للمستخدم: {username}");
                throw;
            }
        }

        public async Task<bool> InvalidateSessionAsync(string sessionToken)
        {
            try
            {
                if (_activeSessions.Remove(sessionToken))
                {
                    _logger.LogInformation("تم إلغاء الجلسة بنجاح");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إلغاء الجلسة");
                return false;
            }
        }

        public async Task<UserInfo?> GetCurrentUserAsync(string sessionToken)
        {
            try
            {
                if (await ValidateSessionAsync(sessionToken))
                {
                    if (_activeSessions.TryGetValue(sessionToken, out var session))
                    {
                        if (_users.TryGetValue(session.Username, out var userCredentials))
                        {
                            return new UserInfo
                            {
                                Username = userCredentials.Username,
                                Role = userCredentials.Role,
                                LastLoginAt = userCredentials.LastLoginAt
                            };
                        }
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على معلومات المستخدم الحالي");
                return null;
            }
        }
    }

    // النماذج المساعدة
    public class AuthenticationResult
    {
        public bool IsSuccess { get; set; }
        public string? SessionToken { get; set; }
        public UserInfo? User { get; set; }
        public string? ErrorMessage { get; set; }
    }

    public class UserInfo
    {
        public string Username { get; set; } = string.Empty;
        public UserRole Role { get; set; }
        public DateTime? LastLoginAt { get; set; }
    }

    public class UserCredentials
    {
        public string Username { get; set; } = string.Empty;
        public string PasswordHash { get; set; } = string.Empty;
        public bool IsActive { get; set; } = true;
        public UserRole Role { get; set; } = UserRole.User;
        public DateTime CreatedAt { get; set; }
        public DateTime? LastLoginAt { get; set; }
    }

    public class UserSession
    {
        public string SessionToken { get; set; } = string.Empty;
        public string Username { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime LastActivity { get; set; }
        public DateTime ExpiresAt { get; set; }
    }

    public enum UserRole
    {
        User = 0,
        Administrator = 1
    }
}
