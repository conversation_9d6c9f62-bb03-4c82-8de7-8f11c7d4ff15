<mah:MetroWindow x:Class="ExamBuilder.UI.Views.LoginWindow"
                 xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                 xmlns:mah="http://metro.mahapps.com/winfx/xaml/controls"
                 xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
                 xmlns:iconPacks="http://metro.mahapps.com/winfx/xaml/iconpacks"
                 Title="تسجيل الدخول - برنامج إنشاء الامتحانات"
                 Height="700" Width="550"
                 WindowStartupLocation="CenterScreen"
                 ResizeMode="CanResize"
                 MinHeight="600" MinWidth="450"
                 FlowDirection="RightToLeft"
                 Background="{StaticResource AccentBrush}"
                 WindowStyle="None"
                 AllowsTransparency="True">

    <mah:MetroWindow.Resources>
        <!-- Animation Storyboards -->
        <Storyboard x:Key="FadeInAnimation">
            <DoubleAnimation Storyboard.TargetProperty="Opacity" From="0" To="1" Duration="0:0:0.5"/>
        </Storyboard>

        <Storyboard x:Key="SlideInAnimation">
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)"
                           From="50" To="0" Duration="0:0:0.6">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
        </Storyboard>
    </mah:MetroWindow.Resources>

    <mah:MetroWindow.Triggers>
        <EventTrigger RoutedEvent="Loaded">
            <BeginStoryboard Storyboard="{StaticResource FadeInAnimation}"/>
        </EventTrigger>
    </mah:MetroWindow.Triggers>

    <Border CornerRadius="15" Background="{StaticResource AccentBrush}">
        <Border.Effect>
            <DropShadowEffect Color="Black" Direction="315" ShadowDepth="10" Opacity="0.5" BlurRadius="20"/>
        </Border.Effect>

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header Section -->
            <Border Grid.Row="0" Background="{StaticResource PrimaryBrush}" CornerRadius="15,15,0,0" Padding="20">
                <StackPanel>
                    <!-- Close Button -->
                    <Button x:Name="CloseButton"
                            HorizontalAlignment="Left"
                            VerticalAlignment="Top"
                            Width="30" Height="30"
                            Background="Transparent"
                            BorderThickness="0"
                            Cursor="Hand"
                            Click="CloseButton_Click">
                        <iconPacks:PackIconMaterial Kind="Close"
                                                  Foreground="White"
                                                  Width="16" Height="16"/>
                    </Button>

                    <!-- Logo and Title -->
                    <StackPanel Orientation="Vertical" HorizontalAlignment="Center" Margin="0,10,0,0">
                        <Ellipse Width="80" Height="80"
                                Fill="{StaticResource InfoBrush}"
                                Margin="0,0,0,15">
                            <Ellipse.Effect>
                                <DropShadowEffect Color="Black" Direction="315" ShadowDepth="3" Opacity="0.3" BlurRadius="8"/>
                            </Ellipse.Effect>
                        </Ellipse>

                        <TextBlock Text="برنامج إنشاء الامتحانات"
                                  Style="{StaticResource HeaderTextStyle}"
                                  Foreground="White"
                                  FontSize="24"
                                  Margin="0,0,0,5"/>

                        <TextBlock Text="المهندس يوسف غنام"
                                  Style="{StaticResource SubHeaderTextStyle}"
                                  Foreground="{StaticResource InfoBrush}"
                                  FontSize="16"/>
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- Login Form -->
            <StackPanel Grid.Row="1"
                       Margin="40,30,40,20"
                       VerticalAlignment="Center">

                <StackPanel.RenderTransform>
                    <TranslateTransform/>
                </StackPanel.RenderTransform>

                <StackPanel.Triggers>
                    <EventTrigger RoutedEvent="Loaded">
                        <BeginStoryboard Storyboard="{StaticResource SlideInAnimation}"/>
                    </EventTrigger>
                </StackPanel.Triggers>

                <!-- Welcome Message -->
                <TextBlock Text="يرجى إدخال بيانات تسجيل الدخول"
                          Style="{StaticResource SubHeaderTextStyle}"
                          HorizontalAlignment="Center"
                          FontSize="16"
                          Margin="0,0,0,30"/>

                <!-- Username Field -->
                <StackPanel Margin="0,0,0,20">
                    <TextBlock Text="اسم المستخدم"
                              Style="{StaticResource BodyTextStyle}"
                              Margin="0,0,0,8"/>
                    <TextBox x:Name="UsernameTextBox"
                            Style="{StaticResource InputTextBoxStyle}"
                            materialDesign:HintAssist.Hint="أدخل اسم المستخدم"
                            materialDesign:HintAssist.IsFloating="True"
                            Background="#2A2A2A"
                            Foreground="White"/>
                </StackPanel>

                <!-- Password Field -->
                <StackPanel Margin="0,0,0,30">
                    <TextBlock Text="كلمة المرور"
                              Style="{StaticResource BodyTextStyle}"
                              Margin="0,0,0,8"/>
                    <PasswordBox x:Name="PasswordBox"
                                Style="{StaticResource InputPasswordBoxStyle}"
                                materialDesign:HintAssist.Hint="أدخل كلمة المرور"
                                materialDesign:HintAssist.IsFloating="True"
                                Background="#2A2A2A"
                                Foreground="White"
                                KeyDown="PasswordBox_KeyDown"/>
                </StackPanel>

                <!-- Login Button -->
                <Button x:Name="LoginButton"
                       Content="تسجيل الدخول"
                       Style="{StaticResource PrimaryButtonStyle}"
                       Height="50"
                       FontSize="18"
                       Click="LoginButton_Click"
                       IsDefault="True">
                    <Button.Effect>
                        <DropShadowEffect Color="Black" Direction="315" ShadowDepth="3" Opacity="0.3" BlurRadius="8"/>
                    </Button.Effect>
                </Button>

                <!-- Status Message -->
                <TextBlock x:Name="StatusTextBlock"
                          Style="{StaticResource BodyTextStyle}"
                          HorizontalAlignment="Center"
                          Margin="0,15,0,0"
                          FontWeight="Bold"
                          Visibility="Collapsed"/>

                <!-- Loading Indicator -->
                <ProgressBar x:Name="LoadingProgressBar"
                            IsIndeterminate="True"
                            Height="4"
                            Margin="0,15,0,0"
                            Foreground="{StaticResource InfoBrush}"
                            Visibility="Collapsed"/>

            </StackPanel>

            <!-- Footer -->
            <Border Grid.Row="2"
                   Background="{StaticResource SecondaryBrush}"
                   CornerRadius="0,0,15,15"
                   Padding="20,15">
                <StackPanel>
                    <!-- Theme Toggle -->
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,10">
                        <TextBlock Text="الثيم:"
                                  Style="{StaticResource BodyTextStyle}"
                                  VerticalAlignment="Center"
                                  Margin="0,0,10,0"/>

                        <Button x:Name="RedThemeButton"
                               Content="أحمر"
                               Width="60" Height="30"
                               FontSize="12"
                               Background="{StaticResource PrimaryBrush}"
                               Foreground="White"
                               BorderThickness="0"
                               Margin="5,0"
                               Click="RedThemeButton_Click"/>

                        <Button x:Name="GreenThemeButton"
                               Content="أخضر"
                               Width="60" Height="30"
                               FontSize="12"
                               Background="#2E8B57"
                               Foreground="White"
                               BorderThickness="0"
                               Margin="5,0"
                               Click="GreenThemeButton_Click"/>

                        <Button x:Name="DarkThemeButton"
                               Content="داكن"
                               Width="60" Height="30"
                               FontSize="12"
                               Background="#2C2C2C"
                               Foreground="White"
                               BorderThickness="0"
                               Margin="5,0"
                               Click="DarkThemeButton_Click"/>
                    </StackPanel>

                    <!-- Copyright -->
                    <TextBlock Text="© 2024 المهندس يوسف غنام - جميع الحقوق محفوظة"
                              Style="{StaticResource BodyTextStyle}"
                              HorizontalAlignment="Center"
                              FontSize="12"
                              Opacity="0.8"/>
                </StackPanel>
            </Border>

        </Grid>
    </Border>
</mah:MetroWindow>
