import tkinter
import customtkinter as ctk
import os
import json
import uuid
import platform

class QRStudentApp(ctk.CTk):
    def __init__(self):
        super().__init__()
        self.title("تشغيل فيديو بكود QR محمي")
        self.geometry("500x300")
        self.resizable(False, False)

        self.label = ctk.CTkLabel(self, text="أدخل كود QR (code_id) أو اختر ملف البيانات:")
        self.label.pack(pady=10)

        self.code_entry = ctk.CTkEntry(self, placeholder_text="code_id أو اسم ملف json")
        self.code_entry.pack(pady=5, fill="x", padx=30)

        self.browse_btn = ctk.CTkButton(self, text="اختيار ملف...", command=self.browse_file)
        self.browse_btn.pack(pady=5)

        self.open_btn = ctk.CTkButton(self, text="تشغيل الفيديو", command=self.open_video)
        self.open_btn.pack(pady=20)

        self.result_label = ctk.CTkLabel(self, text="", wraplength=400, justify="center")
        self.result_label.pack(pady=10)

    def get_device_id(self):
        # استخدم uuid + platform لتمييز الجهاز
        return str(uuid.uuid5(uuid.NAMESPACE_DNS, platform.node() + platform.system()))

    def browse_file(self):
        from tkinter import filedialog
        file_path = filedialog.askopenfilename(title="اختر ملف بيانات الكود", filetypes=[("JSON Files", "*.json")])
        if file_path:
            self.code_entry.delete(0, "end")
            self.code_entry.insert(0, file_path)

    def open_video(self):
        code_input = self.code_entry.get().strip()
        if not code_input:
            self.result_label.configure(text="يرجى إدخال code_id أو اختيار ملف البيانات.", text_color="red")
            return
        # تحديد مسار ملف البيانات
        if code_input.endswith(".json"):
            data_path = code_input
        else:
            data_path = os.path.join("qrdata", f"data_{code_input}.json")
        if not os.path.exists(data_path):
            self.result_label.configure(text="ملف بيانات الكود غير موجود!", text_color="red")
            return
        try:
            with open(data_path, "r", encoding="utf-8") as f:
                code_data = json.load(f)
        except Exception as e:
            self.result_label.configure(text=f"خطأ في قراءة الملف: {e}", text_color="red")
            return
        device_id = self.get_device_id()
        # تحقق من الجهاز وعدد مرات الاستخدام
        if code_data["device_id"] is None:
            # أول استخدام: اربط الكود بالجهاز
            code_data["device_id"] = device_id
            code_data["usage_count"] = 1
            msg = "تم ربط الكود بهذا الجهاز.\nجاري فتح الفيديو..."
        elif code_data["device_id"] == device_id:
            if code_data["usage_count"] < code_data["usage_limit"]:
                code_data["usage_count"] += 1
                msg = f"تم فتح الفيديو. عدد مرات الاستخدام المتبقية: {code_data['usage_limit']-code_data['usage_count']}"
            else:
                self.result_label.configure(text="تم استهلاك جميع مرات الاستخدام لهذا الكود!", text_color="red")
                return
        else:
            self.result_label.configure(text="هذا الكود مرتبط بجهاز آخر ولا يمكن استخدامه هنا!", text_color="red")
            return
        # حفظ التحديثات
        with open(data_path, "w", encoding="utf-8") as f:
            json.dump(code_data, f, ensure_ascii=False, indent=2)
        self.result_label.configure(text=msg, text_color="green")
        # فتح الفيديو
        import webbrowser
        webbrowser.open(code_data["video_url"])

if __name__ == "__main__":
    app = QRStudentApp()
    app.mainloop()
