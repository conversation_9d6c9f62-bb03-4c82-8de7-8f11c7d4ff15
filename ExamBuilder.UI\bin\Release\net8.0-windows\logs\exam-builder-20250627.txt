2025-06-27 01:47:22.163 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-27 01:47:22.243 +04:00 [INF] Hosting environment: Production
2025-06-27 01:47:22.245 +04:00 [INF] Content root path: D:\mr\qqqq\ExamBuilder.UI\bin\Release\net8.0-windows
2025-06-27 01:47:22.247 +04:00 [INF] تم بدء تشغيل التطبيق بنجاح
2025-06-27 01:47:23.197 +04:00 [INF] تم تحميل نافذة تسجيل الدخول
2025-06-27 01:47:41.310 +04:00 [INF] محاولة تسجيل دخول - المستخدم: 'جو', كلمة المرور: 'جو'
2025-06-27 01:47:41.310 +04:00 [INF] تحقق المستخدم: True, تحقق كلمة المرور: True
2025-06-27 01:47:41.310 +04:00 [INF] تم التحقق من البيانات بنجاح
2025-06-27 01:47:42.316 +04:00 [INF] محاكاة إرسال طلب إذن للمستخدم: جو
2025-06-27 01:47:42.817 +04:00 [INF] محاكاة انتظار الموافقة...
2025-06-27 01:47:43.324 +04:00 [INF] تم تطبيق الثيم الأحمر
2025-06-27 01:47:44.306 +04:00 [INF] تم تطبيق الثيم الأخضر
2025-06-27 01:47:45.820 +04:00 [INF] تم الحصول على الموافقة (محاكاة)
2025-06-27 01:47:46.820 +04:00 [INF] محاولة فتح النافذة الرئيسية
2025-06-27 01:47:46.835 +04:00 [ERR] خطأ في فتح النافذة الرئيسية
System.Windows.Markup.XamlParseException: Provide value on 'System.Windows.Baml2006.TypeConverterMarkupExtension' threw an exception.
 ---> System.FormatException: Settings is not a valid value for PackIconMaterialKind.
 ---> System.ArgumentException: Requested value 'Settings' was not found.
   at System.Enum.TryParseByName[TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TStorage& result)
   at System.Enum.TryParseByValueOrName[TUnderlying,TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TUnderlying& result)
   at System.Enum.TryParse(Type enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, Object& result)
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   --- End of inner exception stack trace ---
   at System.ComponentModel.EnumConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   at MS.Internal.Xaml.Runtime.ClrObjectRuntime.CallProvideValue(MarkupExtension me, IServiceProvider serviceProvider)
   --- End of inner exception stack trace ---
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.LoadBaml(XamlReader xamlReader, Boolean skipJournaledProperties, Object rootObject, XamlAccessLevel accessLevel, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at ExamBuilder.UI.Views.MainWindow.InitializeComponent() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml:line 1
   at ExamBuilder.UI.Views.MainWindow..ctor() in D:\mr\qqqq\ExamBuilder.UI\Views\MainWindow.xaml.cs:line 18
   at ExamBuilder.UI.Views.LoginWindow.PerformLogin() in D:\mr\qqqq\ExamBuilder.UI\Views\LoginWindow.xaml.cs:line 155
2025-06-27 01:47:46.913 +04:00 [INF] تم تسجيل دخول المستخدم: جو
2025-06-27 01:47:50.712 +04:00 [INF] محاولة تسجيل دخول - المستخدم: 'جو', كلمة المرور: 'جو'
2025-06-27 01:47:50.712 +04:00 [INF] تحقق المستخدم: True, تحقق كلمة المرور: True
2025-06-27 01:47:50.713 +04:00 [INF] تم التحقق من البيانات بنجاح
2025-06-27 01:47:51.713 +04:00 [INF] محاكاة إرسال طلب إذن للمستخدم: جو
2025-06-27 01:47:52.215 +04:00 [INF] محاكاة انتظار الموافقة...
