using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using MahApps.Metro.Controls;
using Serilog;
using ExamBuilder.Security.Services;
using ExamBuilder.UI.ViewModels;
using ExamBuilder.Telegram.Services;

namespace ExamBuilder.UI.Views
{
    /// <summary>
    /// نافذة تسجيل الدخول
    /// </summary>
    public partial class LoginWindow : MetroWindow
    {
        private readonly ITelegramAuthService _telegramAuthService;

        public LoginWindow()
        {
            InitializeComponent();

            // الحصول على خدمة التليجرام
            try
            {
                _telegramAuthService = App.GetService<ITelegramAuthService>();
            }
            catch
            {
                // في حالة عدم توفر الخدمة، استخدم المحاكاة
                _telegramAuthService = null;
            }

            // إزالة الملء التلقائي - المستخدم يدخل البيانات بنفسه
            UsernameTextBox.IsReadOnly = false;
            UsernameTextBox.Text = "";
            PasswordBox.Password = "";

            Log.Information("تم تحميل نافذة تسجيل الدخول");
        }

        private async void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            await PerformLogin();
        }

        private async void PasswordBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                await PerformLogin();
            }
        }

        private async Task PerformLogin()
        {
            try
            {
                // إظهار مؤشر التحميل
                ShowLoading(true);

                var username = UsernameTextBox.Text.Trim();
                var password = PasswordBox.Password;

                // التحقق من صحة البيانات
                if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
                {
                    ShowStatus("يرجى إدخال اسم المستخدم وكلمة المرور", StatusType.Error);
                    return;
                }

                // تسجيل دخول مبسط - التحقق من البيانات الثابتة
                ShowStatus("جارٍ التحقق من بيانات تسجيل الدخول...", StatusType.Info);

                // تأخير قصير لمحاكاة عملية التحقق
                await Task.Delay(1000);

                Log.Information($"محاولة تسجيل دخول - المستخدم: '{username}', كلمة المرور: '{password}'");

                // تجربة مقارنات متعددة
                bool isValidUser = username == "جو" || username == "joe" || username == "Jo" || username.Trim() == "جو";
                bool isValidPass = password == "جو" || password == "joe" || password == "Jo" || password.Trim() == "جو";

                Log.Information($"تحقق المستخدم: {isValidUser}, تحقق كلمة المرور: {isValidPass}");

                if (isValidUser && isValidPass)
                {
                    ShowStatus("تم التحقق من البيانات بنجاح", StatusType.Success);
                    Log.Information("تم التحقق من البيانات بنجاح");

                    // تأخير قصير
                    await Task.Delay(1000);

                    // طلب إذن الدخول عبر التليجرام
                    ShowStatus("إرسال طلب إذن عبر التليجرام...", StatusType.Info);

                    bool permissionGranted = false;

                    if (_telegramAuthService != null)
                    {
                        try
                        {
                            // إرسال طلب الإذن
                            var deviceInfo = $"{Environment.MachineName} - {Environment.UserName}";
                            var requestSent = await _telegramAuthService.RequestPermissionAsync(username, deviceInfo);

                            if (requestSent)
                            {
                                ShowStatus("تم إرسال طلب الإذن. انتظار الموافقة...", StatusType.Warning);

                                // انتظار الموافقة لمدة 5 دقائق
                                permissionGranted = await _telegramAuthService.WaitForApprovalAsync(5);
                            }
                            else
                            {
                                ShowStatus("فشل في إرسال طلب الإذن عبر التليجرام", StatusType.Error);
                                return;
                            }
                        }
                        catch (Exception ex)
                        {
                            Log.Error(ex, "خطأ في خدمة التليجرام");
                            ShowStatus("خطأ في الاتصال بخدمة التليجرام", StatusType.Error);
                            return;
                        }
                    }
                    else
                    {
                        // محاكاة للتطوير - إظهار مربع حوار محلي
                        ShowStatus("محاكاة طلب الإذن (وضع التطوير)...", StatusType.Info);
                        await Task.Delay(2000);

                        var result = MessageBox.Show(
                            "محاكاة طلب إذن التليجرام\n\nهل تريد الموافقة على دخول المستخدم؟\n\nالمستخدم: " + username,
                            "محاكاة طلب إذن التليجرام",
                            MessageBoxButton.YesNo,
                            MessageBoxImage.Question,
                            MessageBoxResult.No,
                            MessageBoxOptions.RtlReading | MessageBoxOptions.RightAlign);

                        permissionGranted = (result == MessageBoxResult.Yes);
                    }

                    if (permissionGranted)
                    {
                        ShowStatus("تم الموافقة على الدخول من التليجرام", StatusType.Success);
                        await Task.Delay(1000);

                        try
                        {
                            // فتح النافذة الرئيسية
                            Log.Information("محاولة فتح النافذة الرئيسية");
                            var mainWindow = new MainWindow();
                            mainWindow.Show();
                            Log.Information("تم فتح النافذة الرئيسية بنجاح");

                            // إغلاق نافذة تسجيل الدخول
                            this.Close();
                            Log.Information("تم إغلاق نافذة تسجيل الدخول");
                        }
                        catch (Exception mainWindowEx)
                        {
                            Log.Error(mainWindowEx, "خطأ في فتح النافذة الرئيسية");
                            ShowStatus("خطأ في فتح النافذة الرئيسية", StatusType.Error);
                        }

                        Log.Information($"تم تسجيل دخول المستخدم: {username}");
                    }
                    else
                    {
                        ShowStatus("تم رفض طلب الدخول من التليجرام", StatusType.Warning);
                        Log.Information("تم رفض طلب الدخول من التليجرام");
                    }
                }
                else
                {
                    ShowStatus($"خطأ - المستخدم: '{username}', كلمة المرور: '{password}'", StatusType.Error);
                    Log.Warning($"فشل في تسجيل الدخول للمستخدم: '{username}' مع كلمة المرور: '{password}'");
                }
            }
            catch (Exception ex)
            {
                ShowStatus("حدث خطأ أثناء تسجيل الدخول", StatusType.Error);
                Log.Error(ex, "خطأ في عملية تسجيل الدخول");
            }
            finally
            {
                ShowLoading(false);
            }
        }

        private void ShowLoading(bool isLoading)
        {
            LoadingProgressBar.Visibility = isLoading ? Visibility.Visible : Visibility.Collapsed;
            LoginButton.IsEnabled = !isLoading;
            PasswordBox.IsEnabled = !isLoading;
        }

        private void ShowStatus(string message, StatusType type)
        {
            StatusTextBlock.Text = message;
            StatusTextBlock.Visibility = Visibility.Visible;

            switch (type)
            {
                case StatusType.Success:
                    StatusTextBlock.Foreground = new SolidColorBrush(Colors.LightGreen);
                    break;
                case StatusType.Error:
                    StatusTextBlock.Foreground = new SolidColorBrush(Colors.LightCoral);
                    break;
                case StatusType.Warning:
                    StatusTextBlock.Foreground = new SolidColorBrush(Colors.Orange);
                    break;
                case StatusType.Info:
                    StatusTextBlock.Foreground = new SolidColorBrush(Colors.LightBlue);
                    break;
            }

            // إخفاء الرسالة بعد 5 ثوانٍ (إلا إذا كانت رسالة انتظار)
            if (type != StatusType.Warning)
            {
                Task.Delay(5000).ContinueWith(_ =>
                {
                    Dispatcher.Invoke(() =>
                    {
                        StatusTextBlock.Visibility = Visibility.Collapsed;
                    });
                });
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Application.Current.Shutdown();
        }

        private void RedThemeButton_Click(object sender, RoutedEventArgs e)
        {
            ApplyRedTheme();
            Log.Information("تم تطبيق الثيم الأحمر");
        }

        private void GreenThemeButton_Click(object sender, RoutedEventArgs e)
        {
            ApplyGreenTheme();
            Log.Information("تم تطبيق الثيم الأخضر");
        }

        private void DarkThemeButton_Click(object sender, RoutedEventArgs e)
        {
            ApplyDarkTheme();
            Log.Information("تم تطبيق الثيم الداكن");
        }

        private void ApplyRedTheme()
        {
            this.Resources["PrimaryBrush"] = new SolidColorBrush(Color.FromRgb(211, 47, 47));
            this.Resources["SecondaryBrush"] = new SolidColorBrush(Color.FromRgb(244, 67, 54));
            this.Resources["AccentBrush"] = new SolidColorBrush(Color.FromRgb(255, 235, 238));
            this.Resources["InfoBrush"] = new SolidColorBrush(Color.FromRgb(255, 152, 0));
        }

        private void ApplyGreenTheme()
        {
            this.Resources["PrimaryBrush"] = new SolidColorBrush(Color.FromRgb(46, 139, 87));
            this.Resources["SecondaryBrush"] = new SolidColorBrush(Color.FromRgb(76, 175, 80));
            this.Resources["AccentBrush"] = new SolidColorBrush(Color.FromRgb(232, 245, 233));
            this.Resources["InfoBrush"] = new SolidColorBrush(Color.FromRgb(0, 150, 136));
        }

        private void ApplyDarkTheme()
        {
            this.Resources["PrimaryBrush"] = new SolidColorBrush(Color.FromRgb(44, 44, 44));
            this.Resources["SecondaryBrush"] = new SolidColorBrush(Color.FromRgb(66, 66, 66));
            this.Resources["AccentBrush"] = new SolidColorBrush(Color.FromRgb(33, 33, 33));
            this.Resources["InfoBrush"] = new SolidColorBrush(Color.FromRgb(100, 181, 246));
        }

        // تأثيرات بصرية عند تحريك الماوس
        private void LoginButton_MouseEnter(object sender, MouseEventArgs e)
        {
            LoginButton.Opacity = 0.9;
        }

        private void LoginButton_MouseLeave(object sender, MouseEventArgs e)
        {
            LoginButton.Opacity = 1.0;
        }

        protected override void OnSourceInitialized(EventArgs e)
        {
            base.OnSourceInitialized(e);

            // إزالة أيقونة النافذة من شريط المهام
            this.ShowInTaskbar = true;
        }
    }

    public enum StatusType
    {
        Success,
        Error,
        Warning,
        Info
    }
}
