{"format": 1, "restore": {"D:\\mr\\qqqq\\ExamBuilder.Core\\ExamBuilder.Core.csproj": {}}, "projects": {"D:\\mr\\qqqq\\ExamBuilder.Core\\ExamBuilder.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\mr\\qqqq\\ExamBuilder.Core\\ExamBuilder.Core.csproj", "projectName": "ExamBuilder.Core", "projectPath": "D:\\mr\\qqqq\\ExamBuilder.Core\\ExamBuilder.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\mr\\qqqq\\ExamBuilder.Core\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[12.0.1, )"}, "FluentValidation": {"target": "Package", "version": "[11.8.1, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Serilog": {"target": "Package", "version": "[3.1.1, )"}, "System.ComponentModel.Annotations": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.410/PortableRuntimeIdentifierGraph.json"}}}}}