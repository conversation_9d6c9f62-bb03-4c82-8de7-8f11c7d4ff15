{"format": 1, "restore": {"D:\\mr\\qqqq\\csharp\\WebApplication1\\WebApplication1.csproj": {}}, "projects": {"D:\\mr\\qqqq\\csharp\\WebApplication1\\WebApplication1.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\mr\\qqqq\\csharp\\WebApplication1\\WebApplication1.csproj", "projectName": "WebApplication1", "projectPath": "D:\\mr\\qqqq\\csharp\\WebApplication1\\WebApplication1.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\mr\\qqqq\\csharp\\WebApplication1\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.DotNet.ILCompiler": {"suppressParent": "All", "target": "Package", "version": "[8.0.16, )", "autoReferenced": true}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[8.0.16, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "runtime.win-x64.Microsoft.DotNet.ILCompiler", "version": "[8.0.16, 8.0.16]"}], "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.410/PortableRuntimeIdentifierGraph.json"}}}}}