@echo off
echo ========================================
echo    Exam Builder - Updated Version
echo ========================================
echo.

echo Building project...
dotnet build --configuration Release

if %errorlevel% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo Copying settings...
copy ExamBuilder.UI\appsettings.json ExamBuilder.UI\bin\Release\net8.0-windows\ >nul

echo.
echo Starting application...
echo.
echo Login credentials:
echo Username: جو
echo Password: جو
echo.
echo Features:
echo - Larger login window
echo - Working theme buttons
echo - Simplified login process
echo.

cd ExamBuilder.UI\bin\Release\net8.0-windows
start ExamBuilder.UI.exe

echo.
echo Application launched successfully!
echo You can now:
echo 1. Try different themes (Red, Green, Dark)
echo 2. Login with the credentials above
echo 3. Explore the main interface
echo.
pause
