@echo off
echo ========================================
echo    Exam Builder - Yousse<PERSON>
echo ========================================
echo.

echo Checking .NET...
dotnet --version > nul 2>&1
if errorlevel 1 (
    echo Error: .NET 8.0 not found
    echo Please install .NET 8.0 SDK
    pause
    exit /b 1
)

echo Found .NET: 
dotnet --version

echo.
echo Restoring packages...
dotnet restore

echo.
echo Building project...
dotnet build --configuration Release

echo.
echo Copying settings...
copy ExamBuilder.UI\appsettings.json ExamBuilder.UI\bin\Release\net8.0-windows\ > nul

echo.
echo Starting application...
echo.
echo Login credentials:
echo Username: جو
echo Password: جو
echo.

cd ExamBuilder.UI\bin\Release\net8.0-windows
start dotnet ExamBuilder.UI.dll

echo.
echo Application started successfully!
pause
