using System.ComponentModel.DataAnnotations;

namespace ExamBuilder.Core.Models
{
    /// <summary>
    /// نموذج السؤال
    /// </summary>
    public class Question
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "نص السؤال مطلوب")]
        public string Text { get; set; } = string.Empty;

        public QuestionType Type { get; set; } = QuestionType.MultipleChoice;

        [Range(0.1, 100, ErrorMessage = "درجة السؤال يجب أن تكون بين 0.1 و 100")]
        public decimal Points { get; set; } = 1;

        public int Order { get; set; }

        public string? ImagePath { get; set; }

        public string? Explanation { get; set; }

        [Required(ErrorMessage = "مستوى الصعوبة مطلوب")]
        public DifficultyLevel Difficulty { get; set; } = DifficultyLevel.Medium;

        public string? Category { get; set; }

        public string? Tags { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }

        // العلاقات
        public int ExamId { get; set; }
        public virtual Exam Exam { get; set; } = null!;

        public virtual ICollection<QuestionOption> Options { get; set; } = new List<QuestionOption>();
    }

    /// <summary>
    /// خيارات السؤال (للأسئلة متعددة الخيارات)
    /// </summary>
    public class QuestionOption
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "نص الخيار مطلوب")]
        public string Text { get; set; } = string.Empty;

        public bool IsCorrect { get; set; } = false;

        public int Order { get; set; }

        public string? ImagePath { get; set; }

        // العلاقات
        public int QuestionId { get; set; }
        public virtual Question Question { get; set; } = null!;
    }

    /// <summary>
    /// أنواع الأسئلة
    /// </summary>
    public enum QuestionType
    {
        [Display(Name = "اختيار من متعدد")]
        MultipleChoice = 0,

        [Display(Name = "صح أم خطأ")]
        TrueFalse = 1,

        [Display(Name = "إجابة قصيرة")]
        ShortAnswer = 2,

        [Display(Name = "مقال")]
        Essay = 3,

        [Display(Name = "ملء الفراغات")]
        FillInTheBlanks = 4,

        [Display(Name = "مطابقة")]
        Matching = 5,

        [Display(Name = "ترتيب")]
        Ordering = 6,

        [Display(Name = "رقمي")]
        Numerical = 7
    }

    /// <summary>
    /// مستويات الصعوبة
    /// </summary>
    public enum DifficultyLevel
    {
        [Display(Name = "سهل")]
        Easy = 1,

        [Display(Name = "متوسط")]
        Medium = 2,

        [Display(Name = "صعب")]
        Hard = 3,

        [Display(Name = "صعب جداً")]
        VeryHard = 4
    }

    /// <summary>
    /// فئات الأسئلة
    /// </summary>
    public static class QuestionCategories
    {
        public static readonly string[] DefaultCategories = {
            "رياضيات",
            "علوم",
            "لغة عربية",
            "لغة إنجليزية",
            "تاريخ",
            "جغرافيا",
            "فيزياء",
            "كيمياء",
            "أحياء",
            "حاسوب",
            "تربية إسلامية",
            "تربية وطنية",
            "فنون",
            "رياضة",
            "أخرى"
        };
    }

    /// <summary>
    /// إحصائيات السؤال
    /// </summary>
    public class QuestionStatistics
    {
        public int QuestionId { get; set; }
        public int TimesUsed { get; set; }
        public double AverageScore { get; set; }
        public double DifficultyIndex { get; set; }
        public double DiscriminationIndex { get; set; }
        public DateTime LastUsed { get; set; }
    }
}
