<!DOCTYPE html>
<!-- saved from url=(0132)blob:https://3a9zg5464bezvt5j1up29eeli3rxdro8u0f56106nzt5y0i3w0-h775241406.scf.usercontent.goog/50cb51ea-a796-4f04-9ca7-909c27caf86c -->
<html lang="ar" dir="rtl"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><script>(function(firebaseConfig, initialAuthToken, appId) {
        window.__firebase_config = firebaseConfig;
        window.__initial_auth_token = initialAuthToken;
        window.__app_id = appId;
            })("\n{\n  \"apiKey\": \"AIzaSyCqyCcs2R2e7AegGjvFAwG98wlamtbHvZY\",\n  \"authDomain\": \"bard-frontend.firebaseapp.com\",\n  \"projectId\": \"bard-frontend\",\n  \"storageBucket\": \"bard-frontend.firebasestorage.app\",\n  \"messagingSenderId\": \"175205271074\",\n  \"appId\": \"1:175205271074:web:2b7bd4d34d33bf38e6ec7b\"\n}\n","eyJhbGciOiJSUzI1NiIsImtpZCI6IjAyNzI1NzgwMjQzZmMwYTYwOGExZjA3MzFiZDAzNmVjODZkYmI4MDciLCJ0eXAiOiJKV1QifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.maC7I3080-WGF7Ok3erf66Xq4SKW-LXGUDYac_UXb85xl0fzAvz8G9ozpiZP_ImQ_l9uFy8jAFAp0euZzgL_tsFCTECAy1feNTlF7S4yP1SueTCTtxKfIKUadL0QR9UqFBilNnVDLuMIHYlT3E40LJsh59njKRObIQdvlQ5KBEPHNdX8jhWI3-5ZOaatAX2HCd53tDdpv_E5H4MKTjaMWXfTRDBKI6QBWhSR81qR35nzkNICUBqukY9ltOC_N2IQvqVKz2h8YUPvgXTFYx9lPUkY1WtA4KNCxUTjfjWrb6us5fB9D2guC-pCt6D599LRwTR81IiSDUctgKyJ8ZOASg","c_4fdf05178a45ab1b_video_qr_system_ar-824")</script><script>(function() {
  // Ensure this script is executed only once
  if (window.firebaseAuthBridgeScriptLoaded) {
    return;
  }
  window.firebaseAuthBridgeScriptLoaded = true;

  let nextTokenPromiseId = 0;

  // Stores { resolve, reject } for ongoing token requests
  const pendingTokenPromises = {};

  // Listen for messages from the Host Application
  window.addEventListener('message', function(event) {

    const messageData = event.data;

  if (messageData && messageData.type === 'RESOLVE_NEW_FIREBASE_TOKEN') {
      const { success, token, error, promiseId } = messageData ?? {};
      if (pendingTokenPromises[promiseId]) {
        if (success) {
          pendingTokenPromises[promiseId].resolve(token);
        } else {
          pendingTokenPromises[promiseId].reject(new Error(error || 'Token refresh failed from host.'));
        }
        delete pendingTokenPromises[promiseId];
      }
    }
  });

  // Expose a function for the Generated App to request a new Firebase token
  window.requestNewFirebaseToken = function() {
    const currentPromiseId = nextTokenPromiseId++;
    const promise = new Promise((resolve, reject) => {
      pendingTokenPromises[currentPromiseId] = { resolve, reject };
    });
    if (window.parent && window.parent !== window) {
      window.parent.postMessage({
        type: 'REQUEST_NEW_FIREBASE_TOKEN',
        promiseId: currentPromiseId
      }, '*');
    } else {
      pendingTokenPromises[currentPromiseId].reject(new Error('No parent window to request token from.'));
      delete pendingTokenPromises[currentPromiseId];
    }
    return promise;
  };
})();</script><script>let realOriginalGetUserMedia = null;
if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
  realOriginalGetUserMedia = navigator.mediaDevices.getUserMedia.bind(navigator.mediaDevices);
}

(function() {
  if (navigator.mediaDevices && navigator.mediaDevices.__proto__) {
    try {
      Object.defineProperty(navigator.mediaDevices.__proto__, 'getUserMedia', {
        get: function() {
          return undefined; // Or throw an error
        },
        configurable: false
      });
    } catch (error) {
      console.error("Error defining prototype getter:", error);
    }
  }
})();

(function() {
  let originalGetUserMedia = realOriginalGetUserMedia;

  const pendingMediaResolvers = {};
  let nextMediaPromiseId = 0;

  function interceptGetUserMedia() {
    if (navigator.mediaDevices) {
      Object.defineProperty(navigator.mediaDevices, 'getUserMedia', {
        value: function(constraints) {
          const mediaPromiseId = nextMediaPromiseId++;
          const promise = new Promise((resolve, reject) => {
            pendingMediaResolvers[mediaPromiseId] = (granted) => {
              delete pendingMediaResolvers[mediaPromiseId];
              if (granted) {
                if (originalGetUserMedia) {
                  originalGetUserMedia(constraints).then(resolve).catch(reject);
                } else {
                  reject(new Error("Original getUserMedia not available."));
                }
              } else {
              reject(new DOMException('Permission denied', 'NotAllowedError'));
            }
          };
        });

        window.parent.postMessage({
          type: 'requestMediaPermission',
          constraints: constraints,
          promiseId: mediaPromiseId,
        }, '*');

        return promise;
      },
      writable: false,
      configurable: false
    });
    }
  }

  interceptGetUserMedia();

  const observer = new MutationObserver(function(mutationsList, observer) {
    for (const mutation of mutationsList) {
      if (mutation.type === 'reconfigured' && mutation.name === 'getUserMedia' && mutation.object === navigator.mediaDevices) {
        interceptGetUserMedia();
      } else if (mutation.type === 'attributes' && mutation.attributeName === 'getUserMedia' && mutation.target === navigator.mediaDevices) {
        interceptGetUserMedia();
      } else if (mutation.type === 'childList' && mutation.addedNodes) {
        mutation.addedNodes.forEach(node => {
          if (node === navigator.mediaDevices) {
            interceptGetUserMedia();
          }
        });
      }
    }
  });

  window.addEventListener('message', function(event) {
    if (event.data) {
      if (event.data.type === 'resolveMediaPermission') {
        const { promiseId, granted } = event.data;
        if (pendingMediaResolvers[promiseId]) {
          pendingMediaResolvers[promiseId](granted);
        }
      }
    }
  });

})();</script><script>((function(modelInformation) {
  const originalFetch = window.fetch;
  // TODO: b/421908508 - Move these out of the script and match all generative AI model calls.
  let googleLlmBaseApiUrls = [
    'https://generativelanguage.googleapis.com/v1beta/models/' + modelInformation.textModelName + ':streamGenerateContent',
    'https://generativelanguage.googleapis.com/v1beta/models/' + modelInformation.textModelName + ':generateContent',
    'https://generativelanguage.googleapis.com/v1beta/models/' + modelInformation.imageModelName + ':predict',
    'https://generativelanguage.googleapis.com/v1beta/models/' + modelInformation.imageModelName + ':predictLongRunning',
    'https://generativelanguage.googleapis.com/v1beta/models/' + modelInformation.videoModelName + ':predict',
    'https://generativelanguage.googleapis.com/v1beta/models/' + modelInformation.videoModelName + ':predictLongRunning',
  ];
  modelInformation.deprecatedTextModelNames.forEach((modelName) => {
    googleLlmBaseApiUrls.push(
      'https://generativelanguage.googleapis.com/v1beta/models/' + modelName + ':streamGenerateContent',
      'https://generativelanguage.googleapis.com/v1beta/models/' + modelName + ':generateContent',
    );
  });

  const pendingFetchResolvers = {};
  let nextPromiseId = 0;

  function handleStringInput(input, optionsArgument) {
    const actualUrl = input;
    const fetchCallArgs = [actualUrl, optionsArgument];
    const effectiveOptions = optionsArgument || {};
    const bodyForApiKeyCheck = effectiveOptions.body;
    const bodyForPostMessage = effectiveOptions.body;
    return { actualUrl, fetchCallArgs, effectiveOptions, bodyForApiKeyCheck, bodyForPostMessage };
  }

  function handleRequestInput(input, optionsArgument) {
    const actualUrl = input.url;
    const fetchCallArgs = [input, optionsArgument];
    const effectiveOptions = { method: input.method, headers: new Headers(input.headers) };
    let bodyForApiKeyCheck;
    let bodyForPostMessage;

    if (optionsArgument) {
      if (optionsArgument.method) effectiveOptions.method = optionsArgument.method;
      if (optionsArgument.headers) effectiveOptions.headers = new Headers(optionsArgument.headers);
      if ('body' in optionsArgument) {
        bodyForApiKeyCheck = optionsArgument.body;
        bodyForPostMessage = optionsArgument.body;
      } else {
        bodyForApiKeyCheck = undefined;
        bodyForPostMessage = input.body;
      }
    } else {
      bodyForApiKeyCheck = undefined;
      bodyForPostMessage = input.body;
    }
    return { actualUrl, fetchCallArgs, effectiveOptions, bodyForApiKeyCheck, bodyForPostMessage };
  }

  window.fetch = function(input, optionsArgument) {
    let actualUrl;
    let fetchCallArgs;
    let effectiveOptions = {};
    let bodyForApiKeyCheck;
    let bodyForPostMessage;

    if (typeof input === 'string') {
      ({actualUrl, fetchCallArgs, effectiveOptions, bodyForApiKeyCheck, bodyForPostMessage} = handleStringInput(input, optionsArgument));
    } else if (input instanceof Request) {
      ({actualUrl, fetchCallArgs, effectiveOptions, bodyForApiKeyCheck, bodyForPostMessage} = handleRequestInput(input, optionsArgument));
    } else {
      return originalFetch.apply(window, [input, optionsArgument]);
    }

    effectiveOptions.method = effectiveOptions.method || 'GET';
    if (!effectiveOptions.headers) {
      effectiveOptions.headers = new Headers();
    }


    if (typeof actualUrl === 'string' && googleLlmBaseApiUrls.some((url) => actualUrl.startsWith(url))) {
      let apiKeyIsNull = true;

      const regex = new RegExp("models/([^:]+)");
      const modelNameMatch = actualUrl.match(regex);
      const modelName = modelNameMatch ? modelNameMatch[1] : 'unspecified';


      try {
        const urlObject = new URL(actualUrl);  // Use URL object for robust parsing
        const apiKeyParam = urlObject.searchParams.get('key');
        if (apiKeyParam) {
          apiKeyIsNull = false;
        }
      } catch (e) {
        // Continue checks even if URL parsing fails
      }

      if (apiKeyIsNull && effectiveOptions.headers) {
        const h = new Headers(effectiveOptions.headers);
        const apiKeyHeaderValue = h.get('X-API-Key') || h.get('x-api-key');
        if (apiKeyHeaderValue) {
          apiKeyIsNull = false;
          return originalFetch.apply(window, fetchCallArgs);
        }
      }

      if (apiKeyIsNull && effectiveOptions.method && ['POST', 'PUT', 'PATCH'].includes(effectiveOptions.method.toUpperCase()) && typeof bodyForApiKeyCheck === 'string') {
        try {
          const bodyData = JSON.parse(bodyForApiKeyCheck);
          if (bodyData && bodyData.apiKey) {
            apiKeyIsNull = false;
            return originalFetch.apply(window, fetchCallArgs);
          }
        } catch (e) {
          // Ignore JSON parsing errors
        }
      }

      if(apiKeyIsNull) {
        const promiseId = nextPromiseId++;
        const promise = new Promise((resolve) => {
          pendingFetchResolvers[promiseId] = (resolvedResponse) => {
            delete pendingFetchResolvers[promiseId];
            resolve(resolvedResponse);
          };
        });

        let serializedBodyForPostMessage;
        if (typeof bodyForPostMessage === 'string' || bodyForPostMessage == null) {
            serializedBodyForPostMessage = bodyForPostMessage;
        } else if (bodyForPostMessage instanceof ReadableStream) {
            serializedBodyForPostMessage = null;
        } else {
            try {
                serializedBodyForPostMessage = JSON.stringify(bodyForPostMessage);
            } catch (e) {
                serializedBodyForPostMessage = null;
            }
        }

        const messageOptions = {
            method: effectiveOptions.method,
            headers: Object.fromEntries(new Headers(effectiveOptions.headers).entries()),
            body: serializedBodyForPostMessage
        };

        window.parent.postMessage({
          type: 'requestFetch',
          url: actualUrl,
          modelName: modelName,
          options: messageOptions,
          promiseId: promiseId,
        }, '*');

        return promise;
      }
      return originalFetch.apply(window, fetchCallArgs);
    }
    return originalFetch.apply(window, fetchCallArgs);
  };

  window.addEventListener('message', function(event) {
    if (event.data && event.data.type === 'resolveFetch') {
      const { promiseId, response } = event.data;
      if (pendingFetchResolvers[promiseId]) {
        try {
          const reconstructedResponse = new Response(response.body, {
            status: response.status,
            statusText: response.statusText,
            headers: new Headers(response.headers),
          });
          pendingFetchResolvers[promiseId](reconstructedResponse);
        } catch (error) {
          pendingFetchResolvers[promiseId](new Response(null, { status: 500, statusText: "Interceptor Response Reconstruction Error" }));
        }
      }
    }
  });

}))({"textModelName":"gemini-2.5-flash-preview-04-17","imageModelName":"imagen-3.0-generate-002","videoModelName":"veo-2.0-generate-001","deprecatedTextModelNames":["gemini-2.0-flash"]})</script><script>(function() {
  const originalConsoleLog = console.log;
  const originalConsoleError = console.error;

    /**
   * Normalizes an error event or a promise rejection reason into a structured error object.
   * @param {*} errorEventOrReason The error object or reason.
   * @return {object} Structured error data { message, name, stack }.
   */
  function getErrorObject(errorEventOrReason) {
    if (errorEventOrReason instanceof Error) {
      return {
        message: errorEventOrReason.message,
        name: errorEventOrReason.name,
        stack: errorEventOrReason.stack,
      };
    }
    // Fallback for non-Error objects.
    try {
      return {
        message: JSON.stringify(errorEventOrReason),
        name: 'UnknownErrorType',
        stack: null,
      };
    } catch (e) {
      return {
        message: String(errorEventOrReason),
        name: 'UnknownErrorTypeNonStringifiable',
        stack: null,
      };
    }
  }

  /**
   * Converts an array of arguments (from log/error) into a single string.
   * Handles Error objects specially to include their message and stack.
   * @param {Array<*>} args - Arguments passed to console methods.
   * @return {string} A string representation of the arguments.
   */
  function stringifyArgs(args) {
    return args
      .map((arg) => {
        if (arg instanceof Error) {
          const {message, stack} = arg;
          return `Error: ${message}${stack ? ('\nStack: ' + stack) : ''}`;
        }
        if (typeof arg === 'object' && arg !== null) {
          try {
            return JSON.stringify(arg);
          } catch (error) {
            return '[Circular Object]';
          }
        } else {
          return String(arg);
        }
      })
      .join(' ');
  }

  console.log = function(...args) {
    const logString = stringifyArgs(args);
    window.parent.postMessage({ type: 'log', message: logString }, '*');
    originalConsoleLog.apply(console, args);
  };

  console.error = function(...args) {
    let errorData;
    if (args.length > 0 && args[0] instanceof Error) {
      const err = args[0];
      // If the first arg is an Error, capture its details.
      errorData = {
        type: 'error',
        source: 'CONSOLE_ERROR',
        ...getErrorObject(err),
        rawArgsString: stringifyArgs(args.slice(1)),
        timestamp: new Date().toISOString(),
      };
    } else {
      // If not an Error object, treat all args as a general error message.
      errorData = {
        type: 'error',
        source: 'CONSOLE_ERROR',
        message: stringifyArgs(args),
        name: 'ConsoleLoggedError',
        stack: null,
        timestamp: new Date().toISOString(),
      };
    }
    window.parent.postMessage(errorData, '*');
    originalConsoleError.apply(console, args);
  };

  // Listen for global unhandled synchronous errors.
  window.addEventListener('error', function(event) {
    const errorDetails = event.error ? getErrorObject(event.error) : {
      message: event.message,
      name: 'GlobalError',
      stack: null,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
    };

    window.parent.postMessage({
      type: 'error',
      source: 'global',
      ...errorDetails,
      message: errorDetails.message || event.message,
      timestamp: new Date().toISOString(),
    }, '*');
  });

  // Listen for unhandled promise rejections (asynchronous errors).
  window.addEventListener('unhandledrejection', function(event) {
    const errorDetails = getErrorObject(event.reason);

    window.parent.postMessage({
      type: 'error',
      source: 'unhandledrejection',
      ...errorDetails,
      message: errorDetails.message || 'Unhandled Promise Rejection',
      timestamp: new Date().toISOString(),
    }, '*');
  });

})();</script>
    
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام تأمين الفيديوهات التعليمية</title>
    
    <!-- Tailwind CSS for styling -->
    <script src="https://cdn.tailwindcss.com/"></script>
    
    <!-- Google Fonts: Cairo for Arabic UI -->
    <link rel="preconnect" href="https://fonts.googleapis.com/">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&amp;display=swap" rel="stylesheet">
    
    <!-- QR Code generation library -->
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js" crossorigin="anonymous"></script>

    <style>
        body {
            font-family: 'Cairo', sans-serif;
        }
        .hidden-view {
            display: none !important;
        }
        #video-player::-webkit-media-controls-enclosure {
            overflow: hidden;
        }
        #video-player::-webkit-media-controls-panel {
            width: calc(100% + 30px);
        }
        .progress-bar {
            transition: width 0.3s ease-in-out;
        }
    </style>
<style>*, ::before, ::after{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgb(59 130 246 / 0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }::backdrop{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgb(59 130 246 / 0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }/* ! tailwindcss v3.4.16 | MIT License | https://tailwindcss.com */*,::after,::before{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}::after,::before{--tw-content:''}:host,html{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;tab-size:4;font-family:ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";font-feature-settings:normal;font-variation-settings:normal;-webkit-tap-highlight-color:transparent}body{margin:0;line-height:inherit}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,pre,samp{font-family:ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;font-feature-settings:normal;font-variation-settings:normal;font-size:1em}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}button,input,optgroup,select,textarea{font-family:inherit;font-feature-settings:inherit;font-variation-settings:inherit;font-size:100%;font-weight:inherit;line-height:inherit;letter-spacing:inherit;color:inherit;margin:0;padding:0}button,select{text-transform:none}button,input:where([type=button]),input:where([type=reset]),input:where([type=submit]){-webkit-appearance:button;background-color:transparent;background-image:none}:-moz-focusring{outline:auto}:-moz-ui-invalid{box-shadow:none}progress{vertical-align:baseline}::-webkit-inner-spin-button,::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}summary{display:list-item}blockquote,dd,dl,figure,h1,h2,h3,h4,h5,h6,hr,p,pre{margin:0}fieldset{margin:0;padding:0}legend{padding:0}menu,ol,ul{list-style:none;margin:0;padding:0}dialog{padding:0}textarea{resize:vertical}input::placeholder,textarea::placeholder{opacity:1;color:#9ca3af}[role=button],button{cursor:pointer}:disabled{cursor:default}audio,canvas,embed,iframe,img,object,svg,video{display:block;vertical-align:middle}img,video{max-width:100%;height:auto}[hidden]:where(:not([hidden=until-found])){display:none}.container{width:100%}@media (min-width: 640px){.container{max-width:640px}}@media (min-width: 768px){.container{max-width:768px}}@media (min-width: 1024px){.container{max-width:1024px}}@media (min-width: 1280px){.container{max-width:1280px}}@media (min-width: 1536px){.container{max-width:1536px}}.fixed{position:fixed}.absolute{position:absolute}.relative{position:relative}.inset-0{inset:0px}.left-4{left:1rem}.right-5{right:1.25rem}.top-4{top:1rem}.top-5{top:1.25rem}.z-50{z-index:50}.mx-auto{margin-left:auto;margin-right:auto}.mb-2{margin-bottom:0.5rem}.mb-4{margin-bottom:1rem}.mb-6{margin-bottom:1.5rem}.mt-1{margin-top:0.25rem}.mt-10{margin-top:2.5rem}.mt-2{margin-top:0.5rem}.mt-3{margin-top:0.75rem}.mt-4{margin-top:1rem}.mt-6{margin-top:1.5rem}.mt-8{margin-top:2rem}.block{display:block}.inline-block{display:inline-block}.flex{display:flex}.h-10{height:2.5rem}.h-12{height:3rem}.h-16{height:4rem}.h-2\.5{height:0.625rem}.h-5{height:1.25rem}.h-6{height:1.5rem}.h-full{height:100%}.w-10{width:2.5rem}.w-12{width:3rem}.w-16{width:4rem}.w-5{width:1.25rem}.w-6{width:1.5rem}.w-72{width:18rem}.w-auto{width:auto}.w-full{width:100%}.max-w-4xl{max-width:56rem}.max-w-md{max-width:28rem}.transform{transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}@keyframes spin{to{transform:rotate(360deg)}}.animate-spin{animation:spin 1s linear infinite}.cursor-pointer{cursor:pointer}.items-center{align-items:center}.justify-center{justify-content:center}.space-x-2 > :not([hidden]) ~ :not([hidden]){--tw-space-x-reverse:0;margin-right:calc(0.5rem * var(--tw-space-x-reverse));margin-left:calc(0.5rem * calc(1 - var(--tw-space-x-reverse)))}.space-x-4 > :not([hidden]) ~ :not([hidden]){--tw-space-x-reverse:0;margin-right:calc(1rem * var(--tw-space-x-reverse));margin-left:calc(1rem * calc(1 - var(--tw-space-x-reverse)))}.space-y-10 > :not([hidden]) ~ :not([hidden]){--tw-space-y-reverse:0;margin-top:calc(2.5rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(2.5rem * var(--tw-space-y-reverse))}.space-y-3 > :not([hidden]) ~ :not([hidden]){--tw-space-y-reverse:0;margin-top:calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(0.75rem * var(--tw-space-y-reverse))}.space-y-4 > :not([hidden]) ~ :not([hidden]){--tw-space-y-reverse:0;margin-top:calc(1rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(1rem * var(--tw-space-y-reverse))}.space-x-reverse > :not([hidden]) ~ :not([hidden]){--tw-space-x-reverse:1}.overflow-hidden{overflow:hidden}.overflow-y-auto{overflow-y:auto}.rounded-2xl{border-radius:1rem}.rounded-full{border-radius:9999px}.rounded-lg{border-radius:0.5rem}.rounded-md{border-radius:0.375rem}.rounded-r-lg{border-top-right-radius:0.5rem;border-bottom-right-radius:0.5rem}.border{border-width:1px}.border-l-4{border-left-width:4px}.border-gray-300{--tw-border-opacity:1;border-color:rgb(209 213 219 / var(--tw-border-opacity, 1))}.border-indigo-500{--tw-border-opacity:1;border-color:rgb(99 102 241 / var(--tw-border-opacity, 1))}.bg-black{--tw-bg-opacity:1;background-color:rgb(0 0 0 / var(--tw-bg-opacity, 1))}.bg-gray-100{--tw-bg-opacity:1;background-color:rgb(243 244 246 / var(--tw-bg-opacity, 1))}.bg-gray-200{--tw-bg-opacity:1;background-color:rgb(229 231 235 / var(--tw-bg-opacity, 1))}.bg-gray-50{--tw-bg-opacity:1;background-color:rgb(249 250 251 / var(--tw-bg-opacity, 1))}.bg-gray-600{--tw-bg-opacity:1;background-color:rgb(75 85 99 / var(--tw-bg-opacity, 1))}.bg-indigo-50{--tw-bg-opacity:1;background-color:rgb(238 242 255 / var(--tw-bg-opacity, 1))}.bg-indigo-600{--tw-bg-opacity:1;background-color:rgb(79 70 229 / var(--tw-bg-opacity, 1))}.bg-red-100{--tw-bg-opacity:1;background-color:rgb(254 226 226 / var(--tw-bg-opacity, 1))}.bg-red-500{--tw-bg-opacity:1;background-color:rgb(239 68 68 / var(--tw-bg-opacity, 1))}.bg-red-600{--tw-bg-opacity:1;background-color:rgb(220 38 38 / var(--tw-bg-opacity, 1))}.bg-white{--tw-bg-opacity:1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1))}.bg-opacity-50{--tw-bg-opacity:0.5}.p-2\.5{padding:0.625rem}.p-4{padding:1rem}.p-5{padding:1.25rem}.p-6{padding:1.5rem}.p-8{padding:2rem}.px-4{padding-left:1rem;padding-right:1rem}.px-5{padding-left:1.25rem;padding-right:1.25rem}.px-7{padding-left:1.75rem;padding-right:1.75rem}.py-2{padding-top:0.5rem;padding-bottom:0.5rem}.py-3{padding-top:0.75rem;padding-bottom:0.75rem}.text-center{text-align:center}.text-2xl{font-size:1.5rem;line-height:2rem}.text-base{font-size:1rem;line-height:1.5rem}.text-lg{font-size:1.125rem;line-height:1.75rem}.text-sm{font-size:0.875rem;line-height:1.25rem}.text-xl{font-size:1.25rem;line-height:1.75rem}.text-xs{font-size:0.75rem;line-height:1rem}.font-bold{font-weight:700}.font-medium{font-weight:500}.leading-6{line-height:1.5rem}.text-gray-400{--tw-text-opacity:1;color:rgb(156 163 175 / var(--tw-text-opacity, 1))}.text-gray-500{--tw-text-opacity:1;color:rgb(107 114 128 / var(--tw-text-opacity, 1))}.text-gray-600{--tw-text-opacity:1;color:rgb(75 85 99 / var(--tw-text-opacity, 1))}.text-gray-700{--tw-text-opacity:1;color:rgb(55 65 81 / var(--tw-text-opacity, 1))}.text-gray-800{--tw-text-opacity:1;color:rgb(31 41 55 / var(--tw-text-opacity, 1))}.text-gray-900{--tw-text-opacity:1;color:rgb(17 24 39 / var(--tw-text-opacity, 1))}.text-green-600{--tw-text-opacity:1;color:rgb(22 163 74 / var(--tw-text-opacity, 1))}.text-indigo-600{--tw-text-opacity:1;color:rgb(79 70 229 / var(--tw-text-opacity, 1))}.text-red-600{--tw-text-opacity:1;color:rgb(220 38 38 / var(--tw-text-opacity, 1))}.text-white{--tw-text-opacity:1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.text-red-800{--tw-text-opacity:1;color:rgb(153 27 27 / var(--tw-text-opacity, 1))}.opacity-25{opacity:0.25}.opacity-75{opacity:0.75}.shadow-lg{--tw-shadow:0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);--tw-shadow-colored:0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)}.shadow-sm{--tw-shadow:0 1px 2px 0 rgb(0 0 0 / 0.05);--tw-shadow-colored:0 1px 2px 0 var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)}.shadow{--tw-shadow:0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);--tw-shadow-colored:0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)}.transition-transform{transition-property:transform;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms}.hover\:scale-105:hover{--tw-scale-x:1.05;--tw-scale-y:1.05;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.hover\:bg-gray-200:hover{--tw-bg-opacity:1;background-color:rgb(229 231 235 / var(--tw-bg-opacity, 1))}.hover\:bg-gray-300:hover{--tw-bg-opacity:1;background-color:rgb(209 213 219 / var(--tw-bg-opacity, 1))}.hover\:bg-indigo-700:hover{--tw-bg-opacity:1;background-color:rgb(67 56 202 / var(--tw-bg-opacity, 1))}.hover\:bg-red-600:hover{--tw-bg-opacity:1;background-color:rgb(220 38 38 / var(--tw-bg-opacity, 1))}.hover\:bg-red-700:hover{--tw-bg-opacity:1;background-color:rgb(185 28 28 / var(--tw-bg-opacity, 1))}.hover\:text-indigo-600:hover{--tw-text-opacity:1;color:rgb(79 70 229 / var(--tw-text-opacity, 1))}.hover\:underline:hover{-webkit-text-decoration-line:underline;text-decoration-line:underline}.focus\:border-indigo-500:focus{--tw-border-opacity:1;border-color:rgb(99 102 241 / var(--tw-border-opacity, 1))}.focus\:outline-none:focus{outline:2px solid transparent;outline-offset:2px}.focus\:ring-2:focus{--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)}.focus\:ring-4:focus{--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)}.focus\:ring-gray-300:focus{--tw-ring-opacity:1;--tw-ring-color:rgb(209 213 219 / var(--tw-ring-opacity, 1))}.focus\:ring-indigo-300:focus{--tw-ring-opacity:1;--tw-ring-color:rgb(165 180 252 / var(--tw-ring-opacity, 1))}.focus\:ring-indigo-500:focus{--tw-ring-opacity:1;--tw-ring-color:rgb(99 102 241 / var(--tw-ring-opacity, 1))}.focus\:ring-red-300:focus{--tw-ring-opacity:1;--tw-ring-color:rgb(252 165 165 / var(--tw-ring-opacity, 1))}@media (min-width: 768px){.md\:p-6{padding:1.5rem}.md\:p-8{padding:2rem}.md\:text-3xl{font-size:1.875rem;line-height:2.25rem}}</style></head>
<body class="bg-gray-100 text-gray-800">

    <div class="container mx-auto p-4 md:p-8 max-w-4xl">
        
        <!-- Global Alert Message -->
        <div id="alert-container" class="fixed top-5 right-5 z-50 w-72"></div>
        
        <!-- Login View -->
        <div id="login-view" class="hidden-view">
            <div class="bg-white p-8 rounded-2xl shadow-lg max-w-md mx-auto mt-10">
                <h1 class="text-2xl font-bold text-center text-indigo-600 mb-6">تسجيل دخول المعلم</h1>
                <form id="login-form" class="space-y-4">
                    <div>
                        <label for="username" class="block mb-2 text-sm font-medium text-gray-700">اسم المستخدم</label>
                        <input type="text" id="username" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-indigo-500 focus:border-indigo-500 block w-full p-2.5" required="">
                    </div>
                    <div>
                        <label for="password" class="block mb-2 text-sm font-medium text-gray-700">كلمة المرور</label>
                        <input type="password" id="password" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-indigo-500 focus:border-indigo-500 block w-full p-2.5" required="">
                    </div>
                    <button type="submit" class="w-full text-white bg-indigo-600 hover:bg-indigo-700 focus:ring-4 focus:outline-none focus:ring-indigo-300 font-medium rounded-lg text-lg px-5 py-3 text-center">دخول</button>
                </form>
            </div>
        </div>


        <!-- Teacher View: Upload and QR Code Generation -->
        <div id="teacher-view" class="space-y-10">
             <button id="logout-btn" class="absolute top-4 left-4 text-sm text-white bg-red-500 hover:bg-red-600 font-medium rounded-lg px-4 py-2">تسجيل الخروج</button>
            <div class="bg-white p-6 md:p-8 rounded-2xl shadow-lg">
                <h1 class="text-2xl md:text-3xl font-bold text-center text-indigo-600 mb-2">منصة المعلم</h1>
                <p class="text-center text-gray-500 mb-6">ارفع فيديو جديد وقم بتوليد رابط و QR كود خاص به</p>

                <div class="space-y-4">
                    <div>
                        <label for="video-name" class="block mb-2 text-sm font-medium text-gray-700">اسم الفيديو (للتنظيم)</label>
                        <input type="text" id="video-name" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-indigo-500 focus:border-indigo-500 block w-full p-2.5" placeholder="مثال: الدرس الأول في الفيزياء" required="">
                    </div>
                    <div>
                        <label for="video-file" class="block mb-2 text-sm font-medium text-gray-700">اختر ملف الفيديو</label>
                        <input type="file" id="video-file" class="block w-full text-sm text-gray-900 border border-gray-300 rounded-lg cursor-pointer bg-gray-50 focus:outline-none" accept="video/mp4,video/webm,video/ogg" required="">
                        <p class="mt-1 text-xs text-gray-500">الأنواع المسموح بها: MP4, WebM, OGG.</p>
                    </div>
                </div>

                <!-- Upload Progress -->
                <div id="upload-progress-container" class="mt-4 hidden-view">
                    <p id="upload-status-text" class="text-sm text-center text-gray-600 mb-2">جاري رفع الملف... 0%</p>
                    <div class="w-full bg-gray-200 rounded-full h-2.5">
                        <div id="upload-progress-bar" class="bg-indigo-600 h-2.5 rounded-full progress-bar" style="width: 0%"></div>
                    </div>
                </div>

                <button id="generate-btn" class="mt-6 w-full text-white bg-indigo-600 hover:bg-indigo-700 focus:ring-4 focus:outline-none focus:ring-indigo-300 font-medium rounded-lg text-lg px-5 py-3 text-center transition-transform transform hover:scale-105">رفع وتوليد الرابط</button>

                <div id="result-view" class="hidden-view mt-8 p-4 bg-indigo-50 border-l-4 border-indigo-500 rounded-r-lg">
                    <h2 class="text-xl font-bold text-gray-800 mb-4">تم إنشاء الرابط بنجاح!</h2>
                    <p class="mb-4">شارك هذا الرابط أو الـ QR Code مع طلابك. سيتم ربط أول جهاز يقوم بفتحه مع هذا الفيديو.</p>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700">الرابط الخاص:</label>
                        <div class="flex items-center space-x-2 space-x-reverse mt-1">
                            <input id="generated-link" type="text" readonly="" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-2.5 cursor-pointer">
                            <button id="copy-btn" class="p-2.5 text-gray-500 hover:text-indigo-600 bg-gray-100 rounded-lg hover:bg-gray-200">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z"></path><path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z"></path></svg>
                            </button>
                        </div>
                         <span id="copy-feedback" class="text-green-600 text-xs mt-1 hidden-view">تم النسخ!</span>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">QR Code:</label>
                        <div class="mt-2 p-4 bg-white rounded-lg inline-block shadow-sm">
                           <canvas id="qrcode-canvas"></canvas>
                        </div>
                    </div>
                </div>
            </div>

             <!-- Video Management Section -->
            <div id="video-list-container" class="">
                <div class="bg-white p-6 md:p-8 rounded-2xl shadow-lg">
                    <h2 class="text-xl font-bold text-gray-800 mb-4">إدارة الفيديوهات</h2>
                    <div id="video-list-loading" class="text-center text-gray-500 hidden-view">
                        <p>جاري تحميل قائمة الفيديوهات...</p>
                    </div>
                    <div id="video-list" class="space-y-3"></div>
                    <p id="no-videos-message" class="text-center text-gray-500 mt-4">لا توجد فيديوهات مرفوعة حتى الآن.</p>
                </div>
            </div>
        </div>

        <!-- Student View: Video Player -->
        <div id="student-view" class="hidden-view">
             <div id="player-container" class="hidden-view bg-white p-4 md:p-6 rounded-2xl shadow-lg">
                <h1 id="video-title-player" class="text-2xl font-bold text-center mb-4"></h1>
                <div class="aspect-w-16 aspect-h-9 bg-black rounded-lg overflow-hidden">
                    <video id="video-player" class="w-full h-full" controls="" controlslist="nodownload" oncontextmenu="return false;"></video>
                </div>
                <p class="text-xs text-center mt-2 text-gray-400">المشاهدة مقتصرة على هذا الجهاز فقط.</p>
                 <a href="" class="mt-4 inline-block text-indigo-600 hover:underline">← العودة</a>
            </div>
            <div id="auth-container" class="text-center bg-white p-8 rounded-2xl shadow-lg">
                 <div id="auth-loading" class="text-center">
                    <svg class="animate-spin h-10 w-10 text-indigo-600 mx-auto" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>
                    <p class="mt-4 text-lg text-gray-700">جاري التحقق من صلاحية الجهاز...</p>
                </div>
                <div id="auth-error" class="hidden-view">
                     <div class="mx-auto w-16 h-16 flex items-center justify-center bg-red-100 rounded-full mb-4"><svg class="w-10 h-10 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636"></path></svg></div>
                     <h2 class="text-2xl font-bold text-red-600">الجهاز غير مصرح له</h2>
                     <p class="text-gray-600 mt-2">هذا الفيديو تم ربطه بجهاز آخر. لا يمكنك مشاهدته من هذا الجهاز.</p>
                     <a href="" class="mt-6 inline-block text-indigo-600 hover:underline">← العودة</a>
                </div>
            </div>
        </div>

        <!-- Delete Confirmation Modal -->
        <div id="delete-modal" class="hidden-view fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center z-50">
            <div class="relative mx-auto p-5 border w-full max-w-md shadow-lg rounded-2xl bg-white">
                <div class="mt-3 text-center">
                    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                        <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path></svg>
                    </div>
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mt-4">تأكيد الحذف</h3>
                    <div class="mt-2 px-7 py-3">
                        <p class="text-sm text-gray-500">هل أنت متأكد من رغبتك في حذف هذا الفيديو؟ لا يمكن التراجع عن هذا الإجراء.</p>
                    </div>
                    <div class="items-center px-4 py-3 space-x-4 space-x-reverse">
                        <button id="confirm-delete-btn" class="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md w-auto shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-300">نعم، قم بالحذف</button>
                        <button id="cancel-delete-btn" class="px-4 py-2 bg-gray-200 text-gray-800 text-base font-medium rounded-md w-auto shadow-sm hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-300">إلغاء</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Firebase SDK -->
    <script type="module">
        const firebaseConfig = {
            apiKey: "YOUR_API_KEY",
            authDomain: "YOUR_AUTH_DOMAIN",
            projectId: "YOUR_PROJECT_ID",
            storageBucket: "YOUR_STORAGE_BUCKET",
            messagingSenderId: "YOUR_MESSAGING_SENDER_ID",
            appId: "YOUR_APP_ID"
        };
        const finalFirebaseConfig = typeof __firebase_config !== 'undefined' ? JSON.parse(__firebase_config) : firebaseConfig;

        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-app.js";
        import { getFirestore, doc, getDoc, setDoc, collection, serverTimestamp, onSnapshot, deleteDoc } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-firestore.js";
        import { getAuth, signInAnonymously, onAuthStateChanged, signInWithCustomToken } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-auth.js";
        import { getStorage, ref, uploadBytesResumable, getDownloadURL, deleteObject } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-storage.js";

        const app = initializeApp(finalFirebaseConfig);
        const db = getFirestore(app);
        const auth = getAuth(app);
        const storage = getStorage(app);
        const appId = typeof __app_id !== 'undefined' ? __app_id : 'secure-video-default';

        // --- DOM Elements ---
        const loginView = document.getElementById('login-view');
        const loginForm = document.getElementById('login-form');
        const usernameInput = document.getElementById('username');
        const passwordInput = document.getElementById('password');
        const logoutBtn = document.getElementById('logout-btn');
        
        const teacherView = document.getElementById('teacher-view');
        const studentView = document.getElementById('student-view');
        const generateBtn = document.getElementById('generate-btn');
        const videoNameInput = document.getElementById('video-name');
        const videoFileInput = document.getElementById('video-file');
        const resultView = document.getElementById('result-view');
        const generatedLinkInput = document.getElementById('generated-link');
        const qrcodeCanvas = document.getElementById('qrcode-canvas');
        const copyBtn = document.getElementById('copy-btn');
        const copyFeedback = document.getElementById('copy-feedback');
        const uploadProgressContainer = document.getElementById('upload-progress-container');
        const uploadProgressBar = document.getElementById('upload-progress-bar');
        const uploadStatusText = document.getElementById('upload-status-text');
        const playerContainer = document.getElementById('player-container');
        const authContainer = document.getElementById('auth-container');
        const authLoading = document.getElementById('auth-loading');
        const authError = document.getElementById('auth-error');
        const videoPlayer = document.getElementById('video-player');
        const videoTitlePlayer = document.getElementById('video-title-player');
        const alertContainer = document.getElementById('alert-container');
        const videoListContainer = document.getElementById('video-list-container');
        const videoList = document.getElementById('video-list');
        const videoListLoading = document.getElementById('video-list-loading');
        const noVideosMessage = document.getElementById('no-videos-message');
        const deleteModal = document.getElementById('delete-modal');
        const confirmDeleteBtn = document.getElementById('confirm-delete-btn');
        const cancelDeleteBtn = document.getElementById('cancel-delete-btn');
        
        // --- Hardcoded Credentials ---
        const CORRECT_USERNAME = 'admin';
        const CORRECT_PASSWORD = 'password123';

        function showAlert(message, type = 'error') {
            const colors = {
                error: 'bg-red-100 text-red-800',
                success: 'bg-green-100 text-green-800',
                info: 'bg-blue-100 text-blue-800',
            };
            const alertDiv = document.createElement('div');
            alertDiv.className = `p-4 mb-4 text-sm rounded-lg shadow ${colors[type]}`;
            alertDiv.role = 'alert';
            alertDiv.innerHTML = `<span class="font-medium">${message}</span>`;
            alertContainer.appendChild(alertDiv);
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }

        function handleLogin(e) {
            e.preventDefault();
            const username = usernameInput.value;
            const password = passwordInput.value;
            if (username === CORRECT_USERNAME && password === CORRECT_PASSWORD) {
                sessionStorage.setItem('isLoggedIn', 'true');
                showTeacherView();
            } else {
                showAlert('اسم المستخدم أو كلمة المرور غير صحيحة.');
            }
        }

        function handleLogout() {
            sessionStorage.removeItem('isLoggedIn');
            window.location.reload();
        }
        
        function showLoginView() {
            loginView.classList.remove('hidden-view');
            teacherView.classList.add('hidden-view');
            studentView.classList.add('hidden-view');
        }

        function showTeacherView() {
            loginView.classList.add('hidden-view');
            teacherView.classList.remove('hidden-view');
            studentView.classList.add('hidden-view');
            videoListContainer.classList.remove('hidden-view');
            loadTeacherVideos();
        }


        function getDeviceFingerprint() {
            let deviceId = localStorage.getItem('device_unique_id');
            if (!deviceId) {
                deviceId = crypto.randomUUID();
                localStorage.setItem('device_unique_id', deviceId);
            }
            return deviceId;
        }

        async function handleRouting() {
            const urlParams = new URLSearchParams(window.location.search);
            const videoId = urlParams.get('videoId');

            if (videoId) {
                loginView.classList.add('hidden-view');
                teacherView.classList.add('hidden-view');
                studentView.classList.remove('hidden-view');
                authContainer.classList.remove('hidden-view');
                playerContainer.classList.add('hidden-view');
                await initializeViewer(videoId);
            } else {
                if (sessionStorage.getItem('isLoggedIn') === 'true') {
                    showTeacherView();
                } else {
                    showLoginView();
                }
            }
        }

        async function authenticateUser() {
            try {
                if (!auth.currentUser) {
                     if (typeof __initial_auth_token !== 'undefined' && __initial_auth_token) {
                        await signInWithCustomToken(auth, __initial_auth_token);
                    } else {
                        await signInAnonymously(auth);
                    }
                }
            } catch (error) {
                console.error("Authentication Error:", error);
                showAlert("فشل الاتصال بالخادم. يرجى تحديث الصفحة.");
            }
        }

        async function uploadAndGenerateLink() {
            const videoName = videoNameInput.value.trim();
            const file = videoFileInput.files[0];
            if (!videoName || !file) {
                showAlert('الرجاء إدخال اسم الفيديو واختيار ملف.');
                return;
            }
            generateBtn.disabled = true;
            generateBtn.textContent = 'جاري الرفع...';
            resultView.classList.add('hidden-view');
            uploadProgressContainer.classList.remove('hidden-view');
            const videoId = crypto.randomUUID();
            // FIX: Corrected storage path to match security rules
            const storageRef = ref(storage, `artifacts/${appId}/public/data/videos/${videoId}/${file.name}`);
            const uploadTask = uploadBytesResumable(storageRef, file);
            uploadTask.on('state_changed',
                (snapshot) => {
                    const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
                    uploadProgressBar.style.width = progress + '%';
                    uploadStatusText.textContent = `جاري رفع الملف... ${Math.round(progress)}%`;
                },
                (error) => {
                    console.error("Upload failed:", error);
                    showAlert('فشل رفع الفيديو. يرجى التأكد من صلاحيات التخزين والمحاولة مرة أخرى.');
                    generateBtn.disabled = false;
                    generateBtn.textContent = 'رفع وتوليد الرابط';
                    uploadProgressContainer.classList.add('hidden-view');
                },
                async () => {
                    uploadStatusText.textContent = 'اكتمل الرفع! جاري إنشاء الرابط...';
                    try {
                        const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);
                        const videoDocRef = doc(db, `artifacts/${appId}/public/data/videos`, videoId);
                        await setDoc(videoDocRef, {
                            videoName: videoName,
                            videoUrl: downloadURL,
                            storagePath: storageRef.fullPath,
                            deviceId: null,
                            createdAt: serverTimestamp()
                        });
                        const newUrl = `${window.location.origin}${window.location.pathname}?videoId=${videoId}`;
                        generatedLinkInput.value = newUrl;
                        QRCode.toCanvas(qrcodeCanvas, newUrl, { width: 200, errorCorrectionLevel: 'H' });
                        resultView.classList.remove('hidden-view');
                        uploadStatusText.textContent = "تم بنجاح!";
                    } catch (error) {
                        console.error("Error creating Firestore entry:", error);
                        showAlert('فشل حفظ بيانات الفيديو.');
                    } finally {
                        generateBtn.disabled = false;
                        generateBtn.textContent = 'رفع وتوليد الرابط';
                        videoNameInput.value = '';
                        videoFileInput.value = '';
                    }
                }
            );
        }

        function loadTeacherVideos() {
            const videoCollectionRef = collection(db, `artifacts/${appId}/public/data/videos`);
            onSnapshot(videoCollectionRef, (snapshot) => {
                videoListLoading.classList.add('hidden-view');
                videoList.innerHTML = '';
                if (snapshot.empty) {
                    noVideosMessage.classList.remove('hidden-view');
                } else {
                    noVideosMessage.classList.add('hidden-view');
                    let videos = [];
                    snapshot.forEach(doc => videos.push({ id: doc.id, ...doc.data() }));
                    videos.sort((a, b) => (b.createdAt?.toMillis() || 0) - (a.createdAt?.toMillis() || 0));
                    videos.forEach(video => videoList.appendChild(createVideoListItem(video)));
                }
            }, (error) => {
                console.error("Error fetching videos:", error);
                videoListLoading.textContent = "حدث خطأ في تحميل الفيديوهات.";
            });
        }

        function createVideoListItem(video) {
            const div = document.createElement('div');
            div.className = 'flex items-center justify-between bg-gray-50 p-3 rounded-lg border';
            const videoDate = video.createdAt ? new Date(video.createdAt.toMillis()).toLocaleDateString('ar-EG-u-nu-latn') : 'بدون تاريخ';
            div.innerHTML = `
                <div>
                    <p class="font-bold text-gray-800">${video.videoName || 'فيديو بلا عنوان'}</p>
                    <p class="text-sm text-gray-500">تاريخ الإنشاء: ${videoDate}</p>
                </div>
                <button title="حذف الفيديو" class="delete-video-btn p-2 text-red-500 hover:bg-red-100 rounded-full transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path></svg>
                </button>
            `;
            div.querySelector('.delete-video-btn').addEventListener('click', () => openDeleteModal(video.id, video.storagePath));
            return div;
        }

        function openDeleteModal(videoId, storagePath) {
            deleteModal.classList.remove('hidden-view');
            confirmDeleteBtn.onclick = () => deleteVideo(videoId, storagePath);
        }
        
        function closeDeleteModal() {
            deleteModal.classList.add('hidden-view');
        }

        async function deleteVideo(videoId, storagePath) {
            closeDeleteModal();
            showAlert("جاري حذف الفيديو...", 'info');
            try {
                if (storagePath) {
                    const storageFileRef = ref(storage, storagePath);
                    await deleteObject(storageFileRef);
                }
                const videoDocRef = doc(db, `artifacts/${appId}/public/data/videos`, videoId);
                await deleteDoc(videoDocRef);
                showAlert("تم حذف الفيديو بنجاح.", 'success');
            } catch (error) {
                console.error("Error deleting video:", error);
                showAlert("حدث خطأ أثناء حذف الفيديو. قد يكون الملف محذوفاً بالفعل.");
            }
        }

        async function initializeViewer(videoId) {
            const currentDeviceFingerprint = getDeviceFingerprint();
            try {
                const videoDocRef = doc(db, `artifacts/${appId}/public/data/videos`, videoId);
                const docSnap = await getDoc(videoDocRef);
                if (!docSnap.exists()) {
                    return showViewerError('رابط الفيديو غير صالح أو تم حذفه.');
                }
                const videoData = docSnap.data();
                const registeredDeviceId = videoData.deviceId;
                if (!registeredDeviceId) {
                    await setDoc(videoDocRef, { deviceId: currentDeviceFingerprint }, { merge: true });
                    playVideo(videoData);
                } else if (registeredDeviceId === currentDeviceFingerprint) {
                    playVideo(videoData);
                } else {
                    showViewerError('هذا الفيديو تم ربطه بجهاز آخر. لا يمكنك مشاهدته من هذا الجهاز.');
                }
            } catch (error) {
                console.error("Error verifying device:", error);
                showViewerError('حدث خطأ أثناء التحقق. الرجاء تحديث الصفحة والمحاولة مرة أخرى.');
            }
        }

        function showViewerError(message) {
            authLoading.classList.add('hidden-view');
            authError.classList.remove('hidden-view');
            authError.querySelector('p').textContent = message;
        }

        function playVideo(videoData) {
            authContainer.classList.add('hidden-view');
            playerContainer.classList.remove('hidden-view');
            videoTitlePlayer.textContent = videoData.videoName;
            videoPlayer.src = videoData.videoUrl;
            videoPlayer.play().catch(() => console.log("Autoplay prevented."));
        }

        function copyLink() {
            generatedLinkInput.select();
            document.execCommand('copy');
            copyFeedback.classList.remove('hidden-view');
            setTimeout(() => copyFeedback.classList.add('hidden-view'), 2000);
        }

        // --- Initial Load ---
        async function main() {
            await authenticateUser(); // Ensure Firebase connection is ready
            handleRouting(); // Decide which view to show
        }
        
        loginForm.addEventListener('submit', handleLogin);
        logoutBtn.addEventListener('click', handleLogout);
        generateBtn.addEventListener('click', uploadAndGenerateLink);
        copyBtn.addEventListener('click', copyLink);
        cancelDeleteBtn.addEventListener('click', closeDeleteModal);

        main();

    </script>


</body></html>