using ExamBuilder.Core.Models;

namespace ExamBuilder.PDF.Services
{
    /// <summary>
    /// واجهة خدمة إنشاء PDF
    /// </summary>
    public interface IPdfGeneratorService
    {
        Task<string> GenerateExamPdfAsync(Exam exam, string outputPath);
        Task<byte[]> GenerateExamPdfBytesAsync(Exam exam);
    }

    /// <summary>
    /// واجهة خدمة قوالب PDF
    /// </summary>
    public interface IPdfTemplateService
    {
        Task<string> GetTemplatePathAsync(string templateName);
        Task<IEnumerable<string>> GetAvailableTemplatesAsync();
    }

    /// <summary>
    /// تنفيذ مؤقت لخدمة إنشاء PDF
    /// </summary>
    public class PdfGeneratorService : IPdfGeneratorService
    {
        public async Task<string> GenerateExamPdfAsync(Exam exam, string outputPath)
        {
            await Task.Delay(1000); // محاكاة إنشاء PDF
            
            // TODO: تنفيذ إنشاء PDF باستخدام iText7
            // هنا سيتم إنشاء ملف PDF حقيقي
            
            // إنشاء ملف نصي مؤقت للاختبار
            var content = $@"
امتحان: {exam.Title}
المادة: {exam.Subject}
المدرس: {exam.TeacherName ?? "غير محدد"}
المدة: {exam.DurationMinutes} دقيقة
الدرجة الكلية: {exam.TotalMarks}

الأسئلة:
";

            for (int i = 0; i < exam.Questions.Count; i++)
            {
                var question = exam.Questions.ElementAt(i);
                content += $"\n{i + 1}. {question.Text} ({question.Points} نقطة)\n";
                
                if (question.Type == QuestionType.MultipleChoice)
                {
                    foreach (var option in question.Options)
                    {
                        content += $"   {(char)('أ' + option.Order - 1)}. {option.Text}\n";
                    }
                }
                content += "\n";
            }

            content += $"\n\nتم إنشاء هذا الامتحان بواسطة برنامج إنشاء الامتحانات\nالمهندس يوسف غنام\n{DateTime.Now:yyyy-MM-dd HH:mm:ss}";

            await File.WriteAllTextAsync(outputPath, content, System.Text.Encoding.UTF8);
            
            return outputPath;
        }

        public async Task<byte[]> GenerateExamPdfBytesAsync(Exam exam)
        {
            var tempPath = Path.GetTempFileName() + ".txt";
            await GenerateExamPdfAsync(exam, tempPath);
            var bytes = await File.ReadAllBytesAsync(tempPath);
            File.Delete(tempPath);
            return bytes;
        }
    }

    /// <summary>
    /// تنفيذ مؤقت لخدمة قوالب PDF
    /// </summary>
    public class PdfTemplateService : IPdfTemplateService
    {
        public async Task<string> GetTemplatePathAsync(string templateName)
        {
            await Task.Delay(100);
            return $"Templates/{templateName}.template";
        }

        public async Task<IEnumerable<string>> GetAvailableTemplatesAsync()
        {
            await Task.Delay(100);
            return new[] { "Standard", "Modern", "Classic", "Minimal" };
        }
    }
}
