import tkinter
import customtkinter as ctk
import qrcode
from PIL import Image
import os
import uuid
import json

# --- إعدادات الواجهة الرسومية ---
ctk.set_appearance_mode("System")  # Modes: "System" (default), "Dark", "Light"
ctk.set_default_color_theme("blue")  # Themes: "blue" (default), "green", "dark-blue"


class App(ctk.CTk):
    def __init__(self):
        super().__init__()

        # --- إعدادات النافذة الرئيسية ---
        self.title("برنامج توليد أكواد QR للفيديوهات")
        self.geometry(f"{800}x600")
        self.resizable(False, False)

        # --- إعداد هيكل الواجهة ---
        self.grid_columnconfigure(1, weight=1)
        self.grid_rowconfigure(0, weight=1)

        # --- الإطار الجانبي للمدخلات ---
        self.sidebar_frame = ctk.CTkFrame(self, width=250, corner_radius=0)
        self.sidebar_frame.grid(row=0, column=0, rowspan=4, sticky="nsew")
        self.sidebar_frame.grid_rowconfigure(5, weight=1)

        # --- عناصر الإطار الجانبي ---
        self.logo_label = ctk.CTkLabel(self.sidebar_frame, text="لوحة التحكم", font=ctk.CTkFont(size=20, weight="bold"))
        self.logo_label.grid(row=0, column=0, padx=20, pady=(20, 10))

        self.video_name_label = ctk.CTkLabel(self.sidebar_frame, text="اسم الفيديو (للتسمية):", anchor="w")
        self.video_name_label.grid(row=1, column=0, padx=20, pady=(10, 0), sticky="ew")
        self.video_name_entry = ctk.CTkEntry(self.sidebar_frame, placeholder_text="مثال: الدرس الأول")
        self.video_name_entry.grid(row=2, column=0, padx=20, pady=(0, 10), sticky="ew")

        self.video_url_label = ctk.CTkLabel(self.sidebar_frame, text="رابط الفيديو (URL):", anchor="w")
        self.video_url_label.grid(row=3, column=0, padx=20, pady=(10, 0), sticky="ew")
        self.video_url_entry = ctk.CTkEntry(self.sidebar_frame, placeholder_text="https://youtube.com/...")
        self.video_url_entry.grid(row=4, column=0, padx=20, pady=(0, 10), sticky="ew")
        
        self.quantity_label = ctk.CTkLabel(self.sidebar_frame, text="عدد النسخ المطلوبة:", anchor="w")
        self.quantity_label.grid(row=5, column=0, padx=20, pady=(10, 0), sticky="ew")
        self.quantity_entry = ctk.CTkEntry(self.sidebar_frame, placeholder_text="مثال: 30")
        self.quantity_entry.grid(row=6, column=0, padx=20, pady=(0, 10), sticky="ew")

        self.usage_limit_label = ctk.CTkLabel(self.sidebar_frame, text="عدد مرات السماح بتشغيل الكود:", anchor="w")
        self.usage_limit_label.grid(row=7, column=0, padx=20, pady=(10, 0), sticky="ew")
        self.usage_limit_entry = ctk.CTkEntry(self.sidebar_frame, placeholder_text="مثال: 3")
        self.usage_limit_entry.grid(row=8, column=0, padx=20, pady=(0, 10), sticky="ew")

        self.generate_button = ctk.CTkButton(self.sidebar_frame, text="توليد الأكواد", command=self.generate_codes_event)
        self.generate_button.grid(row=9, column=0, padx=20, pady=20, sticky="ew")

        # --- الإطار الرئيسي لعرض الأكواد ---
        self.main_frame = ctk.CTkFrame(self)
        self.main_frame.grid(row=0, column=1, padx=20, pady=20, sticky="nsew")

        # إضافة Scrollable Frame
        self.scrollable_frame = ctk.CTkScrollableFrame(self.main_frame, label_text="الأكواد التي تم إنشاؤها")
        self.scrollable_frame.pack(expand=True, fill="both", padx=10, pady=10)
        self.scrollable_frame.grid_columnconfigure(0, weight=1)
        
        self.info_label = ctk.CTkLabel(self.scrollable_frame, text="سيتم عرض أكواد QR هنا بعد إنشائها.\nسيتم حفظ الصور في مجلد 'qrcodes' تلقائياً.", justify="center")
        self.info_label.pack(pady=50)


    def generate_codes_event(self):
        # التحقق من المدخلات
        video_name = self.video_name_entry.get()
        video_url = self.video_url_entry.get()
        quantity_str = self.quantity_entry.get()
        usage_limit_str = self.usage_limit_entry.get()

        if not video_name or not video_url or not quantity_str or not usage_limit_str:
            self.show_message("خطأ", "الرجاء ملء جميع الحقول.")
            return

        try:
            quantity = int(quantity_str)
            if not (1 <= quantity <= 100):
                raise ValueError
        except ValueError:
            self.show_message("خطأ", "الرجاء إدخال عدد صحيح بين 1 و 100 في حقل العدد.")
            return

        try:
            usage_limit = int(usage_limit_str)
            if not (1 <= usage_limit <= 100):
                raise ValueError
        except ValueError:
            self.show_message("خطأ", "الرجاء إدخال عدد صحيح بين 1 و 100 في حقل السماحية.")
            return

        # مسح الأكواد القديمة من الواجهة
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()

        # إنشاء مجلد لحفظ الصور وملفات البيانات إذا لم يكن موجوداً
        if not os.path.exists("qrcodes"):
            os.makedirs("qrcodes")
        if not os.path.exists("qrdata"):
            os.makedirs("qrdata")

        # تعطيل الزر أثناء العملية
        self.generate_button.configure(state="disabled", text="جاري الإنشاء...")
        self.update_idletasks() # لتحديث الواجهة فوراً

        try:
            for i in range(quantity):
                # لكل كود: توليد معرف فريد
                code_id = str(uuid.uuid4())
                # رابط خاص بالكود (يمكنك تعديله ليحمل code_id)
                qr_link = f"{video_url}?code={code_id}"
                # حفظ بيانات الكود في ملف json
                code_data = {
                    "code_id": code_id,
                    "video_url": video_url,
                    "video_name": video_name,
                    "device_id": None,
                    "usage_count": 0,
                    "usage_limit": usage_limit
                }
                data_path = f"qrdata/data_{code_id}.json"
                with open(data_path, "w", encoding="utf-8") as f:
                    json.dump(code_data, f, ensure_ascii=False, indent=2)

                # إنشاء صورة QR
                qr_img = qrcode.make(qr_link)
                file_path = f"qrcodes/qr_{video_name.replace(' ', '_')}_{i+1}.png"
                qr_img.save(file_path)

                # عرض الصورة في الواجهة
                self.add_qr_to_gui(file_path, i + 1)

            self.show_message("نجاح", f"تم إنشاء {quantity} كود مع حماية أولية (كل كود له ملف بيانات خاص).\nاستخدم كود التحقق في برنامج الطالب.")

        except Exception as e:
            print(f"حدث خطأ: {e}")
            self.show_message("خطأ فادح", "حدث خطأ أثناء إنشاء الأكواد.")
        finally:
            self.generate_button.configure(state="normal", text="توليد الأكواد")


    def add_qr_to_gui(self, file_path, index):
        # إضافة كل كود في إطار خاص به
        item_frame = ctk.CTkFrame(self.scrollable_frame)
        item_frame.pack(pady=5, padx=5, fill="x")

        # عرض الصورة
        ctk_image = ctk.CTkImage(light_image=Image.open(file_path), size=(100, 100))
        image_label = ctk.CTkLabel(item_frame, image=ctk_image, text="")
        image_label.pack(side="left", padx=10, pady=10)

        # عرض المعلومات
        info_label = ctk.CTkLabel(item_frame, text=f"النسخة رقم: {index}\nالملف: {os.path.basename(file_path)}", justify="left")
        info_label.pack(side="left", padx=10, expand=True, anchor="w")


    def show_message(self, title, message):
        # إنشاء نافذة رسالة منبثقة
        dialog = ctk.CTkToplevel(self)
        dialog.title(title)
        dialog.geometry("300x150")
        dialog.resizable(False, False)
        dialog.transient(self) # لجعلها فوق النافذة الرئيسية

        label = ctk.CTkLabel(dialog, text=message, wraplength=250, justify="center")
        label.pack(pady=20, padx=20, expand=True)
        
        button = ctk.CTkButton(dialog, text="موافق", command=dialog.destroy)
        button.pack(pady=10)


if __name__ == "__main__":
    app = App()
    app.mainloop()
