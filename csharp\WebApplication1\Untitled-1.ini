/*
================================================================================================
ملحوظة هامة: هذا الكود مقسم إلى أجزاء تمثل ملفات مختلفة في مشروع C# WPF.
عند إنشاء المشروع في Visual Studio، قم بإنشاء الملفات التالية وانسخ كل جزء من الكود إليها.

1.  MainWindow.xaml      -> واجهة المستخدم الرسومية (تصميم).
2.  MainWindow.xaml.cs   -> منطق الواجهة الرسومية (الأوامر والأحداث).
3.  Services/            -> مجلد جديد باسم Services.
4.  Services/PdfService.cs -> كل ما يتعلق بإنشاء ملفات PDF.
5.  Services/SecurityService.cs -> وظائف التحقق من الجهاز والتشفير.
6.  Services/TelegramService.cs -> وظائف التعامل مع بوت تليجرام.
7.  Models/              -> مجلد جديد باسم Models.
8.  Models/AppSettings.cs -> كلاس لحفظ واستعادة الإعدادات.

------------------------------------------------------------------------------------------------
المكتبات التي ستحتاج لتثبيتها (NuGet Packages):
------------------------------------------------------------------------------------------------
- QuestPDF: لإنشاء ملفات PDF بسهولة واحترافية.
- SkiaSharp: لمعالجة الصور (بديل لمكتبة Pillow).
- Newtonsoft.Json: للتعامل مع ملفات JSON (مثل ملف الإعدادات).
- CTkMessagebox.WPF: لإنشاء مربعات رسائل جميلة (بديل لـ CTkMessagebox).
================================================================================================
*/


// ============================================================================================
// ملف 1: MainWindow.xaml (واجهة المستخدم الرسومية - التصميم)
// هذا هو ملف التصميم. يتم كتابته بلغة XAML.
// ============================================================================================

<Window x:Class="GhannamExam.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:GhannamExam"
        mc:Ignorable="d"
        Title="برنامج إنشاء الامتحانات - للمهندس يوسف غنام" Height="900" Width="1300" MinHeight="700" MinWidth="1000"
        WindowStartupLocation="CenterScreen"
        Background="#0F0A1A">
    <Grid Margin="15">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#622872" CornerRadius="15" Padding="10">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="برنامج إنشاء الامتحانات" FontSize="32" FontWeight="Bold" Foreground="#caa5cb" VerticalAlignment="Center" Margin="20,0,0,0"/>
                <Button Content="تغيير الثيم" HorizontalAlignment="Right" Margin="0,0,20,0" Padding="15,5" Background="#9C27B0" Foreground="#e8ddea" BorderThickness="0" Click="ToggleTheme_Click"/>
            </StackPanel>
        </Border>

        <!-- Tab Control -->
        <TabControl Grid.Row="1" Margin="0,15,0,0" Background="Transparent" BorderThickness="0">
            <TabControl.Resources>
                <Style TargetType="TabItem">
                    <Setter Property="Padding" Value="20,10"/>
                    <Setter Property="Foreground" Value="#e8ddea"/>
                    <Setter Property="Background" Value="#4A1D59"/>
                    <Setter Property="FontSize" Value="16"/>
                    <Setter Property="Template">
                        <Setter.Value>
                            <ControlTemplate TargetType="TabItem">
                                <Border x:Name="Border" BorderThickness="1,1,1,0" BorderBrush="#caa5cb" CornerRadius="10,10,0,0" Margin="2,0">
                                    <ContentPresenter x:Name="ContentSite" VerticalAlignment="Center" HorizontalAlignment="Center" ContentSource="Header" Margin="12,2,12,2"/>
                                </Border>
                                <ControlTemplate.Triggers>
                                    <Trigger Property="IsSelected" Value="True">
                                        <Setter TargetName="Border" Property="Background" Value="#622872" />
                                    </Trigger>
                                    <Trigger Property="IsSelected" Value="False">
                                        <Setter TargetName="Border" Property="Background" Value="#4A1D59" />
                                    </Trigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                </Style>
            </TabControl.Resources>
            
            <TabItem Header="الرئيسية">
                <Border Background="#622872" CornerRadius="0,0,15,15" Padding="20">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Left Panel - File Selection -->
                        <StackPanel Grid.Column="0" Margin="0,0,10,0">
                            <TextBlock Text="1. اختيار الملفات" FontSize="20" FontWeight="Bold" Foreground="#e8ddea" Margin="0,0,0,10"/>
                            <Button x:Name="SelectQuestionsButton" Content="اختيار صور الأسئلة" Margin="5" Padding="10" Click="SelectQuestions_Click"/>
                            <Button x:Name="SelectFrameButton" Content="اختيار صورة الفريم" Margin="5" Padding="10" Click="SelectFrame_Click"/>
                            <Button x:Name="SelectLogoButton" Content="اختيار صورة اللوجو (اختياري)" Margin="5" Padding="10" Click="SelectLogo_Click"/>
                            <TextBlock x:Name="StatusFiles" Text="لم يتم اختيار أي ملفات" Foreground="#a58fa8" Margin="5"/>
                        </StackPanel>
                        
                        <!-- Right Panel - PDF Options -->
                         <StackPanel Grid.Column="1" Margin="10,0,0,0">
                            <TextBlock Text="2. إعدادات PDF" FontSize="20" FontWeight="Bold" Foreground="#e8ddea" Margin="0,0,0,10"/>
                            <StackPanel Orientation="Horizontal" Margin="5">
                                <TextBlock Text="عدد الأسئلة في الصفحة:" VerticalAlignment="Center" Foreground="#e8ddea"/>
                                <TextBox x:Name="NumPerPage" Text="5" Width="50" Margin="10,0" VerticalContentAlignment="Center" HorizontalContentAlignment="Center"/>
                            </StackPanel>
                            <CheckBox x:Name="EnableNumbering" Content="تفعيل ترقيم الأسئلة" IsChecked="True" Foreground="#e8ddea" Margin="5"/>
                            <CheckBox x:Name="EnableCoverPage" Content="تفعيل صفحة الغلاف (بابل شيت)" IsChecked="False" Foreground="#e8ddea" Margin="5"/>
                        </StackPanel>
                    </Grid>
                </Border>
            </TabItem>
            
            <TabItem Header="إعدادات متقدمة">
                 <Border Background="#622872" CornerRadius="0,0,15,15" Padding="20">
                    <StackPanel>
                        <CheckBox x:Name="EnableWatermark" Content="تفعيل العلامة المائية" Foreground="#e8ddea" Margin="5"/>
                        <StackPanel Orientation="Horizontal" Margin="25,5,5,5">
                            <TextBlock Text="نص العلامة:" VerticalAlignment="Center" Foreground="#e8ddea"/>
                            <TextBox x:Name="WatermarkText" Text="سري وهام" Width="150" Margin="10,0"/>
                        </StackPanel>

                        <CheckBox x:Name="EnableEncryption" Content="تفعيل تشفير الملف بكلمة مرور" Foreground="#e8ddea" Margin="5,15,5,5"/>
                         <StackPanel Orientation="Horizontal" Margin="25,5,5,5">
                            <TextBlock Text="كلمة المرور:" VerticalAlignment="Center" Foreground="#e8ddea"/>
                            <TextBox x:Name="PdfPassword" Width="150" Margin="10,0"/>
                        </StackPanel>

                         <CheckBox x:Name="EnableRandomModels" Content="تفعيل توليد نماذج عشوائية" Foreground="#e8ddea" Margin="5,15,5,5"/>
                         <StackPanel Orientation="Horizontal" Margin="25,5,5,5">
                            <TextBlock Text="عدد النماذج:" VerticalAlignment="Center" Foreground="#e8ddea"/>
                            <TextBox x:Name="ModelsCount" Text="2" Width="50" Margin="10,0"/>
                        </StackPanel>
                    </StackPanel>
                 </Border>
            </TabItem>
        </TabControl>

        <!-- Footer -->
        <StackPanel Grid.Row="2" Margin="0,15,0,0">
            <Button x:Name="GeneratePdfButton" Content="إنشاء ملف PDF" Padding="25,15" FontSize="18" FontWeight="Bold" Background="#F50057" Foreground="White" Click="GeneratePdf_Click"/>
            <ProgressBar x:Name="ProgressBar" Height="10" Margin="0,10,0,0" IsIndeterminate="False"/>
            <TextBlock x:Name="StatusTextBlock" Text="جاهز للعمل" HorizontalAlignment="Center" Foreground="#a58fa8" Margin="0,5,0,0"/>
        </StackPanel>
    </Grid>
</Window>

// ============================================================================================
// ملف 2: MainWindow.xaml.cs (منطق الواجهة الرسومية - الأكواد)
// ============================================================================================

using Microsoft.Win32;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using GhannamExam.Services; // يجب إضافة هذا السطر

namespace GhannamExam
{
    public partial class MainWindow : Window
    {
        private readonly SecurityService _securityService;
        private readonly TelegramService _telegramService;
        private readonly PdfService _pdfService;
        private AppSettings _settings;

        public MainWindow()
        {
            InitializeComponent();
            _securityService = new SecurityService();
            _telegramService = new TelegramService();
            _pdfService = new PdfService();
            _settings = new AppSettings();

            this.Loaded += MainWindow_Loaded;
            this.Closing += MainWindow_Closing;
        }

        private async void MainWindow_Loaded(object sender, RoutedEventArgs e)
        {
            // التحقق من صلاحية الجهاز عند بدء التشغيل
            if (!_securityService.IsDeviceAuthorized())
            {
                var deviceId = _securityService.GetDeviceId();
                MessageBox.Show($"هذا الجهاز غير مصرح له باستخدام البرنامج.\nID: {deviceId}\n\nتم إرسال طلب تفعيل للمطور، يرجى الانتظار.", "جهاز غير مصرح به", MessageBoxButton.OK, MessageBoxImage.Warning);
                await _telegramService.SendApprovalRequest(deviceId);
                // يمكن إضافة آلية انتظار هنا أو إغلاق التطبيق
                Application.Current.Shutdown();
                return;
            }

            // تحميل الإعدادات
            _settings = AppSettings.Load();
            ApplySettingsToUi();

            // بدء الاستماع لأوامر تليجرام في الخلفية
            _telegramService.StartListening(HandleTelegramCommand);
        }

        private void MainWindow_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            // حفظ الإعدادات عند الإغلاق
            UpdateSettingsFromUi();
            _settings.Save();
            _telegramService.StopListening();
        }

        private void SelectQuestions_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Multiselect = true,
                Filter = "Image files (*.png;*.jpeg;*.jpg)|*.png;*.jpeg;*.jpg",
                Title = "اختر صور الأسئلة"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                _settings.QuestionImagePaths = openFileDialog.FileNames.ToList();
                StatusFiles.Text = $"تم اختيار {openFileDialog.FileNames.Length} صورة للأسئلة.";
            }
        }

        private void SelectFrame_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog { Filter = "Image files (*.png;*.jpeg;*.jpg)|*.png;*.jpeg;*.jpg" };
            if (openFileDialog.ShowDialog() == true)
            {
                _settings.FrameImagePath = openFileDialog.FileName;
                StatusFiles.Text = "تم اختيار صورة الفريم بنجاح.";
            }
        }

        private void SelectLogo_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog { Filter = "Image files (*.png;*.jpeg;*.jpg)|*.png;*.jpeg;*.jpg" };
            if (openFileDialog.ShowDialog() == true)
            {
                _settings.LogoImagePath = openFileDialog.FileName;
                StatusFiles.Text = "تم اختيار صورة اللوجو بنجاح.";
            }
        }

        private async void GeneratePdf_Click(object sender, RoutedEventArgs e)
        {
            // التحقق من المدخلات
            if (_settings.QuestionImagePaths == null || !_settings.QuestionImagePaths.Any() || string.IsNullOrEmpty(_settings.FrameImagePath))
            {
                MessageBox.Show("يرجى اختيار صور الأسئلة وصورة الفريم أولاً.", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            UpdateSettingsFromUi(); // تحديث الإعدادات من الواجهة قبل الإنشاء

            var saveFileDialog = new SaveFileDialog
            {
                Filter = "PDF file (*.pdf)|*.pdf",
                Title = "حفظ ملف PDF",
                FileName = "Exam.pdf"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                StatusTextBlock.Text = "جارٍ إنشاء الملفات...";
                ProgressBar.IsIndeterminate = true;
                
                try
                {
                    await _pdfService.GeneratePdfAsync(_settings, saveFileDialog.FileName);
                    StatusTextBlock.Text = "تم إنشاء الملفات بنجاح!";
                    MessageBox.Show("تم إنشاء ملفات PDF بنجاح!", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
                    
                    // فتح المجلد الذي يحتوي على الملف
                    Process.Start("explorer.exe", $"/select,\"{saveFileDialog.FileName}\"");
                }
                catch (Exception ex)
                {
                    StatusTextBlock.Text = "حدث خطأ أثناء الإنشاء.";
                    MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ فادح", MessageBoxButton.OK, MessageBoxImage.Error);
                }
                finally
                {
                    ProgressBar.IsIndeterminate = false;
                }
            }
        }

        private void HandleTelegramCommand(string command)
        {
            // التعامل مع الأوامر الواردة من تليجرام
            // هذه الدالة ستعمل في thread مختلف، لذا يجب استخدام Dispatcher لتحديث الواجهة
            Application.Current.Dispatcher.Invoke(() =>
            {
                if (command.ToLower().Contains("/close"))
                {
                    MessageBox.Show("تم استلام أمر إغلاق عن بعد.", "إشعار", MessageBoxButton.OK, MessageBoxImage.Information);
                    Application.Current.Shutdown();
                }
                // يمكن إضافة أوامر أخرى مثل الحظر وغيره
            });
        }
        
        // وظائف مساعدة لحفظ واستعادة الإعدادات
        private void ApplySettingsToUi()
        {
            NumPerPage.Text = _settings.QuestionsPerPage.ToString();
            EnableNumbering.IsChecked = _settings.EnableNumbering;
            EnableCoverPage.IsChecked = _settings.EnableCoverPage;
            EnableWatermark.IsChecked = _settings.EnableWatermark;
            WatermarkText.Text = _settings.WatermarkText;
            EnableEncryption.IsChecked = _settings.EnableEncryption;
            PdfPassword.Text = _settings.PdfPassword;
            EnableRandomModels.IsChecked = _settings.EnableRandomModels;
            ModelsCount.Text = _settings.ModelsCount.ToString();
        }

        private void UpdateSettingsFromUi()
        {
            if (int.TryParse(NumPerPage.Text, out int perPage)) _settings.QuestionsPerPage = perPage;
            _settings.EnableNumbering = EnableNumbering.IsChecked ?? false;
            _settings.EnableCoverPage = EnableCoverPage.IsChecked ?? false;
            _settings.EnableWatermark = EnableWatermark.IsChecked ?? false;
            _settings.WatermarkText = WatermarkText.Text;
            _settings.EnableEncryption = EnableEncryption.IsChecked ?? false;
            _settings.PdfPassword = PdfPassword.Text;
            _settings.EnableRandomModels = EnableRandomModels.IsChecked ?? false;
            if (int.TryParse(ModelsCount.Text, out int modelsCount)) _settings.ModelsCount = modelsCount;
        }

        private void ToggleTheme_Click(object sender, RoutedEventArgs e){} // يمكن إضافة منطق تغيير الثيمات هنا
    }
}


// ============================================================================================
// ملف 3: Services/PdfService.cs (منطق إنشاء الـ PDF)
// ============================================================================================

using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System;
using System.Collections.Generic;

namespace GhannamExam.Services
{
    public class PdfService
    {
        public PdfService()
        {
            // تعيين ترخيص QuestPDF - مهم جداً
            QuestPDF.Settings.License = LicenseType.Community;
        }

        public async Task GeneratePdfAsync(AppSettings settings, string outputPath)
        {
            var baseFilePath = Path.GetDirectoryName(outputPath);
            var baseFileName = Path.GetFileNameWithoutExtension(outputPath);
            var extension = Path.GetExtension(outputPath);

            if (settings.EnableRandomModels)
            {
                var tasks = new List<Task>();
                for (int i = 1; i <= settings.ModelsCount; i++)
                {
                    int modelNumber = i;
                    var modelPath = Path.Combine(baseFilePath, $"{baseFileName}_نموذج_{modelNumber}{extension}");
                    tasks.Add(Task.Run(() => CreateSinglePdf(settings, modelPath, modelNumber)));
                }
                await Task.WhenAll(tasks);
            }
            else
            {
                await Task.Run(() => CreateSinglePdf(settings, outputPath));
            }
        }
        
        private void CreateSinglePdf(AppSettings settings, string outputPath, int? modelNumber = null)
        {
            var questions = settings.QuestionImagePaths.ToList();
            if(modelNumber.HasValue)
            {
                var random = new Random(modelNumber.Value); // استخدام رقم النموذج كـ seed للترتيب العشوائي
                questions = questions.OrderBy(q => random.Next()).ToList();
            }

            Document.Create(container =>
            {
                // إضافة صفحة الغلاف إذا كانت مفعلة
                if (settings.EnableCoverPage)
                {
                    container.Page(page =>
                    {
                        page.Size(PageSizes.A4);
                        page.Margin(2, Unit.Centimetre);
                        page.Header().Text("ورقة الإجابة (بابل شيت)").SemiBold().FontSize(24).AlignCenter();
                        
                        // هنا يتم تصميم البابل شيت
                        page.Content().Column(col => {
                           col.Item().Text($"اسم الطالب: .....................................................");
                           col.Item().Text($"المادة: .....................................................");
                           if(modelNumber.HasValue)
                           {
                               col.Item().Text($"رقم النموذج: {modelNumber.Value}").Bold();
                           }
                           // يمكن إضافة باقي تفاصيل البابل شيت هنا
                        });

                        page.Footer().AlignCenter().Text(text =>
                        {
                            text.Span("صفحة ");
                            text.CurrentPageNumber();
                        });
                    });
                }

                // تقسيم الأسئلة على الصفحات
                var questionChunks = questions.Select((x, i) => new { Index = i, Value = x })
                                           .GroupBy(x => x.Index / settings.QuestionsPerPage)
                                           .Select(x => x.Select(v => v.Value).ToList())
                                           .ToList();

                int questionCounter = 1;
                foreach (var chunk in questionChunks)
                {
                     container.Page(page =>
                    {
                        page.Size(PageSizes.A4);
                        page.Margin(1, Unit.Centimetre);

                        // إضافة الفريم كخلفية
                        page.Background().Image(settings.FrameImagePath);
                        
                        // إضافة العلامة المائية
                        if(settings.EnableWatermark)
                        {
                            page.Foreground().AlignCenter().AlignMiddle().Rotate(-45)
                                .Text(settings.WatermarkText).FontSize(100).Bold().FontColor(Colors.Grey.Lighten2);
                        }
                        
                        // إضافة اللوجو
                         if(!string.IsNullOrEmpty(settings.LogoImagePath))
                         {
                            page.Header().AlignRight().Width(100).Image(settings.LogoImagePath);
                         }

                        page.Content().Column(column =>
                        {
                            foreach (var questionPath in chunk)
                            {
                                column.Item().Row(row => {
                                    if(settings.EnableNumbering)
                                    {
                                        row.AutoItem().Text($"{questionCounter++}. ").Bold();
                                    }
                                    row.RelativeItem().Image(questionPath);
                                });
                                column.Item().Spacing(10); // مسافة بين الأسئلة
                            }
                        });

                        page.Footer().AlignCenter().Text(text =>
                        {
                            text.Span("صفحة ");
                            text.CurrentPageNumber();
                        });
                    });
                }
            })
            .WithMetadata(new DocumentMetadata
            {
                Author = "المهندس يوسف غنام"
            })
            .WithSecurity(new DocumentSecurity
            {
                // تفعيل التشفير اذا كان مطلوبا
                AllowCopy = !settings.EnableEncryption,
                AllowPrint = !settings.EnableEncryption,
                UserPassword = settings.EnableEncryption ? settings.PdfPassword : null,
            })
            .GeneratePdf(outputPath);
        }
    }
}

// ============================================================================================
// ملف 4: Services/SecurityService.cs (وظائف الأمان)
// ============================================================================================
using System;
using System.IO;
using System.Linq;
using System.Net.NetworkInformation;
using System.Security.Cryptography;
using System.Text;

namespace GhannamExam.Services
{
    public class SecurityService
    {
        private const string SECRET_KEY = "my_super_secret_key"; // يجب أن يكون نفس المفتاح السري في نسخة بايثون
        private readonly string _authFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "authorized_devices.txt");

        public string GetDeviceId()
        {
            return NetworkInterface.GetAllNetworkInterfaces()
                .Where(nic => nic.OperationalStatus == OperationalStatus.Up && nic.NetworkInterfaceType != NetworkInterfaceType.Loopback)
                .Select(nic => nic.GetPhysicalAddress().ToString())
                .FirstOrDefault();
        }

        private string HashDeviceId(string deviceId)
        {
            using (var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(SECRET_KEY)))
            {
                var hash = hmac.ComputeHash(Encoding.UTF8.GetBytes(deviceId));
                return Convert.ToBase64String(hash);
            }
        }

        public bool IsDeviceAuthorized()
        {
            if (!File.Exists(_authFilePath))
            {
                File.Create(_authFilePath).Close();
                return false;
            }
            
            var deviceId = GetDeviceId();
            var authorizedIds = File.ReadAllLines(_authFilePath);

            // التحقق من المعرف الأصلي أو المشفر
            return authorizedIds.Contains(deviceId) || authorizedIds.Contains(HashDeviceId(deviceId));
        }
    }
}


// ============================================================================================
// ملف 5: Services/TelegramService.cs (وظائف بوت تليجرام)
// ============================================================================================
using System;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;


namespace GhannamExam.Services
{
    public class TelegramService
    {
        private const string BOT_TOKEN = "7763523784:AAHCt2WoJh1q9ZHEInhii-hqey9PCmtXsXg"; // نفس التوكن
        private const string CHAT_ID = "1486393122"; // نفس المعرف
        private readonly HttpClient _httpClient;
        private CancellationTokenSource _cancellationTokenSource;

        public TelegramService()
        {
            _httpClient = new HttpClient();
        }

        public async Task SendMessageAsync(string message)
        {
            var url = $"https://api.telegram.org/bot{BOT_TOKEN}/sendMessage?chat_id={CHAT_ID}&text={Uri.EscapeDataString(message)}";
            try
            {
                await _httpClient.GetAsync(url);
            }
            catch (Exception ex)
            {
                // يمكنك تسجيل الخطأ هنا
                Console.WriteLine($"Telegram Error: {ex.Message}");
            }
        }

        public async Task SendApprovalRequest(string deviceId)
        {
            var message = $"جهاز جديد يحاول الوصول إلى التطبيق:\nID: {deviceId}\nللموافقة أرسل /approve {deviceId}";
            await SendMessageAsync(message);
        }

        public void StartListening(Action<string> onCommandReceived)
        {
            _cancellationTokenSource = new CancellationTokenSource();
            var token = _cancellationTokenSource.Token;
            long offset = 0;

            Task.Run(async () =>
            {
                while (!token.IsCancellationRequested)
                {
                    try
                    {
                        var url = $"https://api.telegram.org/bot{BOT_TOKEN}/getUpdates?offset={offset}&timeout=60";
                        var response = await _httpClient.GetStringAsync(url);
                        var json = JObject.Parse(response);
                        
                        if (json["ok"].Value<bool>())
                        {
                            foreach (var update in json["result"])
                            {
                                offset = update["update_id"].Value<long>() + 1;
                                var message = update["message"]?["text"]?.Value<string>();
                                if (!string.IsNullOrEmpty(message))
                                {
                                    onCommandReceived?.Invoke(message);
                                }
                            }
                        }
                    }
                    catch (Exception) { /* تجاهل أخطاء الشبكة المؤقتة */ }
                    await Task.Delay(1000, token);
                }
            }, token);
        }

        public void StopListening()
        {
            _cancellationTokenSource?.Cancel();
        }
    }
}


// ============================================================================================
// ملف 6: Models/AppSettings.cs (كلاس الإعدادات)
// ============================================================================================

using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;

namespace GhannamExam
{
    public class AppSettings
    {
        private static readonly string SettingsFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "GhannamExamApp", "settings.json");

        public List<string> QuestionImagePaths { get; set; } = new List<string>();
        public string FrameImagePath { get; set; }
        public string LogoImagePath { get; set; }
        public int QuestionsPerPage { get; set; } = 5;
        public bool EnableNumbering { get; set; } = true;
        public bool EnableCoverPage { get; set; } = false;
        public bool EnableWatermark { get; set; } = false;
        public string WatermarkText { get; set; } = "سري";
        public bool EnableEncryption { get; set; } = false;
        public string PdfPassword { get; set; } = "";
        public bool EnableRandomModels { get; set; } = false;
        public int ModelsCount { get; set; } = 2;


        public void Save()
        {
            try
            {
                var directory = Path.GetDirectoryName(SettingsFilePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }
                var json = JsonConvert.SerializeObject(this, Formatting.Indented);
                File.WriteAllText(SettingsFilePath, json);
            }
            catch (Exception) { /* يمكنك التعامل مع الخطأ هنا */ }
        }

        public static AppSettings Load()
        {
            try
            {
                if (File.Exists(SettingsFilePath))
                {
                    var json = File.ReadAllText(SettingsFilePath);
                    return JsonConvert.DeserializeObject<AppSettings>(json) ?? new AppSettings();
                }
            }
            catch (Exception) { /* تجاهل الخطأ والعودة بإعدادات افتراضية */ }
            
            return new AppSettings();
        }
    }
}


Install-Package SkiaSharp
Install-Package Newtonsoft.Json
