using System;
using System.Windows;
using MahApps.Metro.Controls;
using Serilog;
using ExamBuilder.UI.ViewModels;

namespace ExamBuilder.UI.Views
{
    /// <summary>
    /// النافذة الرئيسية للتطبيق
    /// </summary>
    public partial class MainWindow : MetroWindow
    {
        private readonly MainViewModel _viewModel;

        public MainWindow()
        {
            InitializeComponent();

            // الحصول على ViewModel من حاوي الحقن
            _viewModel = App.GetService<MainViewModel>();
            DataContext = _viewModel;

            Log.Information("تم تحميل النافذة الرئيسية");
        }

        protected override void OnSourceInitialized(EventArgs e)
        {
            base.OnSourceInitialized(e);

            // تطبيق إعدادات النافذة المحفوظة
            LoadWindowSettings();
        }

        protected override void OnClosed(EventArgs e)
        {
            // حفظ إعدادات النافذة
            SaveWindowSettings();

            base.OnClosed(e);
        }

        private void LoadWindowSettings()
        {
            try
            {
                // TODO: تحميل إعدادات النافذة من الإعدادات
                // مثل الحجم والموقع والحالة
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "فشل في تحميل إعدادات النافذة");
            }
        }

        private void SaveWindowSettings()
        {
            try
            {
                // TODO: حفظ إعدادات النافذة
                // مثل الحجم والموقع والحالة
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "فشل في حفظ إعدادات النافذة");
            }
        }

        private void RedThemeButton_Click(object sender, RoutedEventArgs e)
        {
            App.ApplyTheme("Red");
            Log.Information("تم تطبيق الثيم الأحمر");
        }

        private void GreenThemeButton_Click(object sender, RoutedEventArgs e)
        {
            App.ApplyTheme("Green");
            Log.Information("تم تطبيق الثيم الأخضر");
        }

        private void DarkThemeButton_Click(object sender, RoutedEventArgs e)
        {
            App.ApplyTheme("Dark");
            Log.Information("تم تطبيق الثيم الداكن");
        }
    }
}
