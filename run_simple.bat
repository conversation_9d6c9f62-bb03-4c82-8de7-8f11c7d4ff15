@echo off
echo ========================================
echo    Exam Builder - Youssef <PERSON>m
echo ========================================
echo.

echo Checking system requirements...

dotnet --version > nul 2>&1
if %errorlevel% neq 0 (
    echo Error: .NET 8.0 is not installed
    echo Please install .NET 8.0 SDK from:
    echo https://dotnet.microsoft.com/download/dotnet/8.0
    pause
    exit /b 1
)

echo Found .NET: 
dotnet --version

echo.
echo Restoring packages...
dotnet restore

if %errorlevel% neq 0 (
    echo Error restoring packages
    pause
    exit /b 1
)

echo.
echo Building project...
dotnet build --configuration Release

if %errorlevel% neq 0 (
    echo Error building project
    pause
    exit /b 1
)

echo.
echo Starting application...
echo.
echo Login credentials:
echo Username: جو
echo Password: جو
echo.

dotnet run --project ExamBuilder.UI --configuration Release

if %errorlevel% neq 0 (
    echo Error running application
    pause
    exit /b 1
)

echo.
echo Application closed successfully
pause
