@echo off
echo Building and running Exam Builder...
echo.

dotnet restore
if %errorlevel% neq 0 goto error

dotnet build --configuration Release
if %errorlevel% neq 0 goto error

copy ExamBuilder.UI\appsettings.json ExamBuilder.UI\bin\Release\net8.0-windows\ >nul

echo.
echo Login: Username=جو Password=جو
echo.

cd ExamBuilder.UI\bin\Release\net8.0-windows
dotnet ExamBuilder.UI.dll

goto end

:error
echo Build failed!
pause
exit /b 1

:end
echo Application closed.
pause
