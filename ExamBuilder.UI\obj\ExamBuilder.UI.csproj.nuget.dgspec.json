{"format": 1, "restore": {"D:\\mr\\qqqq\\ExamBuilder.UI\\ExamBuilder.UI.csproj": {}}, "projects": {"D:\\mr\\qqqq\\ExamBuilder.Core\\ExamBuilder.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\mr\\qqqq\\ExamBuilder.Core\\ExamBuilder.Core.csproj", "projectName": "ExamBuilder.Core", "projectPath": "D:\\mr\\qqqq\\ExamBuilder.Core\\ExamBuilder.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\mr\\qqqq\\ExamBuilder.Core\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[12.0.1, )"}, "FluentValidation": {"target": "Package", "version": "[11.8.1, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Serilog": {"target": "Package", "version": "[3.1.1, )"}, "System.ComponentModel.Annotations": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.410/PortableRuntimeIdentifierGraph.json"}}}, "D:\\mr\\qqqq\\ExamBuilder.Data\\ExamBuilder.Data.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\mr\\qqqq\\ExamBuilder.Data\\ExamBuilder.Data.csproj", "projectName": "ExamBuilder.Data", "projectPath": "D:\\mr\\qqqq\\ExamBuilder.Data\\ExamBuilder.Data.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\mr\\qqqq\\ExamBuilder.Data\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\mr\\qqqq\\ExamBuilder.Core\\ExamBuilder.Core.csproj": {"projectPath": "D:\\mr\\qqqq\\ExamBuilder.Core\\ExamBuilder.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "System.ComponentModel.Annotations": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.410/PortableRuntimeIdentifierGraph.json"}}}, "D:\\mr\\qqqq\\ExamBuilder.PDF\\ExamBuilder.PDF.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\mr\\qqqq\\ExamBuilder.PDF\\ExamBuilder.PDF.csproj", "projectName": "ExamBuilder.PDF", "projectPath": "D:\\mr\\qqqq\\ExamBuilder.PDF\\ExamBuilder.PDF.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\mr\\qqqq\\ExamBuilder.PDF\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\mr\\qqqq\\ExamBuilder.Core\\ExamBuilder.Core.csproj": {"projectPath": "D:\\mr\\qqqq\\ExamBuilder.Core\\ExamBuilder.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Serilog": {"target": "Package", "version": "[3.1.1, )"}, "System.Drawing.Common": {"target": "Package", "version": "[8.0.0, )"}, "iText7": {"target": "Package", "version": "[8.0.2, )"}, "iText7.pdfhtml": {"target": "Package", "version": "[5.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.410/PortableRuntimeIdentifierGraph.json"}}}, "D:\\mr\\qqqq\\ExamBuilder.Security\\ExamBuilder.Security.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\mr\\qqqq\\ExamBuilder.Security\\ExamBuilder.Security.csproj", "projectName": "ExamBuilder.Security", "projectPath": "D:\\mr\\qqqq\\ExamBuilder.Security\\ExamBuilder.Security.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\mr\\qqqq\\ExamBuilder.Security\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\mr\\qqqq\\ExamBuilder.Core\\ExamBuilder.Core.csproj": {"projectPath": "D:\\mr\\qqqq\\ExamBuilder.Core\\ExamBuilder.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Serilog": {"target": "Package", "version": "[3.1.1, )"}, "System.Management": {"target": "Package", "version": "[8.0.0, )"}, "System.Security.Cryptography.Algorithms": {"target": "Package", "version": "[4.3.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.410/PortableRuntimeIdentifierGraph.json"}}}, "D:\\mr\\qqqq\\ExamBuilder.Telegram\\ExamBuilder.Telegram.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\mr\\qqqq\\ExamBuilder.Telegram\\ExamBuilder.Telegram.csproj", "projectName": "ExamBuilder.Telegram", "projectPath": "D:\\mr\\qqqq\\ExamBuilder.Telegram\\ExamBuilder.Telegram.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\mr\\qqqq\\ExamBuilder.Telegram\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\mr\\qqqq\\ExamBuilder.Core\\ExamBuilder.Core.csproj": {"projectPath": "D:\\mr\\qqqq\\ExamBuilder.Core\\ExamBuilder.Core.csproj"}, "D:\\mr\\qqqq\\ExamBuilder.Security\\ExamBuilder.Security.csproj": {"projectPath": "D:\\mr\\qqqq\\ExamBuilder.Security\\ExamBuilder.Security.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Hosting.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Serilog": {"target": "Package", "version": "[3.1.1, )"}, "System.Text.Json": {"target": "Package", "version": "[8.0.5, )"}, "Telegram.Bot": {"target": "Package", "version": "[19.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.410/PortableRuntimeIdentifierGraph.json"}}}, "D:\\mr\\qqqq\\ExamBuilder.UI\\ExamBuilder.UI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\mr\\qqqq\\ExamBuilder.UI\\ExamBuilder.UI.csproj", "projectName": "ExamBuilder.UI", "projectPath": "D:\\mr\\qqqq\\ExamBuilder.UI\\ExamBuilder.UI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\mr\\qqqq\\ExamBuilder.UI\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"D:\\mr\\qqqq\\ExamBuilder.Core\\ExamBuilder.Core.csproj": {"projectPath": "D:\\mr\\qqqq\\ExamBuilder.Core\\ExamBuilder.Core.csproj"}, "D:\\mr\\qqqq\\ExamBuilder.Data\\ExamBuilder.Data.csproj": {"projectPath": "D:\\mr\\qqqq\\ExamBuilder.Data\\ExamBuilder.Data.csproj"}, "D:\\mr\\qqqq\\ExamBuilder.PDF\\ExamBuilder.PDF.csproj": {"projectPath": "D:\\mr\\qqqq\\ExamBuilder.PDF\\ExamBuilder.PDF.csproj"}, "D:\\mr\\qqqq\\ExamBuilder.Security\\ExamBuilder.Security.csproj": {"projectPath": "D:\\mr\\qqqq\\ExamBuilder.Security\\ExamBuilder.Security.csproj"}, "D:\\mr\\qqqq\\ExamBuilder.Telegram\\ExamBuilder.Telegram.csproj": {"projectPath": "D:\\mr\\qqqq\\ExamBuilder.Telegram\\ExamBuilder.Telegram.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"MahApps.Metro": {"target": "Package", "version": "[2.4.10, )"}, "MahApps.Metro.IconPacks": {"target": "Package", "version": "[4.11.0, )"}, "MaterialDesignColors": {"target": "Package", "version": "[2.1.4, )"}, "MaterialDesignThemes": {"target": "Package", "version": "[4.9.0, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[8.0.0, )"}, "Serilog": {"target": "Package", "version": "[3.1.1, )"}, "Serilog.Extensions.Hosting": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.410/PortableRuntimeIdentifierGraph.json"}}}}}