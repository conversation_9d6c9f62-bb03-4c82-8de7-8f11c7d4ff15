namespace ExamBuilder.Core.Services
{
    /// <summary>
    /// واجهة خدمة إدارة المستخدمين
    /// </summary>
    public interface IUserService
    {
        Task<bool> UserExistsAsync(string username);
        Task<string> GetUserRoleAsync(string username);
    }

    /// <summary>
    /// تنفيذ خدمة إدارة المستخدمين
    /// </summary>
    public class UserService : IUserService
    {
        public async Task<bool> UserExistsAsync(string username)
        {
            await Task.Delay(100);
            return username == "جو";
        }

        public async Task<string> GetUserRoleAsync(string username)
        {
            await Task.Delay(100);
            return username == "جو" ? "Administrator" : "User";
        }
    }
}
