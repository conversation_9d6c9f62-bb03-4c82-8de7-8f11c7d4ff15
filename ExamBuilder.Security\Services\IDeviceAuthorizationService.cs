using System.Management;
using System.Net.NetworkInformation;
using System.Text.Json;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace ExamBuilder.Security.Services
{
    /// <summary>
    /// واجهة خدمة تصريح الأجهزة
    /// </summary>
    public interface IDeviceAuthorizationService
    {
        Task<DeviceAuthorizationResult> CheckDeviceAuthorizationAsync();
        Task<ApprovalResult> WaitForApprovalAsync(int timeoutMinutes = 5);
        Task<bool> RegisterDeviceAsync();
        Task<bool> BlockDeviceAsync(string deviceId);
        Task<bool> UnblockDeviceAsync(string deviceId);
        Task<IEnumerable<DeviceInfo>> GetAuthorizedDevicesAsync();
        Task<IEnumerable<DeviceInfo>> GetBlockedDevicesAsync();
        string GetCurrentDeviceId();
        Task<DeviceInfo> GetCurrentDeviceInfoAsync();
    }

    /// <summary>
    /// تنفيذ خدمة تصريح الأجهزة
    /// </summary>
    public class DeviceAuthorizationService : IDeviceAuthorizationService
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<DeviceAuthorizationService> _logger;
        private readonly IEncryptionService _encryptionService;
        private readonly string _authorizedDevicesFile;
        private readonly string _blockedDevicesFile;
        private readonly string _pendingDevicesFile;

        public DeviceAuthorizationService(
            IConfiguration configuration,
            ILogger<DeviceAuthorizationService> logger,
            IEncryptionService encryptionService)
        {
            _configuration = configuration;
            _logger = logger;
            _encryptionService = encryptionService;

            var dataDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "ExamBuilder");
            Directory.CreateDirectory(dataDirectory);

            _authorizedDevicesFile = Path.Combine(dataDirectory, "authorized_devices.json");
            _blockedDevicesFile = Path.Combine(dataDirectory, "blocked_devices.json");
            _pendingDevicesFile = Path.Combine(dataDirectory, "pending_devices.json");
        }

        public async Task<DeviceAuthorizationResult> CheckDeviceAuthorizationAsync()
        {
            try
            {
                var currentDeviceId = GetCurrentDeviceId();
                var hashedDeviceId = _encryptionService.HashData(currentDeviceId);

                _logger.LogInformation($"التحقق من تصريح الجهاز: {hashedDeviceId[..8]}...");

                // التحقق من قائمة الأجهزة المحظورة أولاً
                var blockedDevices = await LoadDevicesFromFileAsync(_blockedDevicesFile);
                if (blockedDevices.Any(d => d.HashedDeviceId == hashedDeviceId))
                {
                    _logger.LogWarning($"الجهاز محظور: {hashedDeviceId[..8]}...");
                    return new DeviceAuthorizationResult
                    {
                        IsAuthorized = false,
                        RequiresApproval = false,
                        Message = "هذا الجهاز محظور من استخدام التطبيق"
                    };
                }

                // التحقق من قائمة الأجهزة المصرح بها
                var authorizedDevices = await LoadDevicesFromFileAsync(_authorizedDevicesFile);
                var authorizedDevice = authorizedDevices.FirstOrDefault(d => d.HashedDeviceId == hashedDeviceId);

                if (authorizedDevice != null)
                {
                    // تحديث آخر نشاط
                    authorizedDevice.LastActiveAt = DateTime.Now;
                    await SaveDevicesToFileAsync(_authorizedDevicesFile, authorizedDevices);

                    _logger.LogInformation($"الجهاز مصرح له: {hashedDeviceId[..8]}...");
                    return new DeviceAuthorizationResult
                    {
                        IsAuthorized = true,
                        RequiresApproval = false,
                        Message = "الجهاز مصرح له باستخدام التطبيق"
                    };
                }

                // الجهاز غير مصرح له - يحتاج موافقة
                await AddToPendingDevicesAsync(currentDeviceId);

                _logger.LogInformation($"الجهاز يحتاج موافقة: {hashedDeviceId[..8]}...");
                return new DeviceAuthorizationResult
                {
                    IsAuthorized = false,
                    RequiresApproval = true,
                    Message = "الجهاز يحتاج موافقة للوصول إلى التطبيق"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحقق من تصريح الجهاز");
                throw;
            }
        }

        public async Task<ApprovalResult> WaitForApprovalAsync(int timeoutMinutes = 5)
        {
            try
            {
                var currentDeviceId = GetCurrentDeviceId();
                var hashedDeviceId = _encryptionService.HashData(currentDeviceId);
                var startTime = DateTime.Now;
                var timeout = TimeSpan.FromMinutes(timeoutMinutes);

                _logger.LogInformation($"انتظار الموافقة للجهاز: {hashedDeviceId[..8]}... (مهلة: {timeoutMinutes} دقائق)");

                while (DateTime.Now - startTime < timeout)
                {
                    // التحقق من قائمة الأجهزة المصرح بها
                    var authorizedDevices = await LoadDevicesFromFileAsync(_authorizedDevicesFile);
                    if (authorizedDevices.Any(d => d.HashedDeviceId == hashedDeviceId))
                    {
                        _logger.LogInformation($"تمت الموافقة على الجهاز: {hashedDeviceId[..8]}...");
                        return new ApprovalResult
                        {
                            IsApproved = true,
                            Message = "تمت الموافقة على الجهاز"
                        };
                    }

                    // التحقق من قائمة الأجهزة المحظورة
                    var blockedDevices = await LoadDevicesFromFileAsync(_blockedDevicesFile);
                    if (blockedDevices.Any(d => d.HashedDeviceId == hashedDeviceId))
                    {
                        _logger.LogWarning($"تم رفض الجهاز: {hashedDeviceId[..8]}...");
                        return new ApprovalResult
                        {
                            IsApproved = false,
                            Message = "تم رفض الجهاز"
                        };
                    }

                    // انتظار قبل المحاولة التالية
                    await Task.Delay(5000); // 5 ثوانٍ
                }

                _logger.LogWarning($"انتهت مهلة انتظار الموافقة للجهاز: {hashedDeviceId[..8]}...");
                return new ApprovalResult
                {
                    IsApproved = false,
                    Message = "انتهت مهلة انتظار الموافقة"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في انتظار الموافقة");
                throw;
            }
        }

        public async Task<bool> RegisterDeviceAsync()
        {
            try
            {
                var deviceInfo = await GetCurrentDeviceInfoAsync();
                var authorizedDevices = await LoadDevicesFromFileAsync(_authorizedDevicesFile);

                // التحقق من عدم وجود الجهاز مسبقاً
                if (!authorizedDevices.Any(d => d.HashedDeviceId == deviceInfo.HashedDeviceId))
                {
                    authorizedDevices.Add(deviceInfo);
                    await SaveDevicesToFileAsync(_authorizedDevicesFile, authorizedDevices);

                    _logger.LogInformation($"تم تسجيل الجهاز: {deviceInfo.HashedDeviceId[..8]}...");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تسجيل الجهاز");
                return false;
            }
        }

        public async Task<bool> BlockDeviceAsync(string deviceId)
        {
            try
            {
                var hashedDeviceId = _encryptionService.HashData(deviceId);
                var blockedDevices = await LoadDevicesFromFileAsync(_blockedDevicesFile);

                if (!blockedDevices.Any(d => d.HashedDeviceId == hashedDeviceId))
                {
                    var deviceInfo = new DeviceInfo
                    {
                        HashedDeviceId = hashedDeviceId,
                        DeviceName = "Unknown",
                        BlockedAt = DateTime.Now,
                        Status = DeviceStatus.Blocked
                    };

                    blockedDevices.Add(deviceInfo);
                    await SaveDevicesToFileAsync(_blockedDevicesFile, blockedDevices);

                    // إزالة من قائمة الأجهزة المصرح بها إن وجد
                    var authorizedDevices = await LoadDevicesFromFileAsync(_authorizedDevicesFile);
                    var deviceToRemove = authorizedDevices.FirstOrDefault(d => d.HashedDeviceId == hashedDeviceId);
                    if (deviceToRemove != null)
                    {
                        authorizedDevices.Remove(deviceToRemove);
                        await SaveDevicesToFileAsync(_authorizedDevicesFile, authorizedDevices);
                    }

                    _logger.LogInformation($"تم حظر الجهاز: {hashedDeviceId[..8]}...");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"خطأ في حظر الجهاز: {deviceId}");
                return false;
            }
        }

        public async Task<bool> UnblockDeviceAsync(string deviceId)
        {
            try
            {
                var hashedDeviceId = _encryptionService.HashData(deviceId);
                var blockedDevices = await LoadDevicesFromFileAsync(_blockedDevicesFile);

                var deviceToUnblock = blockedDevices.FirstOrDefault(d => d.HashedDeviceId == hashedDeviceId);
                if (deviceToUnblock != null)
                {
                    blockedDevices.Remove(deviceToUnblock);
                    await SaveDevicesToFileAsync(_blockedDevicesFile, blockedDevices);

                    _logger.LogInformation($"تم إلغاء حظر الجهاز: {hashedDeviceId[..8]}...");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"خطأ في إلغاء حظر الجهاز: {deviceId}");
                return false;
            }
        }

        public async Task<IEnumerable<DeviceInfo>> GetAuthorizedDevicesAsync()
        {
            return await LoadDevicesFromFileAsync(_authorizedDevicesFile);
        }

        public async Task<IEnumerable<DeviceInfo>> GetBlockedDevicesAsync()
        {
            return await LoadDevicesFromFileAsync(_blockedDevicesFile);
        }

        public string GetCurrentDeviceId()
        {
            try
            {
                // الحصول على MAC Address كمعرف فريد للجهاز
                var networkInterfaces = NetworkInterface.GetAllNetworkInterfaces()
                    .Where(nic => nic.OperationalStatus == OperationalStatus.Up && 
                                 nic.NetworkInterfaceType != NetworkInterfaceType.Loopback)
                    .ToList();

                if (networkInterfaces.Any())
                {
                    var primaryInterface = networkInterfaces.First();
                    var macAddress = primaryInterface.GetPhysicalAddress().ToString();
                    
                    if (!string.IsNullOrEmpty(macAddress))
                    {
                        return macAddress;
                    }
                }

                // إذا لم نتمكن من الحصول على MAC Address، نستخدم معرف الجهاز من Windows
                return GetWindowsDeviceId();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على معرف الجهاز");
                
                // كحل أخير، نستخدم اسم الجهاز + اسم المستخدم
                return $"{Environment.MachineName}_{Environment.UserName}";
            }
        }

        public async Task<DeviceInfo> GetCurrentDeviceInfoAsync()
        {
            try
            {
                var deviceId = GetCurrentDeviceId();
                var hashedDeviceId = _encryptionService.HashData(deviceId);

                return new DeviceInfo
                {
                    HashedDeviceId = hashedDeviceId,
                    DeviceName = Environment.MachineName,
                    UserName = Environment.UserName,
                    OperatingSystem = Environment.OSVersion.ToString(),
                    RegisteredAt = DateTime.Now,
                    LastActiveAt = DateTime.Now,
                    Status = DeviceStatus.Authorized
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على معلومات الجهاز الحالي");
                throw;
            }
        }

        private string GetWindowsDeviceId()
        {
            try
            {
                using var searcher = new ManagementObjectSearcher("SELECT UUID FROM Win32_ComputerSystemProduct");
                using var collection = searcher.Get();
                
                foreach (var item in collection)
                {
                    var uuid = item["UUID"]?.ToString();
                    if (!string.IsNullOrEmpty(uuid))
                    {
                        return uuid;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "فشل في الحصول على UUID من Windows");
            }

            return Environment.MachineName;
        }

        private async Task<List<DeviceInfo>> LoadDevicesFromFileAsync(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    return new List<DeviceInfo>();
                }

                var json = await File.ReadAllTextAsync(filePath);
                return JsonSerializer.Deserialize<List<DeviceInfo>>(json) ?? new List<DeviceInfo>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"خطأ في تحميل الأجهزة من الملف: {filePath}");
                return new List<DeviceInfo>();
            }
        }

        private async Task SaveDevicesToFileAsync(string filePath, List<DeviceInfo> devices)
        {
            try
            {
                var json = JsonSerializer.Serialize(devices, new JsonSerializerOptions { WriteIndented = true });
                await File.WriteAllTextAsync(filePath, json);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"خطأ في حفظ الأجهزة إلى الملف: {filePath}");
                throw;
            }
        }

        private async Task AddToPendingDevicesAsync(string deviceId)
        {
            try
            {
                var hashedDeviceId = _encryptionService.HashData(deviceId);
                var pendingDevices = await LoadDevicesFromFileAsync(_pendingDevicesFile);

                if (!pendingDevices.Any(d => d.HashedDeviceId == hashedDeviceId))
                {
                    var deviceInfo = await GetCurrentDeviceInfoAsync();
                    deviceInfo.Status = DeviceStatus.Pending;
                    
                    pendingDevices.Add(deviceInfo);
                    await SaveDevicesToFileAsync(_pendingDevicesFile, pendingDevices);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة الجهاز إلى قائمة الانتظار");
            }
        }
    }

    // النماذج المساعدة
    public class DeviceAuthorizationResult
    {
        public bool IsAuthorized { get; set; }
        public bool RequiresApproval { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    public class ApprovalResult
    {
        public bool IsApproved { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    public class DeviceInfo
    {
        public string HashedDeviceId { get; set; } = string.Empty;
        public string DeviceName { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public string OperatingSystem { get; set; } = string.Empty;
        public DateTime RegisteredAt { get; set; }
        public DateTime LastActiveAt { get; set; }
        public DateTime? BlockedAt { get; set; }
        public DeviceStatus Status { get; set; }
    }

    public enum DeviceStatus
    {
        Pending = 0,
        Authorized = 1,
        Blocked = 2
    }
}
