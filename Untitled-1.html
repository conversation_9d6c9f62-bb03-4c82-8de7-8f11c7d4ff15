import React, { useState, useEffect } from 'react';
import { ShoppingCart, User, Menu, X, Star, MessageSquare, Phone, MapPin, Send } from 'lucide-react';

// HELPER: بيانات وهمية للمنيو (يمكن استبدالها ببيانات من قاعدة البيانات)
const menuData = {
  "الإفطار": [
    { id: 1, name: "فول بالزيت الحار", description: "طبق فول مدمس مع زيت الزيتون والبهارات الخاصة.", price: 15, image: "https://placehold.co/400x300/800000/F2E8C6?text=فول" },
    { id: 2, name: "طعمية محشية", description: "5 قطع طعمية مقرمشة محشية بالجبنة أو الخضروات.", price: 20, image: "https://placehold.co/400x300/800000/F2E8C6?text=طعمية" },
  ],
  "الشاورما": [
    { id: 3, name: "شاورما لحم", description: "خبز سوري، شرائح لحم متبلة، طحينة ومخلل.", price: 45, image: "https://placehold.co/400x300/982B1C/F2E8C6?text=شاورما+لحم" },
    { id: 4, name: "شاورما دجاج", description: "خبز سوري، قطع دجاج مشوي، ثومية ومخلل.", price: 40, image: "https://placehold.co/400x300/982B1C/F2E8C6?text=شاورما+دجاج" },
  ],
  "الكريب": [
      { id: 5, name: "كريب دجاج كرسبي", description: "كريب مقرمش محشو بقطع الدجاج الكرسبي والجبنة الموتزاريلا.", price: 55, image: "https://placehold.co/400x300/800000/F2E8C6?text=كريب+حادق" },
      { id: 6, name: "كريب نوتيلا وموز", description: "كريب حلو غني بشوكولاتة النوتيلا وشرائح الموز الطازج.", price: 40, image: "https://placehold.co/400x300/982B1C/F2E8C6?text=كريب+حلو" },
  ],
  "المشروبات": [
      { id: 7, name: "عصير برتقال طازج", description: "عصير برتقال طبيعي معصور لحظة الطلب.", price: 25, image: "https://placehold.co/400x300/800000/F2E8C6?text=عصير" },
      { id: 8, name: "مياه معدنية", description: "مياه نقية ومنعشة.", price: 10, image: "https://placehold.co/400x300/982B1C/F2E8C6?text=مياه" },
  ],
};

// HELPER: بيانات وهمية لآراء العملاء
const testimonials = [
  { name: "أحمد علي", comment: "أفضل شاورما ذقتها في حياتي! الطعم أصلي والخدمة ممتازة.", rating: 5 },
  { name: "سارة محمود", comment: "المكان راقي والأكل نظيف جدًا. الكريب كان رائعًا. سأعود مرة أخرى بالتأكيد.", rating: 5 },
  { name: "خالد إبراهيم", comment: "تجربة طلب أونلاين سهلة وسريعة، والطلب وصل في الوقت المحدد وكان ساخنًا.", rating: 4 },
];

// المكون الرئيسي للتطبيق
export default function App() {
  const [page, setPage] = useState('home');
  const [cart, setCart] = useState([]);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [notification, setNotification] = useState('');

  // دالة لإظهار إشعار مؤقت
  const showNotification = (message) => {
    setNotification(message);
    setTimeout(() => {
      setNotification('');
    }, 3000);
  };

  // دوال خاصة بسلة التسوق
  const addToCart = (item) => {
    setCart(prevCart => {
      const existingItem = prevCart.find(cartItem => cartItem.id === item.id);
      if (existingItem) {
        return prevCart.map(cartItem =>
          cartItem.id === item.id ? { ...cartItem, quantity: cartItem.quantity + 1 } : cartItem
        );
      } else {
        return [...prevCart, { ...item, quantity: 1 }];
      }
    });
    showNotification(`تمت إضافة "${item.name}" إلى السلة`);
  };

  const removeFromCart = (itemId) => {
    setCart(prevCart => prevCart.filter(item => item.id !== itemId));
  };

  const updateQuantity = (itemId, amount) => {
    setCart(prevCart =>
      prevCart.map(item =>
        item.id === item.id ? { ...item, quantity: Math.max(1, item.quantity + amount) } : item
      ).filter(item => item.quantity > 0)
    );
  };
  
  const totalCartItems = cart.reduce((sum, item) => sum + item.quantity, 0);
  const totalCartPrice = cart.reduce((sum, item) => sum + item.price * item.quantity, 0);

  // مكون التنقل (Header)
  const Header = () => (
    <header className="bg-maroon-dark sticky top-0 z-50 shadow-lg text-beige-light">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-20">
          {/* Logo */}
          <div 
            className="text-2xl font-bold cursor-pointer transition-transform hover:scale-105"
            onClick={() => setPage('home')}
          >
            اللافندر المتميز
          </div>

          {/* Navigation Links - Desktop */}
          <nav className="hidden md:flex items-center space-x-8 space-x-reverse">
            <a onClick={() => setPage('home')} className="cursor-pointer hover:text-white transition-colors">الرئيسية</a>
            <a onClick={() => setPage('menu')} className="cursor-pointer hover:text-white transition-colors">المنيو</a>
            <a onClick={() => setPage('about')} className="cursor-pointer hover:text-white transition-colors">من نحن</a>
            <a onClick={() => setPage('contact')} className="cursor-pointer hover:text-white transition-colors">تواصل معنا</a>
          </nav>

          {/* Action Buttons - Desktop */}
          <div className="hidden md:flex items-center space-x-4 space-x-reverse">
            <button onClick={() => setPage('order')} className="relative flex items-center p-2 rounded-full hover:bg-maroon-accent transition-colors">
              <ShoppingCart />
              {totalCartItems > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">{totalCartItems}</span>
              )}
            </button>
            <button onClick={() => setPage('admin')} className="flex items-center p-2 rounded-full hover:bg-maroon-accent transition-colors">
              <User />
            </button>
            <button onClick={() => setPage('menu')} className="bg-maroon-accent hover:bg-red-700 text-white font-bold py-2 px-6 rounded-lg transition-all transform hover:scale-105 shadow-md">
              اطلب الآن
            </button>
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden">
            <button onClick={() => setIsMenuOpen(!isMenuOpen)}>
              {isMenuOpen ? <X size={28} /> : <Menu size={28} />}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="md:hidden bg-maroon-dark pb-4">
          <nav className="flex flex-col items-center space-y-4">
            <a onClick={() => { setPage('home'); setIsMenuOpen(false); }} className="cursor-pointer hover:text-white transition-colors">الرئيسية</a>
            <a onClick={() => { setPage('menu'); setIsMenuOpen(false); }} className="cursor-pointer hover:text-white transition-colors">المنيو</a>
            <a onClick={() => { setPage('about'); setIsMenuOpen(false); }} className="cursor-pointer hover:text-white transition-colors">من نحن</a>
            <a onClick={() => { setPage('contact'); setIsMenuOpen(false); }} className="cursor-pointer hover:text-white transition-colors">تواصل معنا</a>
            <div className="flex items-center space-x-6 space-x-reverse pt-4">
               <button onClick={() => { setPage('order'); setIsMenuOpen(false); }} className="relative flex items-center p-2 rounded-full hover:bg-maroon-accent transition-colors">
                  <ShoppingCart />
                  {totalCartItems > 0 && (
                    <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">{totalCartItems}</span>
                  )}
               </button>
               <button onClick={() => { setPage('admin'); setIsMenuOpen(false); }} className="flex items-center p-2 rounded-full hover:bg-maroon-accent transition-colors">
                  <User />
               </button>
            </div>
            <button onClick={() => { setPage('menu'); setIsMenuOpen(false); }} className="mt-4 bg-maroon-accent hover:bg-red-700 text-white font-bold py-2 px-6 rounded-lg transition-transform hover:scale-105 shadow-md">
              اطلب الآن
            </button>
          </nav>
        </div>
      )}
    </header>
  );

  // مكون الصفحة الرئيسية
  const HomePage = () => {
    // Slider state
    const sliderImages = [
      "https://placehold.co/1200x500/800000/F2E8C6?text=أشهى+الأطباق",
      "https://placehold.co/1200x500/982B1C/F2E8C6?text=شاورما+لا+تقاوم",
      "https://placehold.co/1200x500/800000/F2E8C6?text=مذاق+أصلي"
    ];
    const [currentSlide, setCurrentSlide] = useState(0);

    useEffect(() => {
        const timer = setTimeout(() => {
            setCurrentSlide((prev) => (prev + 1) % sliderImages.length);
        }, 5000);
        return () => clearTimeout(timer);
    }, [currentSlide]);

    return (
        <div className="animate-fade-in">
            {/* Hero Section with Slider */}
            <section className="relative h-96 md:h-[500px] bg-gray-800 text-white overflow-hidden">
                {sliderImages.map((src, index) => (
                    <div key={index} className={`absolute inset-0 transition-opacity duration-1000 ${index === currentSlide ? 'opacity-100' : 'opacity-0'}`}>
                        <img src={src} alt={`Slide ${index + 1}`} className="w-full h-full object-cover" />
                        <div className="absolute inset-0 bg-black bg-opacity-50"></div>
                    </div>
                ))}
                <div className="relative z-10 flex flex-col items-center justify-center h-full text-center px-4">
                    <h1 className="text-4xl md:text-6xl font-extrabold mb-4 drop-shadow-lg">مذاق لا ينسى في اللافندر المتميز</h1>
                    <p className="text-lg md:text-xl max-w-2xl mb-8 drop-shadow-md">نقدم لكم أشهى المأكولات المحضرة بحب وشغف لتمنحكم تجربة فريدة.</p>
                    <button onClick={() => setPage('menu')} className="bg-maroon-accent hover:bg-red-700 text-white font-bold py-3 px-8 rounded-lg text-lg transition-all transform hover:scale-105 shadow-xl">
                        تصفح المنيو
                    </button>
                </div>
            </section>

            {/* About Snippet */}
            <section className="py-16 bg-beige-light">
                <div className="container mx-auto px-6 text-center">
                    <h2 className="text-3xl font-bold text-maroon-dark mb-4">قصتنا</h2>
                    <p className="max-w-3xl mx-auto text-maroon-dark opacity-80 leading-relaxed">
                        في "اللافندر المتميز"، نحن لا نقدم الطعام فقط، بل نقدم تجربة. بدأت رحلتنا من شغف بالمطبخ الشرقي الأصيل، ونسعى كل يوم لتقديم أطباق تجمع بين التقاليد العريقة واللمسات العصرية، باستخدام أجود المكونات الطازجة.
                    </p>
                </div>
            </section>

            {/* Featured Items */}
            <section className="py-16 bg-beige-extra-light">
                <div className="container mx-auto px-6">
                    <h2 className="text-3xl font-bold text-maroon-dark text-center mb-12">أصنافنا المميزة</h2>
                    <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                        {menuData["الشاورما"].map(item => <MenuItemCard key={item.id} item={item} />)}
                        {menuData["الكريب"].map(item => <MenuItemCard key={item.id} item={item} />)}
                    </div>
                </div>
            </section>
            
            {/* Testimonials */}
            <section className="py-16 bg-beige-light">
                <div className="container mx-auto px-6">
                    <h2 className="text-3xl font-bold text-maroon-dark text-center mb-12">ماذا يقول زبائننا؟</h2>
                    <div className="grid md:grid-cols-3 gap-8">
                        {testimonials.map((testimonial, index) => (
                            <div key={index} className="bg-white p-6 rounded-lg shadow-md border-t-4 border-maroon-accent">
                                <p className="text-gray-600 mb-4">"{testimonial.comment}"</p>
                                <div className="flex justify-between items-center">
                                    <span className="font-bold text-maroon-dark">{testimonial.name}</span>
                                    <div className="flex">
                                        {[...Array(testimonial.rating)].map((_, i) => <Star key={i} className="text-yellow-400 fill-current" />)}
                                        {[...Array(5 - testimonial.rating)].map((_, i) => <Star key={i} className="text-gray-300" />)}
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </section>
        </div>
    );
  };
  
  // مكون بطاقة الصنف في المنيو
  const MenuItemCard = ({ item }) => (
    <div className="bg-white rounded-lg shadow-lg overflow-hidden transform transition-transform hover:-translate-y-2 flex flex-col">
        <img src={item.image} alt={item.name} className="w-full h-48 object-cover" />
        <div className="p-4 flex flex-col flex-grow">
            <h3 className="text-xl font-bold text-maroon-dark mb-2">{item.name}</h3>
            <p className="text-gray-600 text-sm mb-4 flex-grow">{item.description}</p>
            <div className="flex justify-between items-center mt-auto">
                <span className="text-lg font-bold text-maroon-accent">{item.price} جنيه</span>
                <button onClick={() => addToCart(item)} className="bg-maroon-dark text-white py-2 px-4 rounded-lg hover:bg-maroon-accent transition-colors">
                    أضف للسلة
                </button>
            </div>
        </div>
    </div>
  );

  // مكون صفحة المنيو
  const MenuPage = () => (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12 animate-fade-in">
        <h1 className="text-4xl font-extrabold text-maroon-dark text-center mb-12">قائمة الطعام</h1>
        {Object.keys(menuData).map(category => (
            <div key={category} className="mb-16">
                <h2 className="text-3xl font-bold text-maroon-accent mb-8 border-b-2 border-maroon-accent pb-2">{category}</h2>
                <div className="grid sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
                    {menuData[category].map(item => (
                        <MenuItemCard key={item.id} item={item} />
                    ))}
                </div>
            </div>
        ))}
    </div>
  );

  // مكون صفحة الطلب (سلة التسوق)
  const OrderPage = () => (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12 animate-fade-in">
      <h1 className="text-4xl font-extrabold text-maroon-dark text-center mb-12">سلة الطلبات</h1>
      {cart.length === 0 ? (
        <div className="text-center py-16">
          <ShoppingCart size={64} className="mx-auto text-gray-400 mb-4" />
          <p className="text-xl text-gray-600">سلة التسوق فارغة.</p>
          <button onClick={() => setPage('menu')} className="mt-6 bg-maroon-accent hover:bg-red-700 text-white font-bold py-3 px-8 rounded-lg text-lg transition-all transform hover:scale-105 shadow-xl">
            ابدأ التسوق
          </button>
        </div>
      ) : (
        <div className="grid lg:grid-cols-3 gap-12">
          {/* Cart Items */}
          <div className="lg:col-span-2 space-y-4">
            {cart.map(item => (
              <div key={item.id} className="flex items-center bg-white p-4 rounded-lg shadow-md">
                <img src={item.image} alt={item.name} className="w-24 h-24 object-cover rounded-md" />
                <div className="flex-grow mx-4">
                  <h3 className="text-lg font-bold text-maroon-dark">{item.name}</h3>
                  <p className="text-maroon-accent font-semibold">{item.price} جنيه</p>
                </div>
                <div className="flex items-center space-x-3 space-x-reverse">
                    <button onClick={() => updateQuantity(item.id, 1)} className="bg-gray-200 w-8 h-8 rounded-full font-bold text-lg hover:bg-gray-300">+</button>
                    <span className="font-bold w-8 text-center">{item.quantity}</span>
                    <button onClick={() => updateQuantity(item.id, -1)} className="bg-gray-200 w-8 h-8 rounded-full font-bold text-lg hover:bg-gray-300">-</button>
                </div>
                <button onClick={() => removeFromCart(item.id)} className="mr-4 text-gray-500 hover:text-red-500 transition-colors">
                    <X size={24}/>
                </button>
              </div>
            ))}
          </div>

          {/* Order Summary & Checkout */}
          <div className="bg-beige-light p-6 rounded-lg shadow-lg h-fit sticky top-28">
            <h2 className="text-2xl font-bold text-maroon-dark border-b-2 border-maroon-accent pb-3 mb-4">ملخص الطلب</h2>
            <div className="space-y-2 mb-6">
              <div className="flex justify-between">
                <span>المجموع الفرعي</span>
                <span>{totalCartPrice} جنيه</span>
              </div>
              <div className="flex justify-between">
                <span>رسوم التوصيل</span>
                <span>15 جنيه</span>
              </div>
              <div className="flex justify-between text-xl font-bold text-maroon-dark pt-2 border-t mt-2">
                <span>الإجمالي</span>
                <span>{totalCartPrice + 15} جنيه</span>
              </div>
            </div>
            
            <h3 className="text-xl font-bold text-maroon-dark mb-3">طريقة التوصيل</h3>
            <div className="flex space-x-4 space-x-reverse mb-6">
                <label className="flex-1 border p-3 rounded-md cursor-pointer has-[:checked]:bg-maroon-accent has-[:checked]:text-white has-[:checked]:border-maroon-accent transition-colors">
                    <input type="radio" name="delivery" value="delivery" className="sr-only" defaultChecked /> توصيل
                </label>
                 <label className="flex-1 border p-3 rounded-md cursor-pointer has-[:checked]:bg-maroon-accent has-[:checked]:text-white has-[:checked]:border-maroon-accent transition-colors">
                    <input type="radio" name="delivery" value="pickup" className="sr-only" /> استلام
                </label>
            </div>

            <h3 className="text-xl font-bold text-maroon-dark mb-3">طريقة الدفع</h3>
             <div className="flex space-x-4 space-x-reverse mb-6">
                <label className="flex-1 border p-3 rounded-md cursor-pointer has-[:checked]:bg-maroon-accent has-[:checked]:text-white has-[:checked]:border-maroon-accent transition-colors">
                    <input type="radio" name="payment" value="cash" className="sr-only" defaultChecked /> نقداً
                </label>
                 <label className="flex-1 border p-3 rounded-md cursor-pointer has-[:checked]:bg-maroon-accent has-[:checked]:text-white has-[:checked]:border-maroon-accent transition-colors text-gray-400" title="قريباً">
                    <input type="radio" name="payment" value="online" className="sr-only" disabled /> أونلاين
                </label>
            </div>

            <button onClick={() => { showNotification('تم إرسال طلبك بنجاح!'); setCart([]); }} className="w-full bg-maroon-dark text-white font-bold py-3 rounded-lg text-lg hover:bg-maroon-accent transition-colors shadow-lg">
              إتمام الطلب
            </button>
          </div>
        </div>
      )}
    </div>
  );

  // مكون صفحة "من نحن"
  const AboutPage = () => (
      <div className="bg-beige-extra-light animate-fade-in">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-16">
              <div className="max-w-4xl mx-auto">
                  <h1 className="text-4xl md:text-5xl font-extrabold text-maroon-dark text-center mb-8">قصتنا، شغفنا، طعامنا</h1>
                  <p className="text-lg text-gray-700 leading-relaxed mb-6">
                      وُلد "اللافندر المتميز" من حلم بسيط: تقديم تجربة طعام شرقية أصيلة بلمسة عصرية لا تُنسى. نحن نؤمن بأن الطعام هو لغة عالمية، وسيلة للتواصل تجمع الناس وتخلق ذكريات سعيدة. لهذا السبب، نختار مكوناتنا بعناية فائقة، من المزارع المحلية كلما أمكن، لنضمن لكم نكهة طازجة وغنية في كل طبق.
                  </p>
                  <img src="https://placehold.co/800x400/DAD4B5/800000?text=فريق+العمل" alt="فريق المطعم" className="rounded-lg shadow-xl my-8 mx-auto" />
                  <h2 className="text-3xl font-bold text-maroon-accent mt-12 mb-4">فلسفتنا في الطهي</h2>
                  <p className="text-lg text-gray-700 leading-relaxed">
                      فلسفتنا تقوم على ثلاثة أعمدة رئيسية: الجودة، الأصالة، والابتكار. نحافظ على الوصفات التقليدية التي أحببتموها، مع إضافة لمساتنا الخاصة التي تجعل كل طبق مغامرة جديدة. فريق الطهاة لدينا يمتلك سنوات من الخبرة والشغف، ويعملون بجد لتحويل كل وجبة إلى تحفة فنية ترضي جميع الأذواق.
                  </p>
              </div>
          </div>
      </div>
  );

  // مكون صفحة "تواصل معنا"
  const ContactPage = () => (
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-16 animate-fade-in">
          <h1 className="text-4xl font-extrabold text-maroon-dark text-center mb-12">تواصل معنا</h1>
          <div className="grid lg:grid-cols-2 gap-12 items-start">
              {/* Contact Form */}
              <div className="bg-white p-8 rounded-lg shadow-lg">
                  <h2 className="text-2xl font-bold text-maroon-dark mb-6">أرسل لنا رسالة</h2>
                  <form className="space-y-6">
                      <div>
                          <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">الاسم</label>
                          <input type="text" id="name" className="w-full p-3 border border-gray-300 rounded-md focus:ring-maroon-accent focus:border-maroon-accent" />
                      </div>
                      <div>
                          <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">البريد الإلكتروني</label>
                          <input type="email" id="email" className="w-full p-3 border border-gray-300 rounded-md focus:ring-maroon-accent focus:border-maroon-accent" />
                      </div>
                      <div>
                          <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">رسالتك</label>
                          <textarea id="message" rows="5" className="w-full p-3 border border-gray-300 rounded-md focus:ring-maroon-accent focus:border-maroon-accent"></textarea>
                      </div>
                      <button type="submit" className="w-full flex justify-center items-center bg-maroon-dark text-white font-bold py-3 px-6 rounded-lg hover:bg-maroon-accent transition-colors shadow-md">
                          <Send className="mr-2" size={20} /> إرسال
                      </button>
                  </form>
              </div>
              {/* Contact Info & Map */}
              <div className="space-y-8">
                  <div className="bg-white p-8 rounded-lg shadow-lg">
                      <h3 className="text-xl font-bold text-maroon-dark mb-4">معلومات الاتصال</h3>
                      <div className="space-y-4 text-gray-700">
                          <div className="flex items-center">
                              <MapPin className="text-maroon-accent mr-3" />
                              <span>شارع المثال، حي النموذج، القاهرة، مصر</span>
                          </div>
                          <div className="flex items-center">
                              <Phone className="text-maroon-accent mr-3" />
                              <a href="tel:+201234567890" className="hover:text-maroon-accent">+20 ************</a>
                          </div>
                          <div className="flex items-center">
                              <MessageSquare className="text-maroon-accent mr-3" />
                               <a href="https://wa.me/201234567890" target="_blank" rel="noopener noreferrer" className="hover:text-maroon-accent">تواصل عبر واتساب</a>
                          </div>
                      </div>
                  </div>
                  <div className="rounded-lg shadow-lg overflow-hidden">
                       <a href="https://www.google.com/maps" target="_blank" rel="noopener noreferrer">
                            <img src="https://placehold.co/600x400/DAD4B5/800000?text=خريطة+جوجل" alt="خريطة موقع المطعم" className="w-full h-full object-cover"/>
                       </a>
                  </div>
              </div>
          </div>
      </div>
  );

    // مكون لوحة تحكم الإدارة (مبسط)
    const AdminDashboard = () => {
        // ... هنا يمكن إضافة منطق لوحة التحكم الكامل لاحقاً
        return (
            <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12 animate-fade-in">
                <h1 className="text-4xl font-extrabold text-maroon-dark text-center mb-12">لوحة تحكم الإدارة</h1>
                <div className="bg-white p-8 rounded-lg shadow-lg max-w-md mx-auto">
                    <h2 className="text-2xl font-bold text-maroon-dark mb-6">تسجيل الدخول</h2>
                    <p className="text-center text-gray-600 mb-6">هذه المنطقة مخصصة لإدارة المطعم. في النسخة النهائية، ستكون محمية بكلمة مرور.</p>
                     <div className="space-y-4">
                       <p>• إدارة الأصناف (إضافة – تعديل – حذف).</p>
                       <p>• متابعة الطلبات لحظة بلحظة.</p>
                       <p>• تقارير مبيعات.</p>
                       <p>• إدارة المستخدمين والعملاء.</p>
                    </div>
                    <button className="mt-8 w-full bg-gray-400 text-white font-bold py-3 rounded-lg cursor-not-allowed">
                        تسجيل الدخول (معطل حالياً)
                    </button>
                </div>
            </div>
        );
    };

  // المكون الذي يعرض الصفحة الحالية
  const PageContent = () => {
    switch (page) {
      case 'home':
        return <HomePage />;
      case 'menu':
        return <MenuPage />;
      case 'order':
        return <OrderPage />;
      case 'about':
        return <AboutPage />;
      case 'contact':
        return <ContactPage />;
      case 'admin':
          return <AdminDashboard />;
      default:
        return <HomePage />;
    }
  };
  
  // مكون الإشعارات
  const Notification = ({ message }) => {
      if (!message) return null;
      return (
          <div className="fixed bottom-5 right-5 bg-green-500 text-white py-2 px-6 rounded-lg shadow-lg z-50 animate-bounce-in">
              {message}
          </div>
      );
  };
  
  // مكون زر الواتساب الثابت
  const WhatsAppButton = () => (
      <a 
        href="https://wa.me/201234567890" 
        target="_blank" 
        rel="noopener noreferrer"
        className="fixed bottom-5 left-5 bg-green-500 text-white w-14 h-14 rounded-full flex items-center justify-center shadow-lg z-50 transform transition-transform hover:scale-110"
        aria-label="تواصل معنا عبر واتساب"
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="currentColor"><path d="M.057 24l1.687-6.163c-1.041-1.804-1.588-3.849-1.587-5.946.003-6.556 5.338-11.891 11.893-11.891 3.181.001 6.167 1.24 8.413 3.488 2.245 2.248 3.481 5.236 3.48 8.414-.003 6.557-5.338 11.892-11.894 11.892-1.99-.001-3.951-.5-5.688-1.448l-6.305 1.654zm6.597-3.807c1.676.995 3.276 1.591 5.392 1.592 5.448 0 9.886-4.434 9.889-9.885.002-5.462-4.415-9.89-9.881-9.892-5.452 0-9.887 4.434-9.889 9.886-.001 2.269.655 4.509 1.91 6.344l-1.225 4.485 4.67-1.224zM16.781 13.985c-.27-.135-1.612-.799-1.861-.891-.249-.092-.43-.135-.61.135-.18.27-.703.891-.861 1.069-.158.178-.317.199-.586.064-.27-.135-1.143-.424-2.176-1.341-.803-.719-1.34-1.611-1.498-1.889-.158-.278-.016-.43.118-.564.118-.118.27-.304.404-.448.135-.143.178-.27.27-.448.09-.178.044-.317-.02-.448-.065-.135-.61-1.469-.835-2.011-.225-.542-.45-.466-.61-.476-.144-.009-.317-.009-.49-.009-.173 0-.448.065-.688.317-.24.252-.926.901-1.143 2.199-.217 1.299.117 2.553.134 2.729.018.178.491.742 1.183 1.451.992 1.002 1.693 1.451 2.275 1.76.583.308 1.1.252 1.52.158.465-.107 1.612-.656 1.836-.926.225-.27.225-.5.178-.564s-.09-.135-.178-.227z"></path></svg>
      </a>
  );

  return (
    <div style={{ direction: 'rtl' }} className="font-sans bg-beige-extra-light text-maroon-dark">
      <style>
        {`
          :root {
            --maroon-dark: #800000;
            --maroon-accent: #982B1C;
            --beige-light: #DAD4B5;
            --beige-extra-light: #F2E8C6;
          }
          .bg-maroon-dark { background-color: var(--maroon-dark); }
          .bg-maroon-accent { background-color: var(--maroon-accent); }
          .bg-beige-light { background-color: var(--beige-light); }
          .bg-beige-extra-light { background-color: var(--beige-extra-light); }
          .text-maroon-dark { color: var(--maroon-dark); }
          .text-maroon-accent { color: var(--maroon-accent); }
          .text-beige-light { color: var(--beige-light); }
          .border-maroon-accent { border-color: var(--maroon-accent); }
          .focus\\:ring-maroon-accent:focus { --tw-ring-color: var(--maroon-accent); }
          .focus\\:border-maroon-accent:focus { border-color: var(--maroon-accent); }
          
          @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;700;900&display=swap');
          .font-sans { font-family: 'Cairo', sans-serif; }

          @keyframes fade-in {
              from { opacity: 0; transform: translateY(10px); }
              to { opacity: 1; transform: translateY(0); }
          }
          .animate-fade-in { animation: fade-in 0.5s ease-out forwards; }
          
          @keyframes bounce-in {
            0% { transform: scale(0.5); opacity: 0; }
            80% { transform: scale(1.1); }
            100% { transform: scale(1); opacity: 1; }
          }
          .animate-bounce-in { animation: bounce-in 0.5s ease-out forwards; }
        `}
      </style>
      <Header />
      <main>
        <PageContent />
      </main>
      <Notification message={notification} />
      <WhatsAppButton />
      <footer className="bg-maroon-dark text-beige-light py-8">
          <div className="container mx-auto px-6 text-center">
              <p>&copy; {new Date().getFullYear()} مطعم اللافندر المتميز. جميع الحقوق محفوظة.</p>
          </div>
      </footer>
    </div>
  );
}
