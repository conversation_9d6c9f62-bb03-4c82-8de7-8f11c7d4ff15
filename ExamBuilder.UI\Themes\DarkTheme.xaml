<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Dark Theme Colors -->
    <SolidColorBrush x:Key="PrimaryBrush" Color="#6366F1" />
    <SolidColorBrush x:Key="PrimaryDarkBrush" Color="#4F46E5" />
    <SolidColorBrush x:Key="PrimaryLightBrush" Color="#8B5CF6" />
    
    <SolidColorBrush x:Key="SecondaryBrush" Color="#374151" />
    <SolidColorBrush x:Key="SecondaryDarkBrush" Color="#1F2937" />
    <SolidColorBrush x:Key="SecondaryLightBrush" Color="#4B5563" />
    
    <SolidColorBrush x:Key="AccentBrush" Color="#1F2937" />
    <SolidColorBrush x:Key="AccentDarkBrush" Color="#111827" />
    <SolidColorBrush x:Key="AccentLightBrush" Color="#374151" />
    
    <SolidColorBrush x:Key="BackgroundBrush" Color="#111827" />
    <SolidColorBrush x:Key="SurfaceBrush" Color="#1F2937" />
    <SolidColorBrush x:Key="CardBrush" Color="#1F2937" />
    
    <SolidColorBrush x:Key="TextPrimaryBrush" Color="#F9FAFB" />
    <SolidColorBrush x:Key="TextSecondaryBrush" Color="#9CA3AF" />
    <SolidColorBrush x:Key="TextDisabledBrush" Color="#6B7280" />
    
    <SolidColorBrush x:Key="SuccessBrush" Color="#10B981" />
    <SolidColorBrush x:Key="ErrorBrush" Color="#EF4444" />
    <SolidColorBrush x:Key="WarningBrush" Color="#F59E0B" />
    <SolidColorBrush x:Key="InfoBrush" Color="#06B6D4" />
    
    <SolidColorBrush x:Key="BorderBrush" Color="#374151" />
    <SolidColorBrush x:Key="HoverBrush" Color="#8B5CF6" />
    <SolidColorBrush x:Key="PressedBrush" Color="#4F46E5" />
    <SolidColorBrush x:Key="FocusBrush" Color="#06B6D4" />

    <!-- Window Style -->
    <Style TargetType="{x:Type Window}">
        <Setter Property="Background" Value="{StaticResource BackgroundBrush}" />
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}" />
        <Setter Property="FlowDirection" Value="RightToLeft" />
        <Setter Property="FontFamily" Value="Segoe UI, Tahoma, Arial, Traditional Arabic" />
        <Setter Property="FontSize" Value="14" />
    </Style>

    <!-- Button Styles -->
    <Style x:Key="PrimaryButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="{StaticResource PrimaryBrush}" />
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}" />
        <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="Padding" Value="16,8" />
        <Setter Property="FontWeight" Value="Bold" />
        <Setter Property="FontSize" Value="14" />
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="8"
                            Padding="{TemplateBinding Padding}">
                        <ContentPresenter HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Content="{TemplateBinding Content}" />
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{StaticResource HoverBrush}" />
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="{StaticResource PressedBrush}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Background" Value="{StaticResource TextDisabledBrush}" />
                            <Setter Property="Foreground" Value="{StaticResource TextSecondaryBrush}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SecondaryButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="{StaticResource SecondaryBrush}" />
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}" />
        <Setter Property="BorderBrush" Value="{StaticResource SecondaryBrush}" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="Padding" Value="16,8" />
        <Setter Property="FontWeight" Value="Bold" />
        <Setter Property="FontSize" Value="14" />
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="8"
                            Padding="{TemplateBinding Padding}">
                        <ContentPresenter HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Content="{TemplateBinding Content}" />
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{StaticResource SecondaryLightBrush}" />
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="{StaticResource SecondaryDarkBrush}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Background" Value="{StaticResource TextDisabledBrush}" />
                            <Setter Property="Foreground" Value="{StaticResource TextSecondaryBrush}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- TextBox Style -->
    <Style x:Key="InputTextBoxStyle" TargetType="TextBox">
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}" />
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="Padding" Value="12,8" />
        <Setter Property="FontSize" Value="14" />
        <Setter Property="FlowDirection" Value="RightToLeft" />
        <Setter Property="TextAlignment" Value="Right" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TextBox">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="6"
                            Padding="{TemplateBinding Padding}">
                        <ScrollViewer x:Name="PART_ContentHost"
                                    Focusable="False"
                                    HorizontalScrollBarVisibility="Hidden"
                                    VerticalScrollBarVisibility="Hidden" />
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter Property="BorderBrush" Value="{StaticResource FocusBrush}" />
                            <Setter Property="BorderThickness" Value="2" />
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Card Style -->
    <Style x:Key="CardStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource CardBrush}" />
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="CornerRadius" Value="12" />
        <Setter Property="Padding" Value="20" />
        <Setter Property="Margin" Value="10" />
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="Black" 
                                Direction="315" 
                                ShadowDepth="8" 
                                Opacity="0.5" 
                                BlurRadius="16"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Text Styles -->
    <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
        <Setter Property="Foreground" Value="{StaticResource InfoBrush}" />
        <Setter Property="FontSize" Value="28" />
        <Setter Property="FontWeight" Value="Bold" />
        <Setter Property="TextAlignment" Value="Center" />
        <Setter Property="FlowDirection" Value="RightToLeft" />
        <Setter Property="FontFamily" Value="Segoe UI, Tahoma, Arial, Traditional Arabic" />
    </Style>

    <Style x:Key="SubHeaderTextStyle" TargetType="TextBlock">
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}" />
        <Setter Property="FontSize" Value="18" />
        <Setter Property="FontWeight" Value="SemiBold" />
        <Setter Property="TextAlignment" Value="Center" />
        <Setter Property="FlowDirection" Value="RightToLeft" />
        <Setter Property="FontFamily" Value="Segoe UI, Tahoma, Arial, Traditional Arabic" />
    </Style>

    <Style x:Key="BodyTextStyle" TargetType="TextBlock">
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}" />
        <Setter Property="FontSize" Value="14" />
        <Setter Property="TextAlignment" Value="Right" />
        <Setter Property="FlowDirection" Value="RightToLeft" />
        <Setter Property="FontFamily" Value="Segoe UI, Tahoma, Arial, Traditional Arabic" />
    </Style>

    <!-- Progress Bar Style -->
    <Style x:Key="ProgressBarStyle" TargetType="ProgressBar">
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}" />
        <Setter Property="Foreground" Value="{StaticResource InfoBrush}" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="Height" Value="4" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ProgressBar">
                    <Border Background="{TemplateBinding Background}"
                            CornerRadius="2">
                        <Grid>
                            <Border x:Name="PART_Track"
                                    Background="{TemplateBinding Background}"
                                    CornerRadius="2" />
                            <Border x:Name="PART_Indicator"
                                    Background="{TemplateBinding Foreground}"
                                    HorizontalAlignment="Left"
                                    CornerRadius="2" />
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
